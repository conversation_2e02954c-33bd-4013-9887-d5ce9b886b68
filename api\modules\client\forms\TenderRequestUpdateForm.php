<?php


namespace api\modules\client\forms;


use api\components\BaseRequest;
use api\modules\client\resources\TenderClassifierResource;
use api\modules\client\resources\TenderQualificationSelectionResource;
use api\modules\client\resources\TenderRequestResource;
use api\modules\client\resources\TenderRequestValuesResource;
use api\modules\client\resources\TenderRequirementsAnswerResource;
use api\modules\client\resources\TenderRequirementsResource;
use api\modules\client\resources\TenderResource;
use api\modules\common\resources\FileResource;
use common\enums\CompanyEnum;
use common\enums\CompanyTransactionEnum;
use common\enums\TenderEnum;
use common\models\Company;
use common\models\CompanyTransaction;
use common\models\CurrencyExchange;
use common\models\TenderQualificationFileList;
use Yii;

class TenderRequestUpdateForm extends BaseRequest
{

    public $id;

    public $qualification_files = [];
    public $tech_financ_file_id;
    public $possession_legal_file_id;
    public $taxex_debts_file_id;
    public $bankruptcy_file_id;
    public $dishonest_executors_file_id;
    public $preference_local_producer;
    public $preference_local_producer_file_id;

    public $requirement_files = [];

    public $price_items = []; // tender_classifier_id, classifier_id, price, price_qqs

    public TenderResource $model;
    public TenderRequestResource $request;
    public TenderRequirementsAnswerResource $requirements;

    public function __construct(TenderRequestResource $model, $params = [])
    {
        $this->request = $model;
        $this->model = $this->request->tender;

        parent::__construct($params);
    }

    public function rules()
    {
        $parent = parent::rules();
        $child = [
            [['tech_financ_file_id', 'possession_legal_file_id', 'taxex_debts_file_id', 'bankruptcy_file_id', 'dishonest_executors_file_id', 'preference_local_producer', 'price_items', 'requirement_files', 'qualification_files'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['tech_financ_file_id', 'possession_legal_file_id', 'taxex_debts_file_id', 'bankruptcy_file_id', 'dishonest_executors_file_id', 'preference_local_producer'], 'integer'],
            [['price_items', 'requirement_files', 'qualification_files'], 'safe'],
            [['tech_financ_file_id'], 'exist', 'skipOnError' => true, 'targetClass' => FileResource::class, 'targetAttribute' => ['tech_financ_file_id' => 'id']],
            [['possession_legal_file_id'], 'exist', 'skipOnError' => true, 'targetClass' => FileResource::class, 'targetAttribute' => ['possession_legal_file_id' => 'id']],
            [['taxex_debts_file_id'], 'exist', 'skipOnError' => true, 'targetClass' => FileResource::class, 'targetAttribute' => ['taxex_debts_file_id' => 'id']],
            [['bankruptcy_file_id'], 'exist', 'skipOnError' => true, 'targetClass' => FileResource::class, 'targetAttribute' => ['bankruptcy_file_id' => 'id']],
            [['dishonest_executors_file_id'], 'exist', 'skipOnError' => true, 'targetClass' => FileResource::class, 'targetAttribute' => ['dishonest_executors_file_id' => 'id']],
            [['preference_local_producer_file_id'], 'exist', 'skipOnError' => true, 'targetClass' => FileResource::class, 'targetAttribute' => ['preference_local_producer_file_id' => 'id']],
        ];
        return array_merge($parent, $child);
    }


    public function getResult()
    {
        date_default_timezone_set("Asia/Tashkent");

        if ($this->model->state == TenderEnum::STATE_READY && strtotime($this->model->end_date) > time()) {


            if (count($this->model->tenderRequirements) != count($this->requirement_files)) {
                $this->addError('requirement_files', t("Barcha talablar uchun tasdiqlovchi fayllar yuklanmagan"));

                return false;
            }

            if (count($this->model->tenderClassifiers) != count($this->price_items)) {
                $this->addError('price_items', t("Barcha tovarlar uchun narx taklif qilinmagan"));
                return false;
            }

            $qualificationList = $this->model->tenderQualificationList;
            if ($qualificationList && count($qualificationList) > 0) {
                if (count($this->qualification_files) != count($qualificationList)) {
                    $this->addError('price_items', t("Malaka tanlovi qo'shimcha shartlar uchun fayl yuklanmagan"));
                    return false;
                }
            }

            /**
             * @var $company Company
             */

            $user = Yii::$app->user->identity;
            $companyId = $user->company_id;
            $company = $user->company;

            $transaction = \Yii::$app->db->beginTransaction();

            $this->request->preference_local_producer = $this->preference_local_producer;
            if ($this->preference_local_producer_file_id != null) {
                $this->request->preference_local_producer_file_id = $this->preference_local_producer_file_id;
            }

            $price = 0;
            $price_qqs = 0;
            if ($this->request->save()) {

                TenderQualificationSelectionResource::updateAll(
                    ['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s"), 'updated_by' => \Yii::$app->user->id],
                    ['tender_request_id' => $this->request->id, 'company_id' => $companyId, 'tender_id' => $this->request->tender_id, 'status' => TenderEnum::STATUS_ACTIVE]
                );

                $resources = [
                    [
                        'file_id' => $this->tech_financ_file_id,
                        'title' => t("Shartnomani bajarish uchun zarur texnik, moliyaviy, moddiy, kadrlar resurlarining hamda boshqa resurlarning mavjudligi"),
                    ],
                    [
                        'file_id' => $this->possession_legal_file_id,
                        'title' => t("Shartnoma tuzish uchun qonuniy huquqqa egaligi"),
                    ],
                    [
                        'file_id' => $this->taxex_debts_file_id,
                        'title' => t("Soliqlar va yig’imlarni to’lash bo’yicha muddati o’tgan qarzdorlikning mavjud emasligi"),
                    ],
                    [
                        'file_id' => $this->bankruptcy_file_id,
                        'title' => t("O’ziga nisbatan joriy etilgan bankrotlik tartib-tamoillarining mavjud emasligi"),
                    ],
                    [
                        'file_id' => $this->dishonest_executors_file_id,
                        'title' => t("Insofsiz ijrochilarning yagona reestrida qayd etilmaganligi"),
                    ],
                ];

                foreach ($resources as $resource) {
                    $model = new TenderQualificationSelectionResource();
                    $model->tender_request_id = $this->request->id;
                    $model->company_id = $companyId;
                    $model->tender_id = $this->model->id;
                    $model->file_id = $resource['file_id'];
                    $model->title = $resource['title'];
                    $model->status = TenderEnum::STATUS_ACTIVE;

                    if (!$model->save()) {
                        $this->addErrors($model->errors);
                        $transaction->rollBack();
                        return false;
                    }
                }


                $price = 0;
                $price_qqs = 0;
                $currency = null;

                foreach ($this->price_items as $value) {
                    if (!isset($value['price']) || $value['price'] == null) {
                        $this->addError('price_items', t("price yuborilmagan"));

                        $transaction->rollBack();
                        return false;
                    }

                    if (!isset($value['price_qqs']) || $value['price_qqs'] == null) {
                        $this->addError('price_items', t("price_qqs yuborilmagan"));

                        $transaction->rollBack();
                        return false;
                    }

                    if (!isset($value['classifier_id']) || $value['classifier_id'] == null) {
                        $this->addError('price_items', t("classifier_id yuborilmagan"));

                        $transaction->rollBack();
                        return false;
                    }

                    if (!isset($value['country_id']) || $value['country_id'] == null) {
                        $this->addError('price_items', t("country_id yuborilmagan"));

                        $transaction->rollBack();
                        return false;
                    }

                    $classifierCheck = TenderClassifierResource::find()->notDeleted()
                        ->andWhere(['tender_id' => $this->model->id])
                        ->andWhere(['classifier_id' => $value['classifier_id']])
                        ->andWhere(['id' => $value['tender_classifier_id']])
                        ->one();
                    if (!$classifierCheck) {
                        $this->addError('price_items', t("Yuborilgan tovar id si mos emas"));

                        $transaction->rollBack();
                        return false;
                    }

                    if ($classifierCheck->price < ($value['price'] + $value['price_qqs']) * 100) {
                        $this->addError('price', t("Taklif narxi boshlang'ichdan katta bo'lmasligi kerak"));
                        $transaction->rollBack();

                        return false;
                    }

                    $classifierCheck = TenderRequestValuesResource::find()->notDeleted()
                        ->andWhere(['tender_id' => $this->model->id])
                        ->andWhere(['classifier_id' => $value['classifier_id']])
                        ->andWhere(['id' => $value['tender_request_values_id']])
                        ->one();
                    if (!$classifierCheck) {
                        $this->addError('price_items', t("Yuborilgan tovar id si mos emas"));
                        $transaction->rollBack();

                        return false;
                    }

                    $classifierCheck->status = TenderEnum::STATUS_DELETED;
                    $classifierCheck->deleted_at = date("Y-m-d H:i:s");
                    if (!$classifierCheck->save()) {
                        $this->addErrors($classifierCheck->errors);
                        $transaction->rollBack();
                        return false;
                    }

                    $items = new TenderRequestValuesResource();

                    $items->tender_id = $this->model->id;
                    $items->tender_request_id = $this->request->id;

                    $items->tender_classifier_id = $classifierCheck->tender_classifier_id;
                    $items->classifier_id = $classifierCheck->classifier_id;
                    $items->price = $value['price'] * 100;
                    $items->price_qqs = $value['price_qqs'] * 100;
                    $items->country_id = $value['country_id'];
                    $items->purchase_currency = $value['purchase_currency'];
                    $items->status = TenderEnum::STATUS_ACTIVE;

                    if (!$items->save()) {
                        $this->addErrors($items->errors);
                        $transaction->rollBack();
                        return false;
                    }
                    $price += $items->price;
                    $price_qqs += $items->price_qqs;
                    $currency = $value['purchase_currency'];

                }

                if (!in_array($currency, TenderEnum::PURCHASE_CURRENCY_LIST)) {
                    $this->addError("error", "Valyuta noto'g'ri qiymat yuborildi");

                    $transaction->rollBack();
                    return false;
                }

                if ($company->resident == CompanyEnum::NO_RESIDENT) {

                    $code = TenderEnum::PURCHASE_CURRENCY_LIST_CODE;
                    $code = $code[$currency];

                    $kurs = CurrencyExchange::findOne(['created_date' => date("y-m-d"), 'code' => $code]);
                    if (!$kurs) {
                        $kurs = CurrencyExchange::findOne(['created_date' => date("y-m-d", strtotime("-1 days")), 'code' => $code]);
                    }
                    if ($kurs) {

                        if (($price + $price_qqs) / $kurs->value > $this->model->getTenderTotalPrice()) {
                            $this->addError("error", "Boshlang'ich narxdan yuqori narx berish mumkin emas");

                            $transaction->rollBack();
                            return false;
                        }

                    }

                }

                $this->request->price = $price;
                $this->request->price_qqs = $price_qqs;
                if (!$this->request->save()) {
                    $this->addErrors($this->request->errors);
                    $transaction->rollBack();
                    return false;
                }


                $company_transaction_commissions = CompanyTransaction::find()->where([
                    'company_id' => $company->id,
                    'reverted_id' => null,
                    'tender_id' => $this->model->id,
                    'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION,
                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                ])->one();

                if ($company_transaction_commissions) {
                    $revertCommission = new CompanyTransaction([
                        'company_id' => $company->id,
                        'tender_id' => $company_transaction_commissions->tender_id,
                        'amount' => $company_transaction_commissions->amount,
                        'type' => CompanyTransactionEnum::TYPE_REVERT_BLOCK_COMMISION,
                        'description' => \Yii::t("main", "Tenderga taklif uchun bandlangan kommissiya qaytarildi"),
                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                        'transaction_date' => date("Y-m-d H:i:s"),
                    ]);

                    if (!$revertCommission->save()) {
                        $this->addError("error", $revertCommission->errors);
                        $transaction->rollBack();
                        return false;
                    } else {
                        $company_transaction_commissions->reverted_id = $revertCommission->id;
                        if (!$company_transaction_commissions->save()) {
                            $this->addError("error", $company_transaction_commissions->errors);
                            $transaction->rollBack();
                            return false;
                        }
                    }
                }

                $company_transactions_for_back = CompanyTransaction::find()
                    ->where([
                        'type' => CompanyTransactionEnum::TYPE_ZALOG,
                        'company_id' => $company->id,
                        'reverted_id' => null,
                        'tender_id' => $this->model->id,
                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                    ])
                    ->all();

                //return $company_transactions_for_back;

                foreach ($company_transactions_for_back as $c_for_back) {
                    $company_transaction_back = new CompanyTransaction([
                        'company_id' => $c_for_back->company_id,
                        'amount' => $c_for_back->amount,
                        'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
                        'tender_id' => $c_for_back->tender_id,
                        'description' => t("Tender ni oldingi narx taklifi garovlari qaytarildi"),
                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                        'transaction_date' => date("Y-m-d H:i:s")
                    ]);
                    if ($company_transaction_back->save()) {
                        $c_for_back->reverted_id = $company_transaction_back->id;
                        if (!$c_for_back->save()) {
                            $transaction->rollBack();
                            $this->addError("error", t("Balansingizdan pul yechib bo‘lmadi."));
                            return false;
                        }
                    } else {
                        $this->addError("error", $company_transaction_back->errors);
                        $transaction->rollBack();
                        return false;
                    }
                }


                $zalogPercent = $this->model->amount_deposit / 100;
                // 0.15 foiz vositachilik yigʻimi zakazchikdan lekin 10 ming soʻmdan oshmaydi. Zalog yoʻq zakazchikdan
                $totalPrice = $this->model->tenderTotalPrice;
                $commission_sum = ($price + $price_qqs) * env('COMMISSION_PERCENT', 0.0015);
                $zalog_sum = (int)$totalPrice * $zalogPercent;
                $total_block_sum = $zalog_sum + ($commission_sum >= env('TENDER_SUPPLIER_MAX_COMMISSION_SUM', 4000000) ? env('TENDER_SUPPLIER_MAX_COMMISSION_SUM', 4000000) : $commission_sum);


                if (!hasMoney($company , $total_block_sum)) {
                    $transaction->rollBack();
                    $this->addError("price", t("Balansda mablag' yetarli emas"));
                    return false;
                }

                $company_transaction_commission = new CompanyTransaction([
                    'company_id' => $company->id,
                    'contract_id' => null,
                    'tender_id' => $this->model->id,
                    'amount' => $commission_sum,
                    'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION,
                    'description' => \Yii::t("main", "Tenderga taklif uchun komissiya bandlandi"),
                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                    'transaction_date' => date("Y-m-d H:i:s"),
                ]);

                if (!$company_transaction_commission->save()) {
                    $this->addError("price", $company_transaction_commission->errors);
                    $transaction->rollBack();
                    return false;
                }

                $company_transaction_zalog = new CompanyTransaction([
                    'company_id' => $company->id,
                    'contract_id' => null,
                    'tender_id' => $this->model->id,
                    'amount' => $zalog_sum,
                    'type' => CompanyTransactionEnum::TYPE_ZALOG,
                    'description' => \Yii::t("main", "Tenderga taklif uchun garov bandlandi"),
                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                    'transaction_date' => date("Y-m-d H:i:s"),
                ]);

                if (!$company_transaction_zalog->save()) {
                    $this->addError("price", $company_transaction_zalog->errors);
                    $transaction->rollBack();
                    return false;
                }

                foreach ($this->qualification_files as $item) {
                    if (!isset($item['id']) || $item['id'] == null) {
                        $this->addError('id', t("Malaka tanlov qo'shimcha shart idsi yuborilmagan"));
                        $transaction->rollBack();
                        return false;
                    }
                    if (!isset($item['qualification_title']) || $item['qualification_title'] == null) {
                        $this->addError('qualification_title', t("Malaka tanlov qo'shimcha shart idsi yuborilmagan"));
                        $transaction->rollBack();
                        return false;
                    }
                    if (!isset($item['qualification_file_id']) || $item['qualification_file_id'] == null) {
                        $this->addError('qualification_file_id', t("Malaka tanlov qo'shimcha yuklangan file id yuborilmagan"));
                        $transaction->rollBack();
                        return false;
                    }
                    $file = FileResource::findOne($item['qualification_file_id']);
                    if (!$file) {
                        $this->addError('qualification_files', t("Malaka tanlov qo'shimcha yuklangan fayl topilmadi"));
                        $transaction->rollBack();
                        return false;
                    }

//                    $req = TenderQualificationSelectionResource::find()
//                        ->where(['tender_request_id' => $this->request->id])
//                        ->andWhere(['tender_id' => $this->model->id])
//                        ->andWhere(['company_id' => $companyId])
//                        ->andWhere(['id' => $item['id']])
//                        ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
//                        ->one();
                    $req = TenderQualificationSelectionResource::findOne(['id' => $item['id'], 'tender_request_id' => $this->request->id]);

                    if (!$req) {
                        $this->addError('id', t("Yuborilgan talabga javob id si mos emas"));
                        $transaction->rollBack();
                        return false;
                    }
                    $req->status = TenderEnum::STATUS_DELETED;
                    $req->deleted_at = date("Y-m-d H:i:s");
                    if (!$req->save()) {
                        $transaction->rollBack();
                        $this->addErrors($req->errors);
                        return false;
                    }

                    $qualificationListItem = TenderQualificationFileList::findOne(['title' => $item['qualification_title'], 'tender_id' => $this->model->id, 'status' => TenderEnum::STATUS_ACTIVE]);
                    if (!$qualificationListItem) {
                        $transaction->rollBack();
                        $this->addError("error", t("Malaka tanlovi topilmadi"));
                        return false;
                    }

                    $qSelectionFile = new TenderQualificationSelectionResource();
                    $qSelectionFile->tender_request_id = $this->request->id;
                    $qSelectionFile->company_id = $companyId;
                    $qSelectionFile->tender_id = $this->model->id;
                    $qSelectionFile->file_id = $file->id;
                    $qSelectionFile->title = $qualificationListItem->title;
                    $qSelectionFile->status = TenderEnum::STATUS_ACTIVE;
                    $qSelectionFile->state = TenderEnum::QUALIFIER_STATE_NEW;
                    if (!$qSelectionFile->save()) {
                        $this->addErrors($qSelectionFile->errors);
                        $transaction->rollBack();
                        return false;
                    }

                }

                TenderRequirementsAnswerResource::updateAll(
                    [
                        'status' => TenderEnum::STATUS_DELETED,
                        'deleted_at' => date("Y-m-d H:i:s"),
                    ],
                    [
                        'status' => TenderEnum::STATUS_ACTIVE,
                        'company_id' => $company->id,
                        'tender_request_id' => $this->request->id,
                        'deleted_at' => null
                    ]
                );
                foreach ($this->requirement_files as $value) {

                    $req = TenderRequirementsResource::find()->notDeleted()
                        ->andWhere(['tender_id' => $this->model->id])
                        ->andWhere(['id' => $value['tender_requirements_id']])
                        ->one();
                    if (!$req) {
                        $this->addError('tender_requirements_id', t("Yuborilgan talab id si mos emas"));
                        $transaction->rollBack();

                        return false;
                    }

                    if ($req->file_uploading_necessary && $req->file_is_required == 1) {
                        if (!isset($value['file_id']) || $value['file_id'] == null) {
                            $this->addError('file_id', t("file_id yuborilmagan"));
                            $transaction->rollBack();
                            return false;
                        } else {
                            if (!FileResource::find()->where(['id' => $value['file_id']])->exists()) {
                                $this->addError('file_id', t("Yuborilgan fayl idsi mavjud emas"));
                                $transaction->rollBack();
                                return false;
                            }

                        }
                    }

                    if ($req->type == TenderEnum::REQUIREMENT_TYPE_TEXT && $req->obligation == 1) {
                        if (!isset($value['value']) || $value['value'] == null) {
                            $this->addError('value', t("value yuborilmagan"));
                            $transaction->rollBack();
                            return false;
                        }
                    } elseif ($req->type == TenderEnum::REQUIREMENT_TYPE_NUMBER) {
                        if ($req->obligation == 1) {
                            if (!isset($value['value']) || $value['value'] == null) {
                                $this->addError('value', t("value yuborilmagan"));
                                $transaction->rollBack();
                                return false;
                            } elseif (!is_numeric($value['value'])) {
                                $this->addError('value', t("value qiymati son bo'lishi kerak"));
                                $transaction->rollBack();
                                return false;
                            }

                            if (in_array($req->value_condition, [4, 5])) {
                                if ($value['value'] < $req->value_from || $value['value'] > $req->value_to) {
                                    $this->addError('value', t("value qiymati berilgan oraliqda yuborilmagan"));
                                }
                            } elseif ($req->value_condition == 1) {
                                if ($value['value'] != $req->value) {
                                    $this->addError('value', t("value qiymati berilgan qiymatga teng yuborilmagan"));
                                }
                            } elseif ($req->value_condition == 2) {
                                if ($value['value'] > $req->value) {
                                    $this->addError('value', t("value qiymati berilgan qiymatdan katta yuborilgan"));
                                }
                            } else {
                                if ($value['value'] < $req->value) {
                                    $this->addError('value', t("value qiymati berilgan qiymatdan kichik yuborilgan"));
                                }
                            }

                        }

                    } else {
                        if ($req->obligation == 1) {
                            if (!isset($value['value']) || $value['value'] == null) {
                                $this->addError('value', t("value yuborilmagan"));
                                $transaction->rollBack();
                                return false;
                            } elseif (!in_array($value['value'], [0, 1])) {
                                $this->addError('value', t("value qiymati to'g'ri yuborilmagan [0,1]"));
                                $transaction->rollBack();
                                return false;
                            }
                        }
                    }


                    $items = new TenderRequirementsAnswerResource();

                    $items->tender_id = $this->model->id;
                    $items->tender_request_id = $this->request->id;
                    $items->company_id = $companyId;

                    $items->tender_requirements_id = $req->id;

                    $items->file_id = isset($value['file_id']) && $value['file_id'] ? $value['file_id'] : null;
                    $items->value = isset($value['value']) && $value['value'] ? $value['value'] : null;
                    $items->title = $req->title;
                    $items->status = TenderEnum::STATUS_ACTIVE;

                    if (!$items->save()) {
                        $this->addErrors($items->errors);
                        $transaction->rollBack();
                        return false;
                    }

                }

                $transaction->commit();
                return true;
            } else {
                $this->addErrors($this->request->errors);
            }
        }

        $this->addError('error', t("Tender vaqti tugadi"));

        return false;


    }
}