<?php

namespace api\modules\backend\controllers;

use api\components\ApiController;
use api\modules\backend\filters\TenderHistoryFilter;
use api\modules\backend\forms\TenderAcceptForm;
use api\modules\backend\forms\TenderRejectForm;
use api\modules\backend\resources\TenderResource;
use api\modules\backend\filters\TenderFilter;
use common\enums\TenderEnum;
use Yii;
use yii\filters\AccessControl;
use yii\web\NotFoundHttpException;

class TenderController extends ApiController
{
  //    public function behaviors()
  //    {
  //        return parent::behaviors() + [
  //              'access' => [
  //                'class' => AccessControl::class,
  //                'rules' => [
  //                   /*commission*/
  //                    [
  //                        'controllers' => [
  //                            'tender',
  //                        ],
  //                        'actions' => ['index'],
  //                        'allow' => true,
  //                        'roles' => ['moderator'],
  //                    ],
  //                ], // rules
  //
  //              ], // access
  //            ];
  //    }

  public function actionIndex()
  {
    return $this->sendResponse(
      new TenderFilter(),
      Yii::$app->request->queryParams
    );
  }

  public function actionHistory()
  {
    return $this->sendResponse(
      new TenderHistoryFilter(),
      Yii::$app->request->queryParams
    );
  }

  public function actionView($id)
  {
    return $this->sendModel($this->findModel($id));
  }

  public function actionAccept($id)
  {
    return $this->sendResponse(
      new TenderAcceptForm($this->findOne($id)),
      Yii::$app->request->bodyParams
    );
  }

  public function actionReject($id)
  {
    return $this->sendResponse(
      new TenderRejectForm($this->findOne($id)),
      Yii::$app->request->bodyParams
    );
  }

  private function findOne($id)
  {
    $model = TenderResource::find()->where(['id' => $id, 'state' => TenderEnum::STATE_NEW])->one();

    if (!$model) throw new NotFoundHttpException("Tender is not found");

    return $model;
  }
  private function findModel($id)
  {
    $model = TenderResource::find()->join('left join', 'tender_moderator_log', 'tender_moderator_log.tender_id=tender.id')
      ->where('tender.id=' . $id)
      ->andWhere([
        'or',
        ['tender_moderator_log.created_by' => \Yii::$app->user->id],
        ['tender.state' => TenderEnum::STATE_NEW]
      ])->one();

    if (!$model) throw new NotFoundHttpException("Tender is not found");

    return $model;
  }
}
