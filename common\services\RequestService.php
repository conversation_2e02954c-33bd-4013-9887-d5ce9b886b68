<?php

namespace common\services;

use yii\base\Component;
use yii\base\InvalidConfigException;
use yii\httpclient\Client;

class RequestService extends Component
{
    private string $baseUrl;
    private string $requestMethod = "GET";
    private string $endPoint = '';

    private ?string $username = null;
    private ?string $password = null;
    private ?string $token = null;
    private array $params = [];

    private string $language = 'ru';

    private array $headers = [];
    const HTTP_CLIENT_TIMEOUT = 5;

    public function setBaseUrl(string $baseUrl): static
    {
        $this->baseUrl = $baseUrl;
        return $this;
    }

    public function setRequestMethod(string $method): static
    {
        $this->requestMethod = $method;
        return $this;
    }

    public function setEndPoint(string $endPoint): static
    {
        $this->endPoint = $endPoint;
        return $this;
    }

    public function setUsername(string $username): static
    {
        $this->username = $username;
        return $this;
    }

    public function setPassword(string $password): static
    {
        $this->password = $password;
        return $this;
    }

    public function setToken(string $token): static
    {
        $this->token = $token;
        return $this;
    }

    public function setParam(array $params): static
    {
        $this->params = $params;
        return $this;
    }

    public function setHeaders(array $headers): static
    {
        $this->headers = $headers;
        return $this;
    }

    public function setLanguage(string $language): static
    {
        $this->language = $language;
        return $this;
    }

    private function prepareUrl(): string
    {
        $url = $this->baseUrl . $this->endPoint;
        if ($this->requestMethod === 'GET')
        {
            $url .= '?' . http_build_query($this->params);
        }
        return $url;
    }

    /**
     * @throws InvalidConfigException
     */
    public function send()
    {
        $client = new Client(['baseUrl' => $this->prepareUrl()]);
        $request = $client->createRequest()
            ->setOptions(['timeout' => self::HTTP_CLIENT_TIMEOUT])
            ->addHeaders(['Accept' => 'application/json'])
            ->addHeaders(['Content-type' => 'application/json'])
            ->addHeaders(['Accept-Language' => $this->language]);

        if ($this->requestMethod === 'POST')
        {
            $request->setMethod('POST')
                ->setData($this->params)
                ->setContent(json_encode($this->params));
        }

        if ($this->token != null) {
            $request->addHeaders(['Authorization' => 'Bearer ' . $this->token]);
        } else if ($this->username != null && $this->password != null) {
            $request->addHeaders(['Authorization' => 'Basic ' . base64_encode($this->username . ':' . $this->password)]);
        }

        foreach ($this->headers ?? [] as $header)
        {
            $request->addHeaders($header);
        }

        try {
            $response = $request->send();
            if ($response->isOk)
                return $response->data;
            debuggerTelegram('Request service response',$request,['message' => $response->content]);
        } catch (\Throwable $exception) {
            $message = $exception->getMessage();
            $message .= "Code: " . $exception->getCode();
            $message .= "\nFile: " . $exception->getFile();
            $message .= "\nLine: " . $exception->getLine();
            debuggerTelegram('Request service exception',$request,['message' => $message]);
        }
        return null;
    }


}