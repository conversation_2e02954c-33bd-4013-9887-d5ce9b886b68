<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\shop\resources\OrderResource;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;
use common\enums\ShopEnum;
use common\models\shop\Order;
use Yii;
use yii\data\ActiveDataProvider;

class CancelOrderListFilter extends BaseRequest
{
    public $classifier_category_id;
    public $classifier_id;
    public $plan_schedule_id;
    public $company_id;
    public $product_id;
    public $status;
    public $title;
    public $user_id;
    public $pageNo = 0;
    public $pageSize = 10;
    public function rules()
    {
        return [
            [['pageNo', 'pageSize'], 'integer'],
            [['plan_schedule_id','classifier_id','company_id','product_id','status','user_id','title'], 'safe']
        ];
    }

    public function getResult()
    {
        $model = OrderResource::find();

        $model->andWhere(['user_id' => Yii::$app->user->identity->id]);
        $model->andWhere(['in','status' ,[ShopEnum::ORDER_STATUS_CANCEL]]);

        return paginate($model);

    }
}
