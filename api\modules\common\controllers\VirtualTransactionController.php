<?php

namespace api\modules\common\controllers;

use api\modules\common\filters\TransactionImportFilter;
use api\components\ApiController;
use Exception;
use yii\filters\auth\HttpBearerAuth;

class VirtualTransactionController extends ApiController
{
    public function behaviors()
    {
        $parent = parent::behaviors();
        $parent['bearerAuth'] = [
            'class' => HttpBearerAuth::class,
            'except' => ['export'], // vaqtincha keyinchalik chopiladi.
        ];
        return $parent;
    }

    /**
     * @throws Exception
     */
    public function actionExport()
    {
        return (new TransactionImportFilter())->getResult();
    }
}