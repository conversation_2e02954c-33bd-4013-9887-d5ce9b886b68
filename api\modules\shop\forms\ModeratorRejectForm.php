<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;
use common\models\CompanyBalance;
use common\models\TenderModeratorLog;
use Yii;
use yii\base\Exception;
use function foo\func;

class ModeratorRejectForm extends BaseRequest
{

    public ProductResource $model;

    public $state;
    public $description;
    public $moderator_pinfl;


    public function __construct(ProductResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [ ['description'
            ], 'required', 'message' => t('{attribute} yuborish majburiy')],

        ];
    }



    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();


        throw new Exception("Tasdiqlash moderator adminkasi orqali");

        $this->model->state = ProductEnum::SHOP_STATE_RETURN_MODERATOR;
        if ($this->model->save()){
            //Oldingi moderator qaytarganlarini ocirish
//            TenderModeratorLog::deleteAll(['product_id'=>$this->model->id]);

            $moderatorLog = new  TenderModeratorLog();
            $moderatorLog->product_id = $this->model->id;
            $moderatorLog->description = $this->description;
            $moderatorLog->moderator_pinfl = $this->moderator_pinfl;
            $moderatorLog->state = $this->state;
            if(!($moderatorLog->validate() && $moderatorLog->save())){
                $transaction->rollBack();
                $this->addError('product_id', $moderatorLog->errors);
                return false;
            }
            $transaction->commit();
            return true;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}