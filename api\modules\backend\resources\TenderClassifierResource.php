<?php


namespace api\modules\backend\resources;


use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderClassifier;

class TenderClassifierResource extends TenderClassifier
{

    public function fields(){
        return [
            'id',
            'number_purchased',
            'additional_charges_non_residents',
            'additional_charges_amount',
            'price',
            'unit_id',
            'expiry_date_value',
            'expiry_date_unit',
            'delivery_period',
            'description',
        ];
    }

    public function extraFields (){
        return [
            'classifier',
            'planScheduleClassifier',
            'tender',
        ];
    }

    public function getClassifier()
    {
        return $this->hasOne(Classifier::class, ['id' => 'classifier_id']);
    }

    /**
     * Gets query for [[PlanScheduleClassifier]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getPlanScheduleClassifier()
    {
        return $this->hasOne(PlanScheduleClassifierResource::class, ['id' => 'plan_schedule_classifier_id']);
    }

    /**
     * Gets query for [[Tender]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTender()
    {
        return $this->hasOne(TenderResource::class, ['id' => 'tender_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

    public static function findOne($id)
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

}