<?php


namespace api\modules\shop\forms;


use api\components\BaseModel;
use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\shop\resources\ProductResource;
use common\enums\CompanyEnum;
use common\enums\ProductEnum;
use common\models\Company;
use common\models\TenderModeratorLog;
use Yii;
use function foo\func;

class OrderPlaceForm extends BaseModel
{

    public ProductResource $model;

    public $state;
    public $description;
    public $moderator_pinfl;
    public $pkcs7;

    public function __construct(ProductResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        $parent = parent::rules();
        $child =  [
            [ ['description','state'], 'required', 'message' => t('{attribute} yuborish majburiy')],

        ];
        return  array_merge($parent,$child);
    }


    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();

        $company = \Yii::$app->user->identity->company;
        // 3% zalog + 0.15% komissiya

        $totalPrice = $this->model->tenderTotalPrice;
        $zalog_percent = $this->model->amount_deposit / 100;

        $zalog_sum = $totalPrice * $zalog_percent;
        $commission_sum = $totalPrice * env('COMMISSION_PERCENT', 0.0015);
        $total_block_sum = $zalog_sum + ($commission_sum > env('TENDER_SUPPLIER_MAX_COMMISSION_SUM', 4000000) ? env('TENDER_SUPPLIER_MAX_COMMISSION_SUM', 4000000) : $commission_sum);

        if ($company->availableBalance < $total_block_sum) {
            $this->addError("days", t("Balansda yetarli mablag' mavjud emas"));
            return false;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}