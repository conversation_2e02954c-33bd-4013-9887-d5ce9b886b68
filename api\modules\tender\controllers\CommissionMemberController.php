<?php

namespace api\modules\tender\controllers;

use api\components\ApiController;
use api\modules\tender\filters\CommissionMemberByGroupIdFilter;
use api\modules\tender\filters\CommissionMemberByTenderIdFilter;
use api\modules\tender\filters\CommissionMemberFilter;
use api\modules\tender\filters\CommissionMemberNotByTenderIdFilter;
use api\modules\tender\forms\CommissionMemberActiveForm;
use api\modules\tender\forms\CommissionMemberForm;
use api\modules\tender\forms\CommissionGroupMemberEmailForm;
use api\modules\tender\forms\CommissionMemberDeleteForm;
use api\modules\tender\forms\CommissionGroupMemberDeleteForm;
use api\modules\tender\forms\CommissionMemberUpdateForm;
use api\modules\tender\forms\MemberRemoveAllGroupDeleteForm;
use api\modules\tender\resources\CommissionMemberResource;
use common\enums\TenderEnum;
use Yii;
use yii\web\NotFoundHttpException;

/**
 * Group controller for the `tender` module
 */
class CommissionMemberController extends ApiController
{
    public function actionIndex()
    {
        return $this->sendResponse(
            new CommissionMemberFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionListByGroupId($groupId)
    {
        return $this->sendResponse(
            new CommissionMemberByGroupIdFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionView($id)
    {
        return $this->sendModel($this->findOne($id));
    }

    /*status yuborsa bo'ldi aktivlari, bo'lmasa hammasi*/
    public function actionListByTenderId($tenderId, $status = null)
    {
        return $this->sendResponse(
            new CommissionMemberByTenderIdFilter(),
            Yii::$app->request->queryParams
        );
    }

    /*tenderga qoshilmagan memberlar aktivlari*/
    public function actionListNotByTenderId($id)
    {
        return $this->sendResponse(
            new CommissionMemberNotByTenderIdFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionCreate()
    {
        return $this->sendResponse(
            new CommissionMemberForm(new CommissionMemberResource()),
            Yii::$app->request->bodyParams
        );
    }

    public function actionSendInviting($id)
    {
        return $this->sendResponse(
            new CommissionGroupMemberEmailForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }


    public function actionUpdate($id)
    {
        return $this->sendResponse(
            new CommissionMemberUpdateForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionDeleteFromGroups()
    {
        return $this->sendResponse(
            new MemberRemoveAllGroupDeleteForm(),
            Yii::$app->request->bodyParams
        );
    }

    public function actionDelete($id)
    {
        return $this->sendResponse(
            new CommissionMemberDeleteForm($this->findOne($id)),
            Yii::$app->request->queryParams
        );
    }

    public function actionSetActive($id)
    {
        return $this->sendResponse(
            new CommissionMemberActiveForm($this->findDeletedOne($id)),
            Yii::$app->request->queryParams
        );
    }

    private function findOne($id)
    {
        $model = CommissionMemberResource::find()->where(['id' => $id, 'company_id' => Yii::$app->user->identity->company_id])->one(); //, 'status' => StatusEnum::STATUS_ACTIVE

        if (!$model) throw new NotFoundHttpException("Member not found");

        return $model;
    }

    private function findDeletedOne($id)
    {
        $model = CommissionMemberResource::find()->where(['id' => $id, 'status' => TenderEnum::STATUS_DELETED, 'company_id' => Yii::$app->user->identity->company_id])->one(); //, 'status' => StatusEnum::STATUS_ACTIVE

        if (!$model) throw new NotFoundHttpException("Member not found");

        return $model;
    }
}
