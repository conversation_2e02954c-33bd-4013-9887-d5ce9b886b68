<?php

namespace api\modules\auction\filters;

use api\components\BaseRequest;
use api\modules\auction\resources\AuctionResource;
use common\enums\AuctionEnum;
use Yii;

class AuctionFilter extends BaseRequest
{
    public $lot;
    public $status;

    public function rules()
    {
        // rules regarding all props from /common/migrations/db/m231115_193117_create_auction_table.php
        return [
            [['lot', 'status'], 'integer', 'message' => t("{attribute} qiymati son bo'lishi kerak")]
        ];
    }

    public function attributeLabels()
    {
        return [
            'log' => t("Lot"),
            'status' => t("Status"),
        ];
    }

    public function getResult()
    {
        $user = Yii::$app->user->identity;

        $query = AuctionResource::find()->where(['company_id' => $user->company_id]);

        if ($user->isBudget) {
            $query->andWhere(['organ' => $user->organ]);
        }

        if ($this->lot) {
            $query->andWhere(['lot' => $this->lot]);
        }
        if ($this->status) {
            $query->andWhere(['status' => $this->status]);
        }
        $query = $query->orderBy("auction.id desc");

        // if ($params['name']) {
        //   $query->andWhere([
        //     'or',
        //     ['auction.id' => $params['name']],
        //     ['like', 'category_translate.title', $params['name']]
        //   ]);
        // }

        // if ($params['summa_from']) {
        //   $query->andWhere(['>=', 'auction.total_sum', $params['summa_from']]);
        // }

        // if ($params['summa_to']) {
        //   $query->andWhere(['<=', 'auction.total_sum', $params['summa_to']]);
        // }

        // if ($params['region_id']) {
        //   $query->andWhere(['auction.region_id' => $params['region_id']]);
        // }

        // if ($params['district_id']) {
        //   $query->andWhere(['auction.region_id' => $params['district_id']]);
        // }

        // if ($params['customer_tin']) {
        //   $query->andWhere(['company.tin' => $params['customer_tin']]);
        // }

        // if ($params['classifier_category_id']) {
        //   $query->andWhere(['auction.classifier_category_id' => $params['classifier_category_id']]);
        // }

        // if ($params['start_date']) {
        //   $query->andWhere(['>=', 'DATE(auction.auction_end)', $params['start_date']]);
        // }

        // if ($params['end_date']) {
        //   $query->andWhere(['<=', 'DATE(auction.auction_end)', $params['end_date']]);
        // }

        return paginate($query);
    }
}
