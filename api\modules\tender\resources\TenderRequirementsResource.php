<?php


namespace api\modules\tender\resources;


use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderRequirements;

class TenderRequirementsResource extends TenderRequirements
{

    public function fields()
    {
        return [
            'type', 'evaluation_method', 'obligation', 'file_name',
            'max_ball', 'min_ball', 'comparative_weight', 'title', 'description',
            'file_uploading_necessary', 'file_is_required', 'value', 'value_binary', 'value_condition', 'unit', 'value_from', 'value_to'
        ];
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

}