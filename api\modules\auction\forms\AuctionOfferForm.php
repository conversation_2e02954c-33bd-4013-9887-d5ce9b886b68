<?php

namespace api\modules\auction\forms;

use api\components\BaseModel;
use api\modules\auction\resources\AuctionActiveResource;
use api\modules\auction\resources\AuctionClassifierShortResource;
use api\modules\common\resources\VirtualTransactionResource;
use common\enums\CompanyTransactionEnum;
use common\enums\OperationTypeEnum;
use common\enums\UserEnum;
use common\models\auction\AuctionClassifier;
use common\models\auction\AuctionOffer;
use common\models\auction\AuctionRequestClassifierCountry;
use common\models\Bmh;
use common\models\CompanyTransaction;
use common\models\Countries;
use common\models\Region;
use common\models\User;
use common\models\VirtualTransaction;
use common\models\WorkdayCalendar;
use Swagger\Annotations\Operation;
use Yii;
use yii\db\Exception;
use yii\helpers\ArrayHelper;

class AuctionOfferForm extends BaseModel
{
    public $price;
    public $id;
    public $countryId;
    public $classifierCountry = [];
    public AuctionActiveResource $model;
    public $pkcs7;


    public function __construct(AuctionActiveResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        $parent = parent::rules();
        $child = [
            [['price', 'classifierCountry'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            ['price', 'number'],

        ];
        return array_merge($parent, $child);
    }


    public function getResult()
    {
        $user = Yii::$app->user->identity;
        $company = $user->company;
        if($user->user_type != UserEnum::USER_TYPE_SUPPLIER){
            $this->addError("error", t("Yetkazib beruvchi taklif beroladi"));
            return false;
        }

        $transaction = Yii::$app->db->beginTransaction();

        try {
            $model = $this->model;

            if ($model->company_id == $company->id) {
                $this->addError("error", t("Auksion buyurtmachisi narx taklif etolmaydi"));
                return false;
            }

            $endTime = strtotime($model->auction_end);
            $now = strtotime(date("Y-m-d H:i:s"));
            if ($endTime < $now) {
                $this->addError("error", t("Auksion vaqti tugadi"));
                return false;
            }

            $request = new AuctionOffer([
                'company_id' => Yii::$app->user->identity->company_id,
                'auction_id' => $model->id,
                'is_winner' => 0
            ]);

            $next_price = $model->nextPrice * 100;
            if ($next_price <= 0) {
                $this->addError("error", t("Mumkin boʻlgan eng kam miqdor taklif etildi."));
                return false;
            }

            if (($request->price = $next_price) == $this->price * 100 and $request->validate()) {

                $date = date("Y-m-d H:i:s");

                $percentage_block_sum = $model->total_sum * env('ZALOG_AUCTION_PERCENT', 0.03);
                $commission_block_sum = $request->price * env('COMMISSION_AUCTION_PERCENT', 0.0015);
                $commission_block_sum = $commission_block_sum > 1000000 ? 1000000 : $commission_block_sum;

                $isDemping = $request->price / $model->total_sum <= env('DUMPING_AUCTION_PERCENT', 0.8);
                $demping_sum = $isDemping ? $model->total_sum - $request->price : 0;

                $bmh = Bmh::getAmount();
                if ($bmh * 2500 > $model->total_sum) {
                    if ($demping_sum > $bmh * 100) {
                        $demping_sum = $bmh * 100;
                    }
                } else {
                    if ($demping_sum > $bmh * 1000) {
                        $demping_sum = $bmh * 1000;
                    }
                }

//                $company_transactions_for_back_zalog = CompanyTransaction::find()
//                    ->where([
//                        'type' => CompanyTransactionEnum::TYPE_ZALOG,
//                        'auction_id' => $model->id,
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'reverted_id' => null
//                    ])
//                    ->all();
                $company_transactions_for_back_zalog = VirtualTransactionResource::findAllByProductAndOperationAndNotRefund(
                    OperationTypeEnum::PRODUCT_NAME_AUCTION,
                    $this->model->id,
                    OperationTypeEnum::BLOCK_SALE_DEPOSIT
                );

                $company_transactions_for_back_commisison =  VirtualTransactionResource::findAllByProductAndOperationAndNotRefund(
                    OperationTypeEnum::PRODUCT_NAME_AUCTION,
                    $this->model->id,
                    OperationTypeEnum::BLOCK_SALE_COMMISSION
                );

                foreach ($company_transactions_for_back_zalog as $z_for_back) {
                    /** @var VirtualTransactionResource  $z_for_back */
                    $virtaul_transaction_id = VirtualTransaction::saveTransaction(
                        $z_for_back->creditCompany,
                        $z_for_back->debitCompany,
                        OperationTypeEnum::P_K_30201,
                        OperationTypeEnum::P_K_30101,
                        $z_for_back->credit,
                        t("Auksion ni oldingi narx taklifi garovlari qaytarilyapti"),
                        OperationTypeEnum::PRODUCT_NAME_AUCTION,
                        $z_for_back->auction_id,
                        null,
                        OperationTypeEnum::UNBLOCK_SALE_DEPOSIT,
                    );

                    $z_for_back->parent_id = $virtaul_transaction_id;
                    if (!$z_for_back->save()){
                        $transaction->rollBack();
                        throw new Exception(t("Balansingizdan pul yechib bo‘lmadi."));
                    }
//
//                    $company_transaction_back = new CompanyTransaction([
//                        'company_id' => $z_for_back->company_id,
//                        'amount' => $z_for_back->amount,
//                        'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
//                        'auction_id' => $z_for_back->auction_id,
//                        'description' => t("Auksion ni oldingi narx taklifi garovlari qaytarilyapti"),
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'transaction_date' => $date
//                    ]);
//                    if ($company_transaction_back->save()) {
//                        $z_for_back->reverted_id = $company_transaction_back->id;
//                        if (!$z_for_back->save()) {
//
//                            $transaction->rollBack();
//                            $this->addError("error", t("Balansingizdan pul yechib bo‘lmadi."));
//                            return false;
//                        }
//                    }
                }


                foreach ($company_transactions_for_back_commisison as  $c_for_back) {
                    /** @var VirtualTransactionResource  $c_for_back */

                    $virtaul_transaction_id = VirtualTransaction::saveTransaction(
                        $c_for_back->creditCompany,
                        $c_for_back->debitCompany,
                        OperationTypeEnum::P_K_30202 ,
                        OperationTypeEnum::P_K_30101 ,
                        $c_for_back->credit,
                        t("Auksion ni oldingi narx taklifi komissiyalari qaytarildi") ,
                        OperationTypeEnum::PRODUCT_NAME_AUCTION,
                        $c_for_back->auction_id ,
                        null , OperationTypeEnum::UNBLOCK_SALE_COMMISSION,
                    );

                    $c_for_back->parent_id = $virtaul_transaction_id;
                    if (!$c_for_back->save()){
                        $transaction->rollBack();
                        throw new \Exception(t("Balansingizdan pul yechib bo‘lmadi."));
                    }

//                    $company_transaction_back2 = new CompanyTransaction([
//                        'company_id' => $c_for_back->company_id,
//                        'amount' => $c_for_back->amount,
//                        'type' => CompanyTransactionEnum::TYPE_REVERT_BLOCK_COMMISION,
//                        'auction_id' => $c_for_back->auction_id,
//                        'description' => t("Auksion ni oldingi narx taklifi komissiyalari qaytarildi"),
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'transaction_date' => $date
//                    ]);
//                    if ($company_transaction_back2->save()) {
//                        $c_for_back->reverted_id = $company_transaction_back2->id;
//                        if (!$c_for_back->save()) {
//                            $transaction->rollBack();
//                            $this->addError("error", t("Balansingizdan pul yechib bo‘lmadi."));
//                            return false;
//                        }
//                    }
                }


                if(!hasMoney($company , $percentage_block_sum + $commission_block_sum + $demping_sum)){
                    $transaction->rollBack();

                    $this->addError("error", t("Hisobda mablag' yetarli emas"));
                    return false;
                }
//                if ($company->availableBalance < ($percentage_block_sum + $commission_block_sum + $demping_sum)) {
//                    $transaction->rollBack();
//
//                    $this->addError("error", t("Hisobda mablag' yetarli emas"));
//                    return false;
//                }

//                $dempingTxt = $isDemping ? " demping" : "";

                VirtualTransaction::saveTransaction(
                    $company,
                    $company,
                    OperationTypeEnum::P_K_30101,
                    OperationTypeEnum::P_K_30201,
                    $percentage_block_sum + $demping_sum,
                    "Auksion ga garov bandlandi",
                    OperationTypeEnum::PRODUCT_NAME_AUCTION,
                    $this->model->id,
                    null,
                    OperationTypeEnum::BLOCK_SALE_DEPOSIT,

                );

                VirtualTransaction::saveTransaction(
                    $company,
                    $company,
                    OperationTypeEnum::P_K_30101,
                    OperationTypeEnum::P_K_30202,
                    $commission_block_sum,
                    "Auksion ga komisiya bandlandi",
                    OperationTypeEnum::PRODUCT_NAME_AUCTION,
                    $this->model->id,
                    null,
                    OperationTypeEnum::BLOCK_SALE_COMMISSION,

                );

//                $company_transaction = new CompanyTransaction([
//                    'company_id' => $user->company_id,
//                    'amount' => $percentage_block_sum + $demping_sum,
//                    'type' => CompanyTransactionEnum::TYPE_ZALOG,
//                    'auction_id' => $model->id,
//                    'description' => t("Auksionga garov bandlandi.") . $dempingTxt,
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => $date
//                ]);
//
//                if (!$company_transaction->save()) {
//                    $transaction->rollBack();
//                    $this->addErrors($company_transaction->errors);
//                    return false;
//                }
//
//
//                $company_transaction2 = new CompanyTransaction([
//                    'company_id' => $user->company_id,
//                    'amount' => $commission_block_sum,
//                    'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION,
//                    'auction_id' => $model->id,
//                    'description' => t("Auksion ga komisiya bandlandi."),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => $date
//                ]);
//                if (!$company_transaction2->save()) {
//                    $transaction->rollBack();
//                    $this->addErrors($company_transaction2->errors);
//                    return false;
//                }


                $request->create = time();
                if ($request->save()) {

                    $auctionClassifierCount = AuctionClassifier::find()->where(['auction_id' => $model->id])->count();
                    if ($auctionClassifierCount != count($this->classifierCountry)) {
                        $transaction->rollBack();
                        $this->addError("classifierCountry", "Barcha classifier uchun davlat tanlanmagan");
                        return false;
                    }

                    foreach ($this->classifierCountry as $elem) {
                        $countryId = $elem['country_id'];
                        $auctionClassifierId = $elem['auction_classifier_id'];

                        $country = Region::findOne(['id' => $countryId, 'type' => [Region::TYPE_COUNTRY, Region::TYPE_COUNTRY_UZB]]);
                        if ($country == null) {
                            $this->addError("classifierCountry.countryId", t("Davlat topilmadi"));
                            return false;
                        }

                        $auctionClassifier = AuctionClassifierShortResource::findOne(['id' => $auctionClassifierId, 'auction_id' => $model->id]);
                        if ($auctionClassifier == null) {
                            $this->addError("classifierCountry.auction_classifier_id", t("AuctionClassifier topilmadi"));
                            return false;
                        }
                        $auctionRequestClassifierCountry = new AuctionRequestClassifierCountry();
                        $auctionRequestClassifierCountry->country_id = $country->id;
                        $auctionRequestClassifierCountry->auction_id = $model->id;
                        $auctionRequestClassifierCountry->auction_request_id = $request->id;
                        $auctionRequestClassifierCountry->auction_classifier_id = $auctionClassifierId;
                        if (!$auctionRequestClassifierCountry->save()) {
                            $transaction->rollBack();
                            $this->addErrors($auctionRequestClassifierCountry->errors);
                            return false;
                        }
                    }

                    if ($endTime - $now <= 600) {
                        $workDays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::WORKDAY]), 'local_date', 'local_date');
                        $holidays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::HOLIDAY]), 'local_date', 'local_date');

                        $auctionEndDateTime = $this->calculateAuctionEnd($workDays, $holidays);
                        //10 mindan kam vaqt qolgan bo'lsa, narx taklif qilinsa, yana 10 min qolgan qilib quyiladi
                        $model->auction_end = $auctionEndDateTime;
                        if(!$model->save()){
                            $transaction->rollBack();
                            $this->addErrors($model->errors);
                            return false;
                        }
                    }

                    $transaction->commit();
                    return true;

                } else {
                    $transaction->rollBack();

                    $this->addError("error", t("So‘rovingiz qabul qilinmadi, qayta urinib ko‘ring."));
                }

            } else {
                $transaction->rollBack();
                $this->addErrors($request->errors);

            }
            return false;
        } catch
        (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        } catch (\Throwable $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    protected function calculateAuctionEnd(array $workDays = [], array $offDays = []): string
    {

        $now = new \DateTime();
        $now->modify('+10 minutes');

        $cutoff = clone $now;
        $cutoff->setTime(18, 0);

        if ($now > $cutoff) {
            $excessSeconds = $now->getTimestamp() - $cutoff->getTimestamp();

            $nextWorkDay = addDaysExcludingWeekends($cutoff->format('Y-m-d'), 1, $workDays, $offDays);
            $nextWorkDateTime = new \DateTime(date("Y-m-d",strtotime($nextWorkDay)) . ' 09:00:00');
            $nextWorkDateTime->modify("+{$excessSeconds} seconds");

            return $nextWorkDateTime->format('Y-m-d H:i:s');
        }

        return $now->format('Y-m-d H:i:s');
    }
}
