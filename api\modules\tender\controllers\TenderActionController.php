<?php

namespace api\modules\tender\controllers;

use api\components\ApiController;
use api\modules\tender\filters\TenderActionRequestFilter;
use api\modules\tender\forms\TenderActionRequestChairmanEndForm;
use api\modules\tender\forms\TenderActionRequestSecretaryEndForm;
use api\modules\tender\forms\TenderActionRequestVoteForm;
use api\modules\tender\forms\TenderCancelActionForm;
use api\modules\tender\resources\TenderActionRequestResource;
use api\modules\tender\resources\TenderResource;
use yii\web\NotFoundHttpException;
use Yii;

/**
 * TenderAction controller for the `tender` module
 */
class TenderActionController extends ApiController
{
    /**
     *
    */
    public function actionIndex()
    {
        return $this->sendResponse(
            new TenderActionRequestFilter(),
            Yii::$app->request->queryParams
        );
    }

    /**
     * Tender bekor qilish uchun so'rov yaratish
    */
    public function actionCreateRequest($id){
        return $this->sendResponse(
            new TenderCancelActionForm($this->findTenderOne($id)),
            \Yii::$app->request->bodyParams
        );
    }

    public function actionCommissionVote($id)
    {
        return $this->sendResponse(
            new TenderActionRequestVoteForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionChairmanEndRequestWithProtocol($id)
    {
        return $this->sendResponse(
            new TenderActionRequestChairmanEndForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * So'rov tender bekor qilish uchun tayyor bo'lsa
    */
    public function actionSecretaryEndRequest($id)
    {
        return $this->sendResponse(
            new TenderActionRequestSecretaryEndForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    private function findTenderOne($id)
    {
        $model = TenderResource::find()->notDeletedAndFromCompany()->andWhere(['id' => $id])->one();

        if (!$model) throw new NotFoundHttpException("Tender is not found");

        return $model;
    }

    /**
     * @throws NotFoundHttpException
     */
    private function findOne($id)
    {
        $model = TenderActionRequestResource::findOne($id);

        if (!$model) throw new NotFoundHttpException("Tender action request is not found");

        return $model;
    }
}
