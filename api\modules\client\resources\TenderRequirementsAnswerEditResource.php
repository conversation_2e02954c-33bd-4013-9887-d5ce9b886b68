<?php


namespace api\modules\client\resources;


use api\modules\common\resources\FileResource;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderRequirementsAnswer;

class TenderRequirementsAnswerEditResource extends TenderRequirementsAnswer
{

    public function fields()
    {
        return [
            'id', 'tenderRequirement', 'file', 'title', 'value'
        ];
    }

    public function getTenderRequirement()
    {
        return $this->hasOne(TenderRequirementsResource::class, ['id' => 'tender_requirements_id']);
    }

    public function getFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'file_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}