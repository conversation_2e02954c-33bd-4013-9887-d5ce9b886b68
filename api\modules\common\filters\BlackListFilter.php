<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use common\models\BlackList;

class BlackListFilter extends BaseRequest
{
    public $title_or_inn;
    public $region;


    public function rules()
    {
        return [
            [['title_or_inn','region'], 'safe'],
        ];
    }

    public function getResult()
    {
        $query = BlackList::find();

        if ($this->title_or_inn) {
            $query->where(['or',
                ['like', 'title', $this->title_or_inn],
                ['like', 'inn', $this->title_or_inn]
            ]);
        }
        if ($this->region) {
            $query->where(['like', 'region', $this->region]);
        }
         return paginate($query);
    }
}
