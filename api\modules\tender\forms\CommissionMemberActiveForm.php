<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\tender\resources\CommissionMemberResource;
use common\enums\StatusEnum;

class CommissionMemberActiveForm extends BaseRequest
{
    public CommissionMemberResource $model;

    public function __construct(CommissionMemberResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function getResult()
    {
        $this->model->status = StatusEnum::STATUS_ACTIVE;
        return $this->model->save();
    }
}