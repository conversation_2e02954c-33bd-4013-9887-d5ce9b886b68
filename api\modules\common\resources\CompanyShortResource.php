<?php


namespace api\modules\common\resources;


use common\models\Company;
use common\models\query\NotDeletedFromCompanyQuery;

class CompanyShortResource extends Company
{
    public function fields()
    {
        return [
            'id',
            'tin',
            'pinfl',
            'title',
            'region',
            'district',
            'address'
        ];
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

    public function getRegion()
    {
        return $this->hasOne(RegionResource::class, ['id' => 'region_id']);
    }

    /**
     * Gets query for [[CompanyBalance]]
     * @return \yii\db\ActiveQuery
     */
    public function getDistrict()
    {
        return $this->hasOne(RegionResource::class, ['id' => 'district_id']);
    }

}