<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\BankResource;

class BankFilter extends BaseRequest
{
    public $mfo;

    public function rules()
    {
        return [
            ['mfo', 'safe']
        ];
    }

    public function getResult()
    {
        $model = BankResource::find();
        if($this->mfo){
            $model->orWhere(['like', 'mfo', $this->mfo]);
            $model->orWhere(['like', 'brand_name', $this->mfo]);
            $model->orWhere(['like', 'name', $this->mfo]);
        }

        return $model->orderBy('mfo asc')->all();
    }
}