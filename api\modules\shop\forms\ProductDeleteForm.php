<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;
use common\models\shop\ProductFile;
use common\models\shop\ProductRegion;

class ProductDeleteForm extends BaseRequest
{

    public ProductResource $model;
    public $delete_reason;
    public function __construct(ProductResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [
                ['delete_reason'], 'required', 'message' => t('{attribute} yuborish majburiy')
            ],
        ];
    }



    public function getResult()
    {
        $this->model->state = ProductEnum::SHOP_STATE_DELETED;
        $this->model->status = ProductEnum::STATUS_DELETED;
        $this->model->delete_reason = $this->delete_reason;
        if ($this->model->delete()){
            $productId = $this->model->id;
            ProductRegion::deleteAll(['product_id' => $productId]);
            ProductFile::deleteAll(['product_id' => $productId]);
        }
        return true;
    }
}