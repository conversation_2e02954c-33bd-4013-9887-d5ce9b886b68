<?php

namespace api\modules\tender\filters;

use api\components\BaseRequest;
use api\modules\tender\resources\CommissionGroupMemberResource;
use api\modules\tender\resources\CommissionMemberResource;
use api\modules\tender\resources\CommissionMemberShortResource;
use common\enums\StatusEnum;
use common\enums\TenderEnum;
use common\models\CommissionGroup;

class CommissionMemberByGroupIdFilter extends BaseRequest
{

    public $groupId;

    public function rules()
    {
        return [
            [['groupId'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['groupId'], 'integer']
        ];
    }

    public function getResult()
    {
        $group = CommissionGroup::findOne(['id' => $this->groupId, 'company_id' => \Yii::$app->user->identity->company_id]);
        $members = null;
        if ($group) {
            $members = CommissionGroupMemberResource::find()->where(['commission_group_id' => $this->groupId, 'status' => StatusEnum::STATUS_ACTIVE])->all();
        }
        return $members;
    }

}