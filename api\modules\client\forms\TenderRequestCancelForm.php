<?php


namespace api\modules\client\forms;


use Yii;
use common\enums\OperationTypeEnum;
use common\models\VirtualTransaction;
use api\components\BaseRequest;
use api\modules\client\resources\TenderQualificationSelectionResource;
use api\modules\client\resources\TenderRequestResource;
use api\modules\client\resources\TenderRequestValuesResource;
use api\modules\client\resources\TenderRequirementsAnswerResource;
use api\modules\client\resources\TenderResource;
use common\enums\TenderEnum;
use yii\db\Exception;

class TenderRequestCancelForm extends BaseRequest
{

    public TenderResource $model;
    public TenderRequestResource $request;
    public $description;

    public function __construct(TenderRequestResource $model, $params = [])
    {
        $this->request = $model;
        $this->model = $model->tender;
        parent::__construct($params);
    }

    public function rules()
    {
        return  [
            ['description', 'required', 'message' => t('{attribute} yuborish majburiy')],
            ['description', 'string', 'max' => 255],
        ];
    }


    /**
     * @throws Exception
     * @throws \Throwable
     * @throws \yii\base\Exception
     */
    public function getResult()
    {
        date_default_timezone_set("Asia/Tashkent");

        if ($this->model->state == TenderEnum::STATE_READY && strtotime($this->model->end_date) > time()) {

            $transaction = Yii::$app->db->beginTransaction();
            try {
                $userId = Yii::$app->user->id;
                $company = Yii::$app->user->identity->company;
                $this->request->description = $this->description;
                $this->request->status = TenderEnum::STATUS_DELETED;
                $this->request->state = TenderEnum::REQUEST_STATE_CANCELLED;
                $this->request->updated_by = $userId;
                $this->request->deleted_at = date("Y-m-d H:i:s");
                if (!$this->request->save()) {
                    $this->addErrors($this->request->errors);
                    return false;
                }
                TenderRequestValuesResource::updateAll(['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s"), 'updated_by' => $userId], [
                    'tender_id' => $this->request->tender_id,
                    'tender_request_id' => $this->request->id,
                    'status' => TenderEnum::STATUS_ACTIVE
                ]);

                TenderRequirementsAnswerResource::updateAll(['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s"), 'updated_by' => $userId], [
                    'tender_id' => $this->request->tender_id,
                    'tender_request_id' => $this->request->id,
                    'status' => TenderEnum::STATUS_ACTIVE,
                    'company_id' => Yii::$app->user->identity->company_id
                ]);
                TenderQualificationSelectionResource::updateAll(['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s"), 'updated_by' => $userId], [
                    'tender_id' => $this->request->tender_id,
                    'tender_request_id' => $this->request->id,
                    'status' => TenderEnum::STATUS_ACTIVE,
                    'company_id' => Yii::$app->user->identity->company_id
                ]);


//                $company_transaction_commission = CompanyTransaction::find()->where([
//                    'company_id' => $company->id,
//                    'reverted_id' => null,
//                    'tender_id' => $this->model->id,
//                    'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION,
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                ])->one();
//                if ($company_transaction_commission) {
//                    $company_transaction_commission_revert = new CompanyTransaction([
//                        'company_id' => $company_transaction_commission->company_id,
//                        'contract_id' => null,
//                        'tender_id' => $this->model->id,
//                        'type' => CompanyTransactionEnum::TYPE_REVERT_BLOCK_COMMISION,
//                        'description' => Yii::t("main", "Taklif bekor qilingani uchun kommissiya qaytarildi"),
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'transaction_date' => date("Y-m-d H:i:s"),
//                    ]);
//                    if ($company_transaction_commission_revert->save()) {
//                        $company_transaction_commission->reverted_id = $company_transaction_commission_revert->id;
//                        $company_transaction_commission->save(false);
//                    }
//                }
//                $company_transaction_zalog = CompanyTransaction::find()->where([
//                    'company_id' => $company->id,
//                    'reverted_id' => null,
//                    'tender_id' => $this->model->id,
//                    'type' => CompanyTransactionEnum::TYPE_ZALOG,
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                ])->one();
//                if ($company_transaction_zalog) {
//                    $company_transaction_commission_revert_z = new CompanyTransaction([
//                        'company_id' => $company_transaction_zalog->company_id,
//                        'contract_id' => null,
//                        'tender_id' => $this->model->id,
//                        'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
//                        'description' => Yii::t("main", "Taklif bekor qilingani uchun garov qaytarildi"),
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'transaction_date' => date("Y-m-d H:i:s"),
//                    ]);
//                    if ($company_transaction_commission_revert_z->save()) {
//                        $company_transaction_zalog->reverted_id = $company_transaction_commission_revert_z->id;
//                        $company_transaction_zalog->save(false);
//                    }
//                }
                $company_transaction_commission = Virtualtransaction::find()->where([
                    "credit_company_id" => $company->id,
                    "contract_id" => null,
                    "tender_id" => $this->model->id,
                    "operation_type" => OperationTypeEnum::BLOCK_SALE_COMMISSION,
                    "parent_id" => null,
                ])->andWhere([">","credit",0])->one();
                $company_transaction_zalog = Virtualtransaction::find()->where([
                    "credit_company_id" => $company->id,
                    "contract_id" => null,
                    "tender_id" => $this->model->id,
                    "operation_type" => OperationTypeEnum::BLOCK_SALE_DEPOSIT,
                    "parent_id" => null,
                ])->andWhere([">","credit",0])->one();
                try {
                    /** @var VirtualTransaction $company_transaction_commission */
                    /** @var VirtualTransaction $company_transaction_zalog */
                    if ($company_transaction_commission) {
                        $revert = VirtualTransaction::saveTransaction(
                            $company_transaction_commission->creditCompany,
                            $company_transaction_commission->debitCompany,
                            $company_transaction_commission->creditAccount->prefix_account,
                            $company_transaction_commission->debitAccount->prefix_account,
                            $company_transaction_commission->credit,
                            Yii::t("main", "Taklif bekor qilingani uchun kommissiya qaytarildi"),
                            OperationTypeEnum::PRODUCT_NAME_TENDER,
                            $this->model->id,
                            null,
                            OperationTypeEnum::UNBLOCK_SALE_COMMISSION
                        );
                        $company_transaction_commission->parent_id = $revert;
                        if (!$company_transaction_commission->save()) {
                            throw new Exception("Kommissiya saqlashda xatolik");
                        }
                    }
                    if ($company_transaction_zalog) {
                        $revert = VirtualTransaction::saveTransaction(
                            $company_transaction_zalog->creditCompany,
                            $company_transaction_zalog->debitCompany,
                            $company_transaction_zalog->creditAccount->prefix_account,
                            $company_transaction_zalog->debitAccount->prefix_account,
                            $company_transaction_zalog->credit,
                            Yii::t("main", "Taklif bekor qilingani uchun garov qaytarildi"),
                            OperationTypeEnum::PRODUCT_NAME_TENDER,
                            $this->model->id,
                            null,
                            OperationTypeEnum::UNBLOCK_SALE_DEPOSIT
                        );
                        $company_transaction_zalog->parent_id = $revert;
                        if (!$company_transaction_zalog->save()) {
                            throw new Exception("Zalog saqlashda xatolik");
                        }
                    }
                } catch (Exception $ex){
                    $transaction->rollBack();
                    throw $ex;
                }
                $transaction->commit();
                return true;

            } catch (Exception $e) {
                $transaction->rollBack();
                $this->addError("error", $e->getMessage());
                return false;
            }

        }
        $this->addError('error', t("Tender vaqti tugadi"));

        return false;
    }
}