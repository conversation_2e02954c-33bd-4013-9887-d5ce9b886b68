<?php

use api\modules\tender\resources\TenderCommissionMemberResource;
use common\enums\TenderEnum;

/**
 * @var TenderCommissionMemberResource[] $members
 * @var boolean $showSecretary
 */

// Filter commission members and count them
$commissionMembers = array_filter($members, function ($m) {
    return $m['role'] == TenderEnum::ROLE_COMMISSION;
});
$secretaryMembers = array_filter($members, function ($m) {
    return $m['role'] == TenderEnum::ROLE_SECRETARY;
});
$commissionCount = count($commissionMembers); // commission members
$commissionIndex = 0;

?>

<table style="border-collapse: collapse;border:none;width: 680px;">
    <tbody>
    <?php foreach ($members as $key => $member): ?>
        <?php if ($member['role'] == TenderEnum::ROLE_CHAIRMAN): ?>
            <tr>
                <td style="width: 180pt;padding: 3mm 0 0 3mm;height: 22.85pt;vertical-align: top;border:none;">
                    <p class="MsoNormal"
                       style='margin-top:6.4pt;margin-right:0cm;margin-bottom:.0001pt;margin-left:4.5pt;text-align:left;font-family:"Trebuchet MS","sans-serif";'>
                        Председатель закупочной комиссии
                    </p>
                </td>
                <td style="width: 300pt;border:none;padding: 0 0 1mm 1mm;height: 22.85pt;vertical-align: bottom;">
                    <p class="MsoNormal"
                       style='margin-top:6.4pt;margin-right:0cm;margin-bottom:.0001pt;margin-left:4.5pt;text-align:left;font-family:"Trebuchet MS","sans-serif";'>
                        <?= $member['fullname'] ?>
                    </p>
                </td>
            </tr>
        <?php elseif ($member['role'] == TenderEnum::ROLE_COMMISSION): ?>
            <?php if ($commissionIndex === 0): ?>
                <tr>
                    <td rowspan="<?= $commissionCount ?>"
                        style="width: 180pt;padding: 3mm 0 0 3mm;height: 22.85pt;vertical-align: top;border:none;">
                        <p class="MsoNormal"
                           style='margin-top:6.4pt;margin-right:0cm;margin-bottom:.0001pt;margin-left:4.5pt;text-align:left;font-family:"Trebuchet MS","sans-serif";'>
                            Члены&nbsp;закупочной комиссии
                        </p>
                    </td>
                    <td style="width: 300pt;border:none;padding: 0 0 1mm 1mm;height: 22.85pt;vertical-align: bottom;">
                        <p class="MsoNormal"
                           style='margin-top:6.4pt;margin-right:0cm;margin-bottom:.0001pt;margin-left:4.5pt;text-align:left;font-family:"Trebuchet MS","sans-serif";'>
                            <?= $member['fullname'] ?>
                        </p>
                    </td>
                </tr>
                <?php $commissionIndex++ ?>
            <?php else: ?>
                <tr>
                    <td style="width: 300pt;border:none;padding: 0 0 0 1mm;height: 22.85pt;vertical-align: middle;">
                        <p style='margin-top:6.4pt;margin-right:0cm;margin-bottom:.0001pt;margin-left:4.5pt;text-align:left;font-family:"Trebuchet MS","sans-serif";'>
                            <?= $member['fullname'] ?>
                        </p>
                    </td>
                </tr>
            <?php endif; ?>
        <?php endif; ?>
    <?php endforeach; ?>
    <!-- Show secretary -->
    <?php if (isset($showSecretary) && $showSecretary): ?>
        <?php foreach ($secretaryMembers as $key => $member): ?>
            <tr>
                <td rowspan="3"
                    style="width: 180pt;padding: 3mm 0 0 3mm;height: 22.85pt;vertical-align: top;border:none;">
                    <p class="MsoNormal"
                       style='margin-top:6.4pt;margin-right:0cm;margin-bottom:.0001pt;margin-left:4.5pt;text-align:left;font-family:"Trebuchet MS","sans-serif";'>
                        Секретарь закупочной комиссии
                    </p>
                </td>
                <td style="width: 300pt;border:none;padding: 0 0 1mm 1mm;height: 22.85pt;vertical-align: bottom;">
                    <p class="MsoNormal"
                       style='margin-top:6.4pt;margin-right:0cm;margin-bottom:.0001pt;margin-left:4.5pt;text-align:left;font-family:"Trebuchet MS","sans-serif";'>
                        <?= $member['fullname'] ?>
                    </p>
                </td>
            </tr>
        <?php endforeach; ?>
    <?php endif; ?>
    </tbody>
</table>

