<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\CompanyBalanceResource;
use api\modules\common\resources\CompanyTransactionResource;
use common\enums\CompanyTransactionEnum;
use Yii;

class BalanceWithdrawToAccountFilter extends BaseRequest
{


    public function getResult()
    {

        $companyId = \Yii::$app->user->identity->company_id;
//        return $companyId;

        $company_balance = CompanyBalanceResource::findOne(['company_id' => $companyId]);

        if ($company_balance) {
            $company_balance->calculateBalance();
        } else {
            $this->addError('balance', t("Balans bilan ishlash uchun siz bank hisobingizni ko'rsatishingiz kerak."));

            return false;
        }

        $transactions = CompanyTransactionResource::find()
            ->where(['company_id' => $companyId])
            ->andWhere(['type' => CompanyTransactionEnum::TYPE_WITHDRAW])
            ->orderBy('created_at desc');

        return paginate($transactions);
    }

}