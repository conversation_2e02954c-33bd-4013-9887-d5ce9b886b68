<?php


namespace api\modules\client\resources;


use api\modules\common\resources\FileResource;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderRequirementsAnswer;

class TenderRequirementsAnswerResource extends TenderRequirementsAnswer
{

    public function fields(){
        return [
            'id',
            'company_id',
            'tender_id',
            'file',
            'title',
            'status',
            'value'
        ];
    }



    /**
     * Gets query for [[File]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'file_id']);
    }

    /**
     * Gets query for [[Tender]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTender()
    {
        return $this->hasOne(TenderResource::class, ['id' => 'tender_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}