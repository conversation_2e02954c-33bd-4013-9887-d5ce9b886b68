<?php


namespace api\modules\common\forms;


use api\components\BaseRequest;
use api\modules\common\resources\PlanScheduleClassifierCreateResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\StatusEnum;
use common\enums\TenderEnum;
use Yii;
use yii\base\Exception;

class PlanScheduleUpdateForm extends BaseRequest
{

    public $year;
    public $quarter;
    public $title;
    public $classifiers = [];

    public PlanScheduleResource $model;

    public function __construct(PlanScheduleResource $model, $params = [])
    {
        $this->model = $model;
        parent::__construct($params);
    }


    /**
     * {@inheritdoc}
     * #return, year qiymatini aniqlashtirish kerak
     */
    public function rules()
    {
        return [
            [['classifiers', 'title', 'quarter', 'year'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['title'], 'string', 'max' => 255],
            ['quarter', 'integer', 'min' => 1, 'max' => 4],
            ['year', 'integer', 'min' => 2023, 'max' => 2025],
        ];
    }

    public function getResult()
    {
        $transaction = Yii::$app->db->beginTransaction();

        if ($this->model->status != StatusEnum::STATUS_ACTIVE) {
            throw new Exception(t("Reja jadvali aktiv holatda emas"));
        }

        if ($this->model->isUsed) {
            throw new Exception(t("Reja jadvali avval foydalanilgan. Tahrirlash mumkin emas"));
        }

        $this->model->title = $this->title;
        $this->model->year = $this->year;
        $this->model->quarter = $this->quarter;

        if (!$this->model->save()) {
            $this->addErrors($this->model->errors);
            $transaction->rollBack();
            return false;
        }


        $clasId = [];

        foreach ($this->classifiers as $classifier) {

            if (isset($classifier['planScheduleClassifierId']) && $classifier['planScheduleClassifierId'] > 0) {

                $product = PlanScheduleClassifierCreateResource::findOne($classifier['planScheduleClassifierId']);
                if ($product) {
                    $product->classifier_id = $classifier['classifier_id'];
                    $product->unit_id = $classifier['unit_id'];
                    $product->description = $classifier['description'];
                    $product->year = $this->model->year;
                    $product->month = $classifier['month'];

                    $product->count = $classifier['count'];
                    $product->count_live = $classifier['count'];
//                    $product->count_used = $classifier['count_used'];
                    $product->status = TenderEnum::STATUS_ACTIVE;
                    $product->enabled = 1;
                }

            } else {
                $product = new PlanScheduleClassifierCreateResource([
                    'plan_schedule_id' => $this->model->id,
                    'classifier_id' => $classifier['classifier_id'],
                    'unit_id' => $classifier['unit_id'],
                    'description' => $classifier['description'],
                    'year' => $this->model->year,
                    'month' => $classifier['month'],
                    'count' => $classifier['count'],
                    'count_live' => $classifier['count'],
                    'count_used' => 0,
                    'status' => TenderEnum::STATUS_ACTIVE,
                    'enabled' => 1,
                ]);
            }


            $this->model->total_product_count += $product->count;

            if (!$product->save()) {
                $this->addError('classifiers', $product->errors);
                $transaction->rollBack();
                return false;
            }
            else{
                array_push($clasId, $product->id);
            }

        }
        PlanScheduleClassifierResource::updateAll(
            ['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s"), 'updated_by' => Yii::$app->user->id],
           [
               'and',
               ['plan_schedule_id' => $this->model->id, 'status' => TenderEnum::STATUS_ACTIVE],
               ['not in', 'id', $clasId]
           ]);

        if (!$this->model->save()) {
            $this->addErrors($this->model->errors);
            $transaction->rollBack();
            return false;
        }

        $transaction->commit();
        return true;
    }

}