<?php

namespace app\modules\client\resources;

use api\modules\common\resources\ClassifierResource;
use common\models\SoleSupplier;
use Yii;
use yii\base\InvalidConfigException;

/**
 * @var ClassifierResource[] $classifier
 */
class SoleSupplierResource extends SoleSupplier
{
    public function fields(): array
    {
        return [
            'id',
            'name_uz',
            'name_ru',
            'name_en',
            'name_uzk',
            'address_uz',
            'address_ru',
            'address_en',
            'address_uzk',
            'tin',
            'classifiers'
        ];
    }

    /**
     * @throws InvalidConfigException
     */
    public function getClassifiers()
    {
        return $this->hasMany(ClassifierResource::class,['id' => 'classifier_id'])
            ->viaTable('sole_supplier_classifier', ['sole_supplier_id' => 'id']);
    }
}