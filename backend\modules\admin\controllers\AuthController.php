<?php


namespace backend\modules\admin\controllers;


use api\components\ApiController;
use backend\modules\admin\forms\LoginForm;
use Yii;
use yii\filters\auth\HttpBearerAuth;
use yii\filters\VerbFilter;

class AuthController extends ApiController
{
    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['bearerAuth'] = [
            'class' => HttpBearerAuth::class,
            'except' => [
                'options',
                'login',
                'refresh-token',
            ],
        ];
        $parent['verbFilter'] = [
            'class' => VerbFilter::class,
            'actions' => [
                'login' => ['POST'],
                'refresh-token' => ['POST'],
                'logout' => ['POST'],
            ]
        ];
        return $parent;
    }

    public function actionLogin(){

        return $this->sendResponse(
            new LoginForm(),
            Yii::$app->request->bodyParams
        );
    }
}