<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\CompanyTransactionFullResource;
use api\modules\common\resources\CompanyWithBankInfoResource;
use common\enums\CompanyTransactionEnum;
use common\models\CompanyTransaction;
use Yii;

class CompanyBalanceHistoryFilter extends BaseRequest
{

    public $fromDate;
    public $toDate;

    public function rules()
    {
        return [
            [['fromDate', 'toDate'], 'safe'],
        ];
    }


    public function getResult()
    {

        $companyId = \Yii::$app->user->identity->company_id;

        $comp = CompanyWithBankInfoResource::findOne($companyId);
        if ($this->toDate == null){
            $this->toDate = date("Y-m-d H:i:s");
        } else {
            $this->toDate = $this->toDate . " 23:59:59";
        }

        $transactions = CompanyTransactionFullResource::find()
            ->where(['company_id' => $companyId]);
        if( $this->toDate != null && isDate($this->toDate)){
            $transactions->andWhere(['<=', 'transaction_date', date("Y-m-d H:i:s", strtotime($this->toDate))]);
        }

        if($this->fromDate != null && isDate($this->fromDate)){
            $transactions->andWhere(['>=', 'transaction_date', date("Y-m-d 00:00:00", strtotime($this->fromDate))]);
        }
        $transactions->orderBy('transaction_date desc, id desc');

        return [
            'company' => $comp,
            'beginningBalance' => $this->fromDate != null ? $this->calculateBalance($this->fromDate) : null,
            'endingBalance' => $this->calculateBalance($this->toDate),
            'transactions' => paginate($transactions),
        ];
    }

    public function calculateBalance($toDate)
    {
        $company_id = Yii::$app->user->identity->company_id;
        $income = CompanyTransaction::find()
                ->where([
                    'company_id' => $company_id,
                    'type' => [
                        CompanyTransactionEnum::TYPE_PAYED_FROM_CONTRACT,
                        CompanyTransactionEnum::TYPE_REFILL,
                        CompanyTransactionEnum::TYPE_PENALTY_IN
                    ],
                    'status' => CompanyTransactionEnum::STATUS_SUCCESS
                ])
                ->andWhere(['<=', 'transaction_date', date("Y-m-d 23:59:59", strtotime($toDate))])
                ->sum("amount") + 0;


        $outplay = CompanyTransaction::find() // расходы
            ->where([
                'company_id' => $company_id,
                'type' => [
                    CompanyTransactionEnum::TYPE_PAY_TO_CONTRACT,
                    CompanyTransactionEnum::TYPE_WITHDRAW,
                    CompanyTransactionEnum::TYPE_COMMISSION,
                    CompanyTransactionEnum::TYPE_PENALTY_OUT
                ],
                'status' => CompanyTransactionEnum::STATUS_SUCCESS
            ])->andWhere(['<=', 'transaction_date', date("Y-m-d 23:59:59", strtotime($toDate))])->sum("amount") + 0;

        $deposit = CompanyTransaction::find()
                ->where([
                    'company_id' => $company_id,
                    'type' => [CompanyTransactionEnum::TYPE_ZALOG, CompanyTransactionEnum::TYPE_BLOCK_COMMISION, CompanyTransactionEnum::TYPE_BLOCK_PAYMENT_FOR_CONTRACT, CompanyTransactionEnum::TYPE_DEMPING, CompanyTransactionEnum::TYPE_BLOCK_PAYMENT_FOR_CONTRACT],
                    'status' => CompanyTransactionEnum::STATUS_SUCCESS
                ])->andWhere(['<=', 'transaction_date', date("Y-m-d 23:59:59", strtotime($toDate))])->sum("amount") + 0;

        $revert_deposit = CompanyTransaction::find()
                ->where([
                    'company_id' => $company_id,
                    'type' => [CompanyTransactionEnum::TYPE_REVERT_ZALOG, CompanyTransactionEnum::TYPE_REVERT_BLOCK_COMMISION, CompanyTransactionEnum::TYPE_REVERT_PAYMENT_FOR_CONTRACT, CompanyTransactionEnum::TYPE_REVERT_DEMPING, CompanyTransactionEnum::TYPE_REVERT_PAYMENT_FOR_CONTRACT],
                    'status' => CompanyTransactionEnum::STATUS_SUCCESS
                ])->andWhere(['<=', 'transaction_date', date("Y-m-d 23:59:59", strtotime($toDate))])->sum("amount") + 0;

        $balance = $income - $outplay;

        $available = $balance - $deposit + $revert_deposit;

        return $available;
    }


}