<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use common\enums\ProductEnum;
use Yii;
use yii\data\ActiveDataProvider;

class ClassifierCategoryFilter extends BaseRequest
{
    public $code;
    public $pageNo = 0;
    public $pageSize = 10;
    public function rules()
    {
        return [
            [['pageNo', 'pageSize'], 'integer'],
            ['code', 'integer']
        ];
    }

    public function getResult()
    {
        // $models = ClassifierCategoryResource::find()
        //     ->select(['classifier_category.*, (select count(*) from classifier where (classifier.classifier_category_id = classifier_category.id) and classifier.deleted_at is null) as classifier_count'])
        //     ->orderBy('category_translate.title asc, classifier_count desc');

        // TODO for active product category
//        $models = ClassifierCategoryResource::find()
//            ->innerJoin("product", "classifier_category.id = product.classifier_category_id and product.deleted_at is null and product.status = :status", [':status' => ProductEnum::STATUS_ACTIVE])
//            ->select(['classifier_category.*, count(product.id) as product_count'])
//            ->orderBy('product_count desc')
//            ->groupBy('classifier_category.id');

        // TODO all category
        $model = ClassifierCategoryResource::find();
        if ($this->code) {
            $model->andWhere(['code' => $this->code]);
        }

        $pagination = new \yii\data\Pagination([
            'totalCount' => $model->count(),
            'pageSize' => $this->pageSize,
            'page' => $this->pageNo
        ]);

        $model->offset($pagination->offset);
        $model->limit($pagination->pageSize);

        return [
            'data' => $model->all(),
            'meta' => [
                'pageCount' => $pagination->pageCount,
                'pageSize' => $pagination->pageSize,
                'pageNo' => $pagination->page,
                'totalCount' => $pagination->totalCount
            ],
        ];
    }
}
