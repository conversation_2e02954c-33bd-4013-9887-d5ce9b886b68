<?php


namespace backend\modules\admin\controllers;


use api\components\ApiController;
use backend\modules\admin\filters\TenderFilter;
use backend\modules\admin\resources\TenderResource;
use common\models\Tender;
use Yii;
use yii\web\NotFoundHttpException;

class TenderController extends ApiController
{
    public function actionTenderList($id)
    {
        return $this->sendResponse(
            new TenderFilter($id),
            Yii::$app->request->queryParams,

        );
    }

    private function findOne($id)
    {
        $model = TenderResource::findOne($id);

        if (!$model) throw new NotFoundHttpException("Tender is not found");

        return $model;
    }

}