<?php

namespace api\modules\auction\controllers;

use api\components\ApiController;
use api\modules\auction\filters\AuctionClassifierFilter;
use api\modules\auction\forms\AuctionClassifierForm;
use common\behaviors\RoleAccessBehavior;
use common\models\auction\AuctionClassifier;
use Yii;

class AuctionClassifierController extends ApiController
{

    public function behaviors()
    {
        $parent = parent::behaviors();
        $parent['accessByRole'] = [
            'class' => RoleAccessBehavior::class,
            'rules' => [
               'index' => ['user'],
               'create' => ['user'],
               'update' => ['user'],
               'delete' => ['user'],
               'view' => ['user'],
            ],
        ];
        return $parent;
    }
  public function actionIndex()
  {
    return $this->sendResponse(
      new AuctionClassifierFilter(),
      Yii::$app->request->queryParams
    );
  }

  public function actionCreate()
  {
    return $this->sendResponse(
      new AuctionClassifierForm(),
      Yii::$app->request->bodyParams
    );
  }

  public function actionUpdate(AuctionClassifier $auctionClassifier)
  {
    return $this->sendResponse(
      new AuctionClassifierForm($auctionClassifier),
      Yii::$app->request->bodyParams
    );
  }

  public function actionDelete(AuctionClassifier $auctionClassifier)
  {
    return $this->sendResponse(
      new AuctionClassifierFilter($auctionClassifier),
      Yii::$app->request->queryParams
    );
  }

  public function actionView(AuctionClassifier $auctionClassifier)
  {
    return $this->sendResponse(
      new AuctionClassifierFilter($auctionClassifier),
      Yii::$app->request->queryParams
    );
  }
}
