<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\CompanyBalanceResource;
use api\modules\common\resources\CompanyTransactionResource;

class CompanyBalanceFilter extends BaseRequest
{

    public $search;

    public function rules()
    {
        return [
            ['search', 'safe']
        ];
    }

    public function getResult()
    {

        $companyId = \Yii::$app->user->identity->company_id;
//        return $companyId;

        $company_balance = CompanyBalanceResource::findOne(['company_id' => $companyId]);

        if ($company_balance) {
            $company_balance->calculateBalance();
        } else {
            $this->addError('balance', t("Balans bilan ishlash uchun siz bank hisobingizni ko'rsatishingiz kerak."));
            return false;
        }

        $transactions = CompanyTransactionResource::find()
            ->leftJoin("tender", "tender.id=company_transaction.tender_id")
            ->leftJoin("auction", "auction.id=company_transaction.auction_id")
            ->leftJoin("order", "order.id=company_transaction.order_id")
            ->where(['company_transaction.company_id' => $companyId]);

        if ($this->search) {
            $transactions->andWhere([
                'or',
                ['auction.lot' => $this->search],
                ['tender.lot' => $this->search],
                ['order.lot_number' => $this->search],
                ['contract_id' => $this->search]
            ]);
        }

        $transactions->orderBy('company_transaction.transaction_date desc, company_transaction.id desc');

        return [
            'company_balance' => $company_balance,
            'transactions' => paginate($transactions)
        ];
    }
}