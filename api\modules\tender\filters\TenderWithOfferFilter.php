<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderResourceForClient;
use common\enums\TenderEnum;

class TenderWithOfferFilter extends BaseRequest
{

    public $lot;

    public function rules()
    {
        return [
            [['lot'], 'safe'],
        ];
    }

    public function getResult()
    {
        $model = TenderResourceForClient::find()
            ->join('inner join', 'tender_request', 'tender_request.tender_id=tender.id')
            ->where(['tender_request.company_id' => \Yii::$app->user->identity->company_id]);
        if ($this->lot) {
            $model->andWhere(['like', 'tender.lot', $this->lot]);
        }

        $model->orderBy(['updated_at' => SORT_DESC]);

        return paginate($model);

    }

}