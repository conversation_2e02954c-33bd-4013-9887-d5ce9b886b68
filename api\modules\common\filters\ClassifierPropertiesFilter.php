<?php

namespace api\modules\common\filters;

use api\components\BaseRequest;
use api\modules\common\resources\ClassifierPropertiesResource;
use api\modules\common\resources\ClassifierResource;
use common\enums\StatusEnum;
use yii\web\NotFoundHttpException;

class ClassifierPropertiesFilter extends BaseRequest
{
    public $classifierId;
    public $lang;

    public function rules()
    {
        return [
            [['classifierId', 'lang'], 'required'],
            ['classifierId', 'integer'],
            ['lang', 'string'],
        ];
    }

    public function getResult()
    {
        $classifier = ClassifierResource::findOne($this->classifierId);
        if (!$classifier) {
            throw new NotFoundHttpException("Classifier not found");
        }
        return ClassifierPropertiesResource::find()->where(['classifier_id' => $classifier->id, 'lang' => $this->lang, 'status' => StatusEnum::STATUS_ACTIVE])->all();
    }

}