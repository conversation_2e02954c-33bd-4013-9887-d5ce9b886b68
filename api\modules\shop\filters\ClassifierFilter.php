<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use common\enums\ProductEnum;
use common\models\Classifier;

class ClassifierFilter extends BaseRequest
{
    public $category_id;
    public $code;
    public $pageNo = 0;
    public $pageSize = 10;
    public function rules()
    {
        return [
            [['pageNo', 'pageSize','category_id'], 'integer'],
            [['code'], 'safe'],
        ];
    }

    public function getResult()
    {
        $model = Classifier::find();
        if ($this->category_id) {
            $model->andWhere(['category_id' => $this->category_id]);
        }
        if ($this->code) {
            $model->andWhere(['code' => $this->code]);
        }
        $pagination = new \yii\data\Pagination([
            'totalCount' => $model->count(),
            'pageSize' => $this->pageSize,
            'page' => $this->pageNo
        ]);

        $model->offset($pagination->offset);
        $model->limit($pagination->pageSize);

        return [
            'data' => $model->all(),
            'meta' => [
                'pageCount' => $pagination->pageCount,
                'pageSize' => $pagination->pageSize,
                'pageNo' => $pagination->page,
                'totalCount' => $pagination->totalCount
            ],
        ];
    }
}
