<?php


namespace api\modules\shop\forms;



use api\modules\common\resources\VirtualTransactionResource;
use Yii;
use api\components\BaseRequest;
use api\modules\shop\resources\OrderRequestResource;
use common\enums\OperationTypeEnum;
use common\enums\ShopEnum;
use common\models\Bmh;
use common\models\CompanyVirtualAccount;
use common\models\shop\Order;
use common\models\shop\OrderRequest;
use common\models\shop\Product;
use common\models\VirtualTransaction;
use yii\base\Exception;

class OrderRequestForm extends BaseRequest
{

    public OrderRequestResource $model;

    public $order_id;
    public $price;

    public function __construct(OrderRequestResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [['order_id', 'price'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['order_id'], 'exist', 'skipOnError' => true, 'targetClass' => Order::class, 'targetAttribute' => ['order_id' => 'id']],
        ];
    }


    /**
     * @throws \yii\db\Exception
     * @throws Exception
     * @throws \Exception
     */
    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();

        $companyId = \Yii::$app->user->identity->company_id;
        $company = Yii::$app->user->identity->company;
        $this->price = $this->price * 100;
        $order = Order::findOne($this->order_id);
        $_company = $order->user->company; // customer company
        $product = Product::findOne($order->product_id);
        $oldPrice = OrderRequest::find()->andWhere(['order_id' => $order->id])->orderBy(['price' => SORT_DESC])->one();
        //TODO oldin qatnashganmi yoqmi tekshiradi

        $orderRequest = OrderRequest::find()->andWhere(['company_id' => $companyId])->andWhere(['order_id' => $this->order_id]);

        if ($this->price >= $oldPrice->price) {
            throw new Exception(t("Siz taklif qilayotgan summa boshlang'ich summadan katta"));
        }
        if ($orderRequest->exists() && $product->company_id != $companyId) {
            throw new Exception(t("Siz oldin bu savdoda qatnashgansz"));
        }
        if ($orderRequest->exists() && ($orderRequest->count() > 1 && $product->company_id == $companyId)) {
            throw new Exception(t("Bu sizning mahsulotingiz bo'lgani uchun savdoda qatnashgansz"));
        }

        //TODO check time
        if ($order->request_end < date('Y-m-d H:i:s')) {
            throw new Exception(t("Vaqt tugadi"));
        }
        //TODO check balance

        $bhm = Bmh::find()->andWhere(['<', 'start_date', date("Y-m-d")])->andWhere(['>=', 'end_date', date("Y-m-d")])->one();

        $zalog = $order->total_sum * env('SHOP_ZALOG_PERSENT', 0.03);
        $commission = $this->price * env('COMMISSION_PERCENT', 0.0015);
        $companyBalance = CompanyVirtualAccount::findOneByPrefixAndCompany(OperationTypeEnum::P_K_30101, $companyId);
        $total_block_sum = $zalog + $commission;
        if ($companyBalance && ($companyBalance->price < $total_block_sum)) {
            throw new Exception(t("Balansda yetarli mablag' mavjud emas"));
        }
        $this->model->company_id = $companyId;
        $this->model->is_winner = 0;
        $this->model->created_at = date('Y-m-d H:i:s');
        $this->model->status = ShopEnum::ORDER_REQUEST_STATUS_DONE;
        $this->model->type = ShopEnum::ORDER_REQUEST_TYPE_PRODUCER;
        $att = $this->attributes;
        $this->model->setAttributes($att, false);
        if ($this->model->attributes && $this->model->validate() && $this->model->save()) {

//            if ($product->company_id==$companyId)
//            {
//            $zalog_transaction = CompanyTransaction::findOne([ // shu zapros bo'yicha oldin zalog olinganmi
//                'company_id' => $companyId,
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                'type' => CompanyTransactionEnum::TYPE_ZALOG,
//                'order_id' => $order->id
//            ]);
            $zalog_transaction = VirtualTransactionResource::findOneByCreditCompanyIDAndProductAndTypeAndNotRefunded(
                $companyId,
                OperationTypeEnum::PRODUCT_NAME_ORDER,
                $this->model->order_id,
                OperationTypeEnum::BLOCK_SALE_DEPOSIT
            );

//            $kommision_block_transaction = CompanyTransaction::find()->where([ // shu zapros bo'yicha oldin komisiya olinganmi
//                'company_id' => $companyId,
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION,
//                'order_id' => $order->id
//            ])->orderBy("id desc")->one();

//            $kommision_block_transaction = VirtualTransaction::find()->where([
//                'debit_company_id' => $companyId,
//                'order_id' => $order->id,
//                'type' => OperationTypeEnum::BLOCK_SALE_COMMISSION
//            ])->orderBy("id desc")->one();
            $kommision_block_transaction = VirtualTransactionResource::findOneByCreditCompanyIDAndProductAndTypeAndNotRefunded(
                $companyId,
                OperationTypeEnum::PRODUCT_NAME_ORDER,
                $this->model->order_id,
                OperationTypeEnum::BLOCK_SALE_COMMISSION
            );
//            }

            // TODO DEMPING
            $demping = false;
            if ($this->price * 100 / $order->total_sum <= env('SHOP_DEMPING_PERSENT', 80)) {
                $zalog = $zalog + $order->total_sum - $this->price;

                if ($order->total_sum <= 2500 * $bhm->price) {
                    $zalog = $zalog > $bhm->price * 100 ? $bhm->price * 100 : $zalog;
                } else {
                    $zalog = $zalog > $bhm->price * 1000 ? $bhm->price * 1000 : $zalog;
                }
                $demping = true;
            }
            //TODO oldin zalog va komissiya saqlanmagan bolsa
            if (!$zalog_transaction && !$kommision_block_transaction) {
                //TODO Balancedan yechib olish
//                $company_transaction_zalog = new CompanyTransaction([
//                    'company_id' => $companyId,
//                    'contract_id' => null,
//                    'order_id' => $order->id,
//                    'amount' => $zalog,
//                    'type' => CompanyTransactionEnum::TYPE_ZALOG,
//                    'description' => Yii::t("main", "Mahsulotga narx so'rovi uchun garov bandlandi") . $demping === true ? ", demping" : "",
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => date("Y-m-d H:i:s"),
//                ]);
//
//                $company_transaction_commission = new CompanyTransaction([
//                    'company_id' => $companyId,
//                    'contract_id' => null,
//                    'order_id' => $order->id,
//                    'amount' => $commission,
//                    'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION,
//                    'description' => Yii::t("main", "Mahsulotga narx so'rovi berilgani uchun komisiya summasi bandlandi"),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => date("Y-m-d H:i:s"),
//                ]);
//                if (!($company_transaction_commission->save() && $company_transaction_zalog->save())) {
//                    $transaction->rollBack();
//                    throw new \Exception($company_transaction_commission->getErrors());
//                }
                Virtualtransaction::saveTransaction(
                    $company,
                    $company,
                    OperationTypeEnum::P_K_30101,
                    OperationTypeEnum::P_K_30201,
                    $zalog,
                    Yii::t("main", "Mahsulotga narx so'rovi uchun garov bandlandi"),
                    OperationTypeEnum::PRODUCT_NAME_ORDER,
                    $this->model->order_id,
                    null,
                    OperationTypeEnum::BLOCK_SALE_DEPOSIT,
                );

                Virtualtransaction::saveTransaction(
                    $company,
                    $company,
                    OperationTypeEnum::P_K_30101,
                    OperationTypeEnum::P_K_30202,
                    $commission,
                    Yii::t("main", "Mahsulotga narx so'rovi berilgani uchun komisiya summasi bandlandi"),
                    OperationTypeEnum::PRODUCT_NAME_ORDER,
                    $this->model->order_id,
                    null,
                    OperationTypeEnum::BLOCK_SALE_COMMISSION,
                );
            } else {

                //TODO Demping bolsa

                if ($demping && $zalog_transaction) {

                    if (($companyBalance->price + $zalog_transaction->credit) >= $zalog) {

                        //TODO olgindi zalogni qaytarib yangi saqlanadi
                        $creditID = Virtualtransaction::saveTransaction(
                            $company,
                            $company,
                            OperationTypeEnum::P_K_30201,
                            OperationTypeEnum::P_K_30101,
                            $zalog_transaction->credit,
                            t("Zalog qaytarildi. OrderRequestForm"),
                            OperationTypeEnum::PRODUCT_NAME_ORDER,
                            $this->model->order_id,
                            null,
                            OperationTypeEnum::UNBLOCK_SALE_DEPOSIT,
                        );
                        $zalog_transaction->parent_id = $creditID;
                        if (!$zalog_transaction->save())
                            throw new \Exception($zalog_transaction->errors);

//                        $revert_zalog_transaction = new CompanyTransaction([
//                            'company_id' => $zalog_transaction->company_id,
//                            'amount' => $zalog_transaction->amount,
//                            'order_id' => $zalog_transaction->order_id,
//                            'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
//                            'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                            'transaction_date' => date("Y-m-d H:i:s"),
//                            'description' => Yii::t("main", "Zalog qaytdi, 185"),
//                        ]);
//                        if ($revert_zalog_transaction->save()) {
//                            $zalog_transaction->reverted_id = $revert_zalog_transaction->id;
//                            $zalog_transaction->save(false);
//
//                            $new_zalog_transaction = new CompanyTransaction([
//                                'company_id' => Yii::$app->user->identity->company_id,
//                                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                                'type' => CompanyTransactionEnum::TYPE_ZALOG,
//                                'order_id' => $order->id,
//                                'amount' => $zalog,
//                                'transaction_date' => date("Y-m-d H:i:s"),
//                                'description' => Yii::t("main", "Mahsulotga narx so'rovi uchun qayta zalog bandlandi, demping"),
//                            ]);
//
//                            if (!$new_zalog_transaction->save()) {
//                                throw new \Exception($new_zalog_transaction->getErrors());
//                            }
//                        }
                        VirtualTransaction::saveTransaction(
                            $company,
                            $company,
                            OperationTypeEnum::P_K_30101,
                            OperationTypeEnum::P_K_30201,
                            $zalog,
                            t("Mahsulotga narx so'rovi uchun qayta zalog bandlandi, demping"),
                            OperationTypeEnum::PRODUCT_NAME_ORDER,
                            $this->model->order_id,
                            null,
                            OperationTypeEnum::BLOCK_SALE_DEPOSIT,
                        );
                    }
                }
                // $kommision_block_transaction->currency = $kommisiya;
                if ($kommision_block_transaction) {
                    $commissionCreditID = Virtualtransaction::saveTransaction(
                        $company,
                        $company,
                        OperationTypeEnum::P_K_30202,
                        OperationTypeEnum::P_K_30101,
                        $kommision_block_transaction->credit,
                        Yii::t("main","Qayta so'rov uchun komissiya qaytarildi lot#{lot} umumiy summa:{price}",["lot" => $order->id, "price" => $this->price]),
                        OperationTypeEnum::PRODUCT_NAME_ORDER,
                        $this->model->order_id,
                        null,
                        OperationTypeEnum::UNBLOCK_SALE_COMMISSION,
                    );
                    $kommision_block_transaction->parent_id = $commissionCreditID;
                    if (!$kommision_block_transaction->save())
                        throw new \Exception($kommision_block_transaction->errors);
//                    $kommision_block_transaction_revert = new CompanyTransaction([
//                        'company_id' => $companyId,
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'type' => CompanyTransactionEnum::TYPE_REVERT_BLOCK_COMMISION,
//                        'order_id' => $order->id,
//                        'amount' => $kommision_block_transaction->amount,
//                        'transaction_date' => date("Y-m-d H:i:s"),
//                        'description' => Yii::t("main", "Qayta so'rov uchun komissiya qaytarildi lot#") . $order->id . t(" umumiy summa:") . $this->price,
//                    ]);
//
//                    if ($kommision_block_transaction_revert->save()) {
//                        $kommision_block_transaction->reverted_id = $kommision_block_transaction_revert->id;
//                        $kommision_block_transaction->save(false);
//                    }
                }
                Virtualtransaction::saveTransaction(
                    $company,
                    $company,
                    OperationTypeEnum::P_K_30101,
                    OperationTypeEnum::P_K_30202,
                    $commission,
                    Yii::t("main","Taklif berilgani uchun kommisiya saqlandi lot#{lot} umumiy summa:{price}",["lot" => $order->id, "price" => $this->price]),
                    OperationTypeEnum::PRODUCT_NAME_ORDER,
                    $this->model->order_id,
                    null,
                    OperationTypeEnum::BLOCK_SALE_COMMISSION,
                );
//                $kommision_block_transaction_new = new CompanyTransaction([
//                    'company_id' => $companyId,
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION,
//                    'order_id' => $order->id,
//                    'amount' => $commission,
//                    'transaction_date' => date("Y-m-d H:i:s"),
//                    'description' => Yii::t("main", "Taklif berilgani uchun kommisiya saqlandi lot#") . $order->id . t(" umumiy summa:") . $this->price,
//                ]);
//
//                if (!$kommision_block_transaction_new->save()) {
//                    throw new \Exception($kommision_block_transaction_new->getErrors());
//                }

            }


            $transaction->commit();
            return true;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}