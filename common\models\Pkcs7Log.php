<?php

namespace common\models;

use backend\modules\system\models\SystemLog;
use Exception;
use Yii;

/**
 * This is the model class for table "pkcs7_log".
 *
 * @property int $id
 * @property string|null $created_at
 * @property string|null $update_at
 * @property string|null $deleted_at
 * @property int|null $object_id
 * @property string|null $pksc7
 * @property string|null $json_body
 * @property int|null $user_id
 * @property int|null $pkcs7_type
 * @property int|null $company_tin
 */
class Pkcs7Log extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'pkcs7_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
//            [['deleted_at', 'object_id', 'pksc7', 'json_body', 'user_id', 'pkcs7_type', 'company_tin'], 'default', 'value' => null],
//            [['created_at', 'update_at', 'deleted_at', 'json_body'], 'safe'],
//            [['object_id', 'user_id', 'pkcs7_type', 'company_tin'], 'integer'],
//            [['pksc7'], 'string'],
            [['object_id', 'user_id', 'pksc7', 'json_body',], 'required'],
            [['deleted_at', 'object_id', 'pksc7', 'json_body', 'user_id', 'pkcs7_type', 'company_tin'], 'default', 'value' => null],
            [['created_at', 'update_at', 'deleted_at', 'json_body'], 'safe'],
            [['object_id', 'user_id', 'company_tin'], 'integer'],
            [['pksc7','pkcs7_type',], 'string'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'created_at' => 'Created At',
            'update_at' => 'Update At',
            'deleted_at' => 'Deleted At',
            'object_id' => 'Object ID',
            'pksc7' => 'Pksc7',
            'json_body' => 'Json Body',
            'user_id' => 'User ID',
            'pkcs7_type' => 'Pkcs7 Type',
            'company_tin' => 'Company Tin',
        ];
    }

    /**
     * @throws \yii\db\Exception
     */
    public static function create($result, $pkcs7, $params, $pkcs_type = null)
    {
        try {
            $user = Yii::$app->user->identity;
            if (!$user)
                throw new Exception("User not found");
            if (!$result)
                throw new Exception("Object ID not found");
            if (!$pkcs7)
                throw new Exception("Invalid pkcs7");
            if (!$pkcs_type)
                throw new Exception("Pkcs type cannot be empty");

            $body = json_encode($params, JSON_UNESCAPED_UNICODE);
            $pkcsModel = new Pkcs7Log([
                'object_id' => $result,
                'user_id' => $user->id,
                'json_body' => $body,
                'pksc7' => $pkcs7,
                'pkcs7_type' => $pkcs_type,
                'company_tin' => $user->company->tin,
            ]);
            if (!$pkcsModel->save()) {
                Yii::$app->response->statusCode = 422;
                SystemLog::create($pkcsModel->getErrors(),'ValidationError');
            }
        } catch (\Exception $e) {
            Yii::$app->response->statusCode = 500;
            SystemLog::create($e->getMessage());
        }
    }
}
