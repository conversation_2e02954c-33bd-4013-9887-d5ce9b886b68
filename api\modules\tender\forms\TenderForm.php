<?php


namespace api\modules\tender\forms;


use api\components\BaseModel;
use api\components\BaseRequest;
use api\modules\common\resources\DistrictResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\RegionResource;
use api\modules\tender\resources\CommissionGroupMemberResource;
use api\modules\tender\resources\CommissionGroupResource;
use api\modules\common\resources\FileResource;
use api\modules\tender\resources\TenderClassifierCreateResource;
use api\modules\tender\resources\TenderCommissionMemberResource;
use api\modules\tender\resources\TenderQualificationFileListResource;
use api\modules\tender\resources\TenderRequirementsResource;
use api\modules\tender\resources\TenderResource;
use common\enums\CompanyEnum;
use common\enums\TenderEnum;
use common\models\Bmh;
use common\models\CommissionGroup;
use common\models\TenderFiles;
use Yii;

class TenderForm extends BaseRequest
{

    public TenderResource $model;

    public $pkcs7;
    public $type;
    public $title;
    public $preference_local_producer;
    public $language;
    public $placement_period;
    public $funding_source;
    public $purchase_currency;
    public $description;
    public $amount_deposit;
    public $accept_guarantee_bank;
    public $method_payment;
    public $advance_payment_percentage;
    public $advance_payment_period;
    public $contact_file_id;
    public $unblocking_type;
    public $contact_position;
    public $contact_fio;
    public $contact_phone;
    public $plan_schedule_id;
    public $region_id;
    public $district_id;
    public $address;
    public $delivery_phone;
    public $criteria_evaluation_proposals;
    public $min_passing_ball;
    public $technical_part;
    public $price_part;
    public $expertise_conclusion_number;
    public $expertise_conclusion_date;

    public $products = [];
    public $requirements = [];
    public $qualifications = [];
    public $files = [];
    public $commission_group_id;

    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        $parent = parent::rules();
        $child = [
            [['type', 'preference_local_producer', 'language', 'placement_period',
                'purchase_currency', 'amount_deposit', 'accept_guarantee_bank', 'method_payment',
                'unblocking_type', 'region_id', 'district_id',
                'title', 'funding_source', 'description',
                'contact_position', 'contact_fio', 'contact_phone', 'address', 'delivery_phone', 'requirements',
                'commission_group_id', 'criteria_evaluation_proposals',
                'min_passing_ball', //'contact_file_id', 'technical_task_file_id', 'technical_document_file_id',
            ], 'required', 'message' => '{attribute} maydoni yuborish majburiy'],
            [['preference_local_producer', 'language',
                'contact_file_id', 'advance_payment_period',
                'plan_schedule_id', 'region_id', 'district_id', 'commission_group_id'], 'integer'],
            [['accept_guarantee_bank', 'unblocking_type'], 'integer', 'min' => 0, 'max' => 1],
            [['title', 'contact_position', 'contact_fio', 'contact_phone', 'address', 'delivery_phone', 'expertise_conclusion_number'], 'string', 'max' => 255],
            [['products', 'requirements', 'qualifications', 'files', 'expertise_conclusion_date'], 'safe'],
            [['contact_file_id'], 'exist', 'skipOnError' => true, 'targetClass' => FileResource::class, 'targetAttribute' => ['contact_file_id' => 'id']],
            [['region_id'], 'exist', 'skipOnError' => true, 'targetClass' => RegionResource::class, 'targetAttribute' => ['region_id' => 'id']],
            [['district_id'], 'exist', 'skipOnError' => true, 'targetClass' => DistrictResource::class, 'targetAttribute' => ['district_id' => 'id']],
            [['commission_group_id'], 'exist', 'skipOnError' => true, 'targetClass' => CommissionGroupResource::class, 'targetAttribute' => ['commission_group_id' => 'id']],
            [['min_passing_ball', 'amount_deposit'], 'number'],
            [['advance_payment_percentage'], 'number', 'min' => 15, 'max' => 85],
            [['funding_source', 'description'], 'string', 'max' => 1025],
            ['type', 'checkType'],
            ['purchase_currency', 'checkPurchaseCurrency'],
            ['amount_deposit', 'checkDeposit'],
            ['method_payment', 'checkMethodPayment'],
            ['criteria_evaluation_proposals', 'checkCriteria'],
            [['advance_payment_percentage', 'advance_payment_period'], 'required', 'when' => function ($model) {
                return $this->method_payment == TenderEnum::ADVANCE_PAYMENT;
            }],
            [['expertise_conclusion_number', 'expertise_conclusion_date'], 'required', 'when' => function ($model) {
                return $this->type == TenderEnum::TYPE_TENDER;
            }],
            [['technical_part', 'price_part'], 'required', 'when' => function ($model) {
                return $this->criteria_evaluation_proposals == TenderEnum::CRITERIA_EVALUATION_PROPOSALS_BALLING;
            }],
            [['technical_part', 'price_part'], 'checkPassingBal'],
            [['placement_period'], 'checkPlacementPeriod'],

            ['placement_period', 'integer', 'min' => 5, 'max' => 30, 'message' => t("Joylashtirish muddati 5-30 kun oralig'ida bo'lishi kerak")],
        ];
        return array_merge($parent, $child);
    }


    public function checkType($attribute, $params)
    {
        if ($this->type == TenderEnum::TYPE_TENDER || $this->type == TenderEnum::TYPE_INVITE) {
            return true;
        } else {
            $this->addError('type', t("Joylashtirish turi qiymatlari noto'g'ri yuborildi"));

            return false;
        }
    }

    public function checkCriteria()
    {
        if ($this->criteria_evaluation_proposals == TenderEnum::CRITERIA_EVALUATION_PROPOSALS_MIN_PRICE || $this->criteria_evaluation_proposals == TenderEnum::CRITERIA_EVALUATION_PROPOSALS_BALLING) {
            return true;
        } else {
            $this->addError('criteria_evaluation_proposals', t("Takliflarni baholash me'zoni qiymatlari noto'g'ri yuborildi"));
            return false;
        }
    }

    public function checkPassingBal()
    {
        if($this->criteria_evaluation_proposals == TenderEnum::CRITERIA_EVALUATION_PROPOSALS_BALLING){
            $min = $this->technical_part;
            $max = $this->price_part;
            if (($max + $min) == 100) {
                if ($min >= 30 && $min <= 70) {
                    return true;
                } else {
                    $this->addError('price_part', t("Texnik qismi ulushi qiymatlari 30%-70% orasi bo'lishi kerak"));
                    return false;
                }
            } else {
                $this->addError('price_part', t("Narx qismi va Texnik qismi ulushlari birgalikda 100% bo'lishi kerak"));
                return false;
            }
        } else {
            return true;
        }

    }

    public function checkPurchaseCurrency()
    {
        if (in_array($this->purchase_currency, TenderEnum::PURCHASE_CURRENCY_LIST)) {
            return true;
        } else {
            $this->addError("purchase_currency", t("Xarid qilish valyutasi maydoni qiymatlari noto'g'ri yuborildi"));
            return false;
        }
    }

    public function checkMethodPayment()
    {
        if (in_array($this->method_payment, TenderEnum::METHOD_PAYMENT_LIST)) {
            return true;
        } else {
            $this->addError("method_payment", t("To'lov tartibi maydoni qiymatlari noto'g'ri yuborildi"));
            return false;
        }
    }

    public function checkPlacementPeriod()
    {
        if ($this->type == TenderEnum::TYPE_TENDER) {

            if ($this->placement_period >= TenderEnum::PERIOD_TENDER_MIN && $this->placement_period <= TenderEnum::PERIOD_TENDER_MAX) {
                return true;
            } else {
                $this->addError("placement_period", t("Joylashtirish muddati 12-30 ish kuni oralig'ida bo'lishi kerak"));
            }

        } else {

            if ($this->placement_period >= TenderEnum::PERIOD_SELECTION_MIN && $this->placement_period <= TenderEnum::PERIOD_SELECTION_MAX) {
                return true;
            } else {
                $this->addError("placement_period", t("Joylashtirish muddati 5-12 ish kuni oralig'ida bo'lishi kerak"));
            }
        }
        return false;
    }

    public function checkDeposit()
    {
        if ($this->type == TenderEnum::TYPE_TENDER) {
            if ($this->amount_deposit >= 0 and $this->amount_deposit <= TenderEnum::DEPOSIT_TENDER_MAX) {
                return true;
            } else {
                $this->addError("amount_deposit", Yii::t("main", "Garov miqdori 0% - {val}% oraliqda bo'lishi kerak", ['val' => TenderEnum::DEPOSIT_TENDER_MAX]));
                return false;
            }
        } else {
            if ($this->amount_deposit >= 0 and $this->amount_deposit <= TenderEnum::DEPOSIT_INVITE_MAX) {
                return true;
            } else {
                $this->addError("amount_deposit", Yii::t("main", "Garov miqdori 0% - {val}% oraliqda bo'lishi kerak", [
                    'val' => TenderEnum::DEPOSIT_INVITE_MAX
                ]));
                return false;
            }
        }

    }

    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        $user = \Yii::$app->user->identity;

        $companyId = $user->company_id;
        $company = $user->company;
        $this->model->company_id = $companyId;
        $this->model->state = TenderEnum::STATE_NEW;
        $this->model->status = TenderEnum::STATUS_NEW;

        $att = $this->attributes;
        $this->model->setAttributes($att, false);
        if($this->model->expertise_conclusion_date){
            $this->model->expertise_conclusion_date = date("Y-m-d", strtotime($this->model->expertise_conclusion_date));
        }
        $isBudget = $user->isBudget;
        if ($this->model->attributes && $this->model->validate() && $this->model->save()) {
            if ($isBudget) {
                $this->model->organ = $user->organ;
            } else {
                if (!isset($this->plan_schedule_id) || $this->plan_schedule_id == null) {
                    $this->addError("plan_schedule_id", Yii::t("main", "Plan Schedule Id maydoni yuborish majburiy"));
                    return false;
                }
            }
            $tenderId = $this->model->id;
            $this->model->lot = $this->model->generateLotNumber($tenderId);
            if (!$this->model->save(false)) {
                $transaction->rollBack();
                $this->addErrors($this->model->errors);
                return false;
            }

            $group = CommissionGroup::findOne(['id' => $this->commission_group_id, 'company_id' => $companyId]);
            if (!$group) {
                $this->addError('error', t("Guruh topilmadi"));
                $transaction->rollBack();
                return false;
            }
            $commission_members = CommissionGroupMemberResource::find()->where(['commission_group_id' => $group->id, 'status' => TenderEnum::STATUS_ACTIVE])->all();
            foreach ($commission_members as $members) {
                $tenderCommissionMember = new TenderCommissionMemberResource();

                $tenderCommissionMember->commission_group_member_id = $members->id;
                $tenderCommissionMember->commission_member_id = $members->commission_member_id;
                $tenderCommissionMember->role = $members->role;
                $tenderCommissionMember->tender_id = $tenderId;
                $tenderCommissionMember->company_id = $companyId;
                $tenderCommissionMember->status = TenderEnum::STATUS_ACTIVE;
                if (!($tenderCommissionMember->validate() && $tenderCommissionMember->save())) {
                    $transaction->rollBack();

                    $this->addErrors($tenderCommissionMember->errors);
                    return false;
                }
            }

            foreach ($this->products as $product) {

                $cls = null;
                if(isset($product['select_product_name']) && $product['select_product_name'] != null && isset($product['select_product_name']['classifier'])){
                    $cls = $product['select_product_name']['classifier'];
                }
                $tenderProduct = new TenderClassifierCreateResource();
                $tenderProduct->setAttributes($product, false);
                if (isset($cls) && $cls != null && isset($cls['id'])) {
                    $tenderProduct->classifier_id = $cls['id'];
                } else {
                    $transaction->rollBack();
                    $this->addError("error", t("Maxsulot topilmadi"));
                    return false;
                }

                if (!isset($product['additional_charges_non_residents'])) {
                    $tenderProduct->additional_charges_non_residents = TenderEnum::NON_RESIDENT_ADDITIONAL_NO;
                }

                $tenderProduct->price = $tenderProduct->price * 100;
                $tenderProduct->tender_id = $tenderId;
                $tenderProduct->status = TenderEnum::STATUS_ACTIVE;
                if (!$tenderProduct->validate()) {
                    $transaction->rollBack();
                    $this->addErrors($tenderProduct->errors);
                    return false;
                }

                $planSchedule = PlanScheduleClassifierResource::find()
                    ->notDeleted()
                    ->andWhere(['classifier_id' => $tenderProduct->classifier_id, 'id' => $tenderProduct->plan_schedule_classifier_id, 'status' => TenderEnum::STATUS_ACTIVE]);
                if (!$isBudget) {
                    $planSchedule->andWhere(['plan_schedule_id' => $this->plan_schedule_id]);
                }
                $planSchedule = $planSchedule->one();
                if ($planSchedule == null) {
                    $transaction->rollBack();
                    $this->addError("products", t("Maxsulot aktiv xolatda topilmadi"));

                    return false;
                }
                if ($tenderProduct->number_purchased > $planSchedule->count_live || $tenderProduct->number_purchased <= 0) {
                    $transaction->rollBack();

                    $this->addError("products", t("Xarid qilinayotgan tovar soni, xarid jadvalidagi qolgan maxsulot soniga mutanosib emas"));
                    return false;
                }

                if (!$tenderProduct->save()) {
                    $transaction->rollBack();

                    $this->addErrors($tenderProduct->errors);
                    return false;
                }
            }

            /*
            tanlov :
            max qiymat : 340,000 * 25000 = 8***********

            tanlov(byudjet)
            max qiymat: 340,000 * 6000 = 2 ***********

            

            tender :
            min qiymat : 340,000 * 25000 = 8 ***********

            tender(byudjet)
            min qiymat: 340,000 * 6000 = 2 ***********

            */

            $bmh = Bmh::getAmount();
            $price = $this->model->tenderTotalPrice;
            $isTender = $this->model->type == TenderEnum::TYPE_TENDER;
            $isNoByudjet = $company->organization_type == CompanyEnum::NO_BYUDJET;

            $limitMultiplier = $isNoByudjet ? 25000 : 6000;
            $limit = $bmh * $limitMultiplier;

            if ($isTender) {
                if ($price < $limit) {
                    $transaction->rollBack();
                    $this->addError("error", Yii::t("main",
                        "Tovarlarning (ishlarning, xizmatlarning) qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining {limit} baravaridan ortiq miqdorni tashkil etadi",
                        ['limit' => $limitMultiplier]
                    ));
                    return false;
                }
            } else {
                if ($price > $limit) {
                    $transaction->rollBack();
                    $this->addError("error", Yii::t("main",
                        "Tovarlarning (ishlarning, xizmatlarning) qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining {limit} baravarigacha bo‘lgan miqdorni tashkil etadi",
                        ['limit' => $limitMultiplier]
                    ));
                    return false;
                }
            }


            foreach ($this->requirements as $requirement) {
                $tenderRequirement = new TenderRequirementsResource();
                $tenderRequirement->setAttributes($requirement, false);
                $tenderRequirement->tender_id = $tenderId;
                $tenderRequirement->status = TenderEnum::STATUS_ACTIVE;

                if (!($tenderRequirement->obligation == 1 || $tenderRequirement->obligation == 2)) {
                    $this->addError('obligation', t("obligation qiymati noto'g'ri"));
                    $transaction->rollBack();
                    return false;
                }

                if ($tenderRequirement->file_uploading_necessary) {
                    if (!isset($tenderRequirement->file_name) || $tenderRequirement->file_name == null) {
                        $this->addError('file_name', t("file_name yuborish kerak"));
                        $transaction->rollBack();
                        return false;
                    }
                }
                if (!in_array($tenderRequirement->type, [TenderEnum::REQUIREMENT_TYPE_TEXT, TenderEnum::REQUIREMENT_TYPE_NUMBER, TenderEnum::REQUIREMENT_TYPE_BINARY])) {
                    $this->addError('type', t("type qiymati noto'g'ri"));
                    $transaction->rollBack();
                    return false;
                }


                if ($tenderRequirement->type == TenderEnum::REQUIREMENT_TYPE_NUMBER) {
                    $tenderRequirement->evaluation_method = 'system';

                    if (!isset($tenderRequirement->value_condition) || $tenderRequirement->value_condition == null || !in_array($tenderRequirement->value_condition, [1, 2, 3, 4, 5])) {
                        $this->addError('value_condition', t("value_condition yuborish kerak"));
                        $transaction->rollBack();
                        return false;
                    }

                    if (!isset($tenderRequirement->unit) || $tenderRequirement->unit == null) {
                        $this->addError('unit', t("unit yuborish kerak"));
                        $transaction->rollBack();
                        return false;
                    }

                    if (in_array($tenderRequirement->value_condition, [1, 2, 3])) {
                        if (!isset($tenderRequirement->value) || $tenderRequirement->value == null) {
                            $this->addError('value', t("value yuborish kerak"));
                            $transaction->rollBack();
                            return false;
                        }
                    } else {
                        if (!isset($tenderRequirement->value_from) || $tenderRequirement->value_from == null) {
                            $this->addError('value_from', t("value_from yuborish kerak"));
                            $transaction->rollBack();
                            return false;
                        }

                        if (!isset($tenderRequirement->value_to) || $tenderRequirement->value_to == null) {
                            $this->addError('value_to', t("value_to yuborish kerak"));
                            $transaction->rollBack();
                            return false;
                        }

                        if ($tenderRequirement->value_to < $tenderRequirement->value_from) {
                            $this->addError('value_to', t("value_from <= value_to bo'lishi kerak"));
                            $transaction->rollBack();
                            return false;
                        }
                    }


                } else {
                    $tenderRequirement->evaluation_method = 'expert';
                    if ($tenderRequirement->type == TenderEnum::REQUIREMENT_TYPE_BINARY) {
                        if (!isset($tenderRequirement->value_binary) || $tenderRequirement->value_binary == null) {
                            $this->addError('value_binary', t("value_binary yuborish kerak"));
                            $transaction->rollBack();
                            return false;
                        } elseif (!in_array($tenderRequirement->value_binary, [1, 2])) {
                            $this->addError('value_binary', t("value_binary qiymati xato yuborildi"));
                            $transaction->rollBack();
                            return false;
                        }
                    }
                }

                if (!($tenderRequirement->validate() && $tenderRequirement->save())) {
                    $transaction->rollBack();

                    $this->addError('requirements', $tenderRequirement->errors);
                    return false;
                }

            }

            foreach ($this->qualifications as $title) {
                if ($title == null || $title == '') {
                    $transaction->rollBack();
                    $this->addError('qualifications', "Qiymat yuborilmagan {title}");
                    return false;
                }
                $tenderRequirements = new TenderQualificationFileListResource();
                $tenderRequirements->title = $title;
                $tenderRequirements->tender_id = $tenderId;
                $tenderRequirements->company_id = $companyId;
                $tenderRequirements->status = TenderEnum::STATUS_ACTIVE;

                if (!($tenderRequirements->validate() && $tenderRequirements->save())) {
                    $transaction->rollBack();

                    $this->addError('qualifications', $tenderRequirements->errors);
                    return false;
                }

            }

            foreach ($this->files as $item) {
                if ($item == null || !isset($item['id']) || $item['id'] == null || !isset($item['name']) || $item['name'] == null) {
                    $transaction->rollBack();
                    $this->addError('files', "Qiymat yuborilmagan {files}");
                    return false;
                }
                $file = FileResource::findOne($item['id']);
                if ($file == null) {
                    $transaction->rollBack();
                    $this->addError('file', t("File topilmadi"));
                    return false;
                }
                $tenderRequirements = new TenderFiles();
                $tenderRequirements->title = $item['name'];
                $tenderRequirements->tender_id = $tenderId;
                $tenderRequirements->file_id = $file->id;
                $tenderRequirements->status = TenderEnum::STATUS_ACTIVE;

                if (!($tenderRequirements->validate() && $tenderRequirements->save())) {
                    $transaction->rollBack();
                    $this->addError('files', $tenderRequirements->errors);
                    return false;
                }

            }


            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}