<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\shop\resources\OrderRequestResource;
use api\modules\shop\resources\OrderResource;
use Yii;

class OrderFinishedFilter extends BaseRequest
{
    public $pageNo = 0;
    public $pageSize = 10;
    public function rules()
    {
        return [
            [['pageNo', 'pageSize'], 'integer'],
        ];
    }

    public function getResult()
    {

        $model = OrderResource::find()
        ->leftJoin(OrderRequestResource::tableName(),OrderRequestResource::tableName().'.order_id'."=".OrderResource::tableName().".id")
        ->andWhere([OrderRequestResource::tableName().'.is_winner'=>1])
        ->andWhere([
        'or',
        ['order.company_id' => Yii::$app->user->identity->company_id],
        ['order_request.company_id' => Yii::$app->user->identity->company_id]
           ]);
        $model->orderBy('order.id desc');
        return paginate($model);
    }
}
