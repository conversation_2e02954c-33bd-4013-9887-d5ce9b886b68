<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%product_draft_classifier_unit}}`.
 */
class m250717_113910_create_product_draft_classifier_unit_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%product_draft_classifier_unit}}', [
            'id' => $this->primaryKey(),
            'product_id' => $this->integer(),
            'classifier_id' => $this->integer(),
            'classifier_properties_id' => $this->integer(),
        ]);

        $this->addForeignKey("fk-product_draft_classifier_unit_classifier", 'product_draft_classifier_unit', 'classifier_id', 'classifier', 'id', 'cascade', 'cascade');
        $this->addForeignKey("fk-product_draft_classifier_unit_product", 'product_draft_classifier_unit', 'product_id', 'product_draft', 'id', 'cascade', 'cascade');
        $this->addFore<PERSON><PERSON><PERSON>("fk-product_draft_classifier_unit_classifier_properties", 'product_draft_classifier_unit', 'classifier_properties_id', 'classifier_properties', 'id', 'cascade', 'cascade');
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%product_draft_classifier_unit}}');
    }
}
