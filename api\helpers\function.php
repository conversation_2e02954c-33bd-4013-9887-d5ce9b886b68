<?php

function getTranslate($translate)
{
    return Yii::t('main', $translate) !== "" ? Yii::t('main', $translate) : $translate;
}

function t($translate)
{
    return getTranslate($translate);
}
function addDaysExcludingWeekends($date, $daysToAdd, array $workDays = [], array $offDays = [], $isGrow = true): string
{
    $resultDate = new DateTime($date);
    $addedDays = 0;

    // Set-based lookup for O(1) performance

    while ($addedDays < $daysToAdd) {
        $resultDate->modify( $isGrow ? '+1 day' : '-1 day');
        $currentDate = $resultDate->format('Y-m-d');
        $dayOfWeek = $resultDate->format('N'); // 1=Mon, ..., 7=Sun

        $isWeekend = $dayOfWeek >= 6;
        $isWorkDay = isset($workDays[$currentDate]);
        $isOffDay = isset($offDays[$currentDate]);

        if ((!$isWeekend || $isWorkDay) && !$isOffDay) {
            $addedDays++;
        }
    }

    return $resultDate->format('Y-m-d H:i:s');
}

if (!function_exists('startWorkDay'))
{
    /**
     * @throws DateMalformedStringException
     * @throws \yii\base\Exception
     */
    function startWorkDay($date,array $workDay = [],array $offDays = []): string
    {
        $resultDate = new DateTime($date);
        for ($i = 0; 0 < 1; $i++) {
            $currentDate = $resultDate->format('Y-m-d');
            $dayOfWeek = $resultDate->format('N'); // 1=Mon, ..., 7=Sun

            $isWeekend = $dayOfWeek >= 6;
            $isOffDay = isset($offDays[$currentDate]);
            $isWorkDay = isset($workDays[$currentDate]);
            if (($isWeekend || $isOffDay) && !$isWorkDay) {
                $resultDate->modify('+1 day');
            } else {
                break;
            }
        }
        return $resultDate->format('Y-m-d H:i:s');
    }
}
function dd()
{
    $args = func_get_args();
    if (count($args) == 0) {
        exit();
    }
    echo "<pre>";
    foreach ($args as $arg) {
        print_r($arg);
    }
    exit();
}


function checkPkcs($pkcs7)
{
    $auth_url = "http://*************:9090/backend/auth";


    $user_ip = empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['REMOTE_ADDR'] : $_SERVER['HTTP_X_REAL_IP'];
    $host = $_SERVER['HTTP_HOST'];

    $headers = array('Host: ' . $host, 'X-Real-IP: ' . $user_ip);

    //$pkcs7 = $_POST['pkcs7'];
    //$keyId = $_POST['keyId'];

    $ch = curl_init();
    $postvars = $pkcs7;
    $url = $auth_url;
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);                //0 for a get request
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postvars);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
    curl_setopt($ch, CURLOPT_TIMEOUT, 20);
    $response = curl_exec($ch);
    $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    curl_close($ch);
    return $response;

}

function getFrontendTimestamp(string $pkcs7): string {
    $url = "http://*************:9090/frontend/timestamp/pkcs7";

    $headers = [
        'Content-Type: application/json',
        'X-Real-IP: *******',
        'Host: ebirja.uz',
    ];

    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_POSTFIELDS => $pkcs7,
        CURLOPT_CONNECTTIMEOUT => 5,
        CURLOPT_TIMEOUT => 10,
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($response === false || $httpCode >= 400) {
        throw new Exception("Xatolik: $error (HTTP $httpCode)");
    }

    $data = json_decode($response, true);

    if (!isset($data['pkcs7b64'])) {
        throw new Exception("pkcs7b64 maydoni topilmadi");
    }

    return $data['pkcs7b64'];
}


function verifyPkcs7(string $pkcs7, $user) {
    if (isEmpty($pkcs7)) {
        throw new Exception("Elektron kluch bilan tasdiqlanmagan");
    }

    $url = "http://*************:9090/backend/pkcs7/verify/attached";

    $user_ip = empty($_SERVER['HTTP_X_REAL_IP']) ? $_SERVER['REMOTE_ADDR'] : $_SERVER['HTTP_X_REAL_IP'];

    $headers = [
        'Content-Type: application/json',
        'X-Real-IP: ' . $user_ip,
        'Host: xarid.ebirja.uz',
    ];

    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_POSTFIELDS => $pkcs7,
        CURLOPT_CONNECTTIMEOUT => 5,
        CURLOPT_TIMEOUT => 20,
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    if ($response === false || $httpCode >= 400) {
        throw new Exception("HTTP xatolik: $curlError (kod: $httpCode)");
    }

    $json = json_decode($response, true);
    if (!isset($json['pkcs7Info']['documentBase64'])) {
        throw new Exception("Imzo tarkibida documentBase64 topilmadi");
    }

    authenticityCheck($json, $user);

    if (isset($json['status']) && $json['status'] !== 1) {
        throw new Exception("Imzo tekshiruvi muvaffaqiyatsiz: " . ($json['message'] ?? ''));
    }

    $decoded = base64_decode($json['pkcs7Info']['documentBase64']);
    if ($decoded === false) {
        throw new Exception("Base64 dekod xatoligi");
    }

    $data = json_decode($decoded, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("Dekodlangan JSON noto‘g‘ri: " . json_last_error_msg());
    }

    return $data;
}

/**
 * @throws Exception
 */
function authenticityCheck(array $verifyJsonResponse, $currentUser): void
{
    $signers = $verifyJsonResponse['pkcs7Info']['signers'][0] ?? null;
    if (!$signers || empty($signers['certificate'][0]['subjectInfo'])) {
        throw new Exception("Imzoda sertifikat ma'lumotlari mavjud emas");
    }

    $subjectInfo = $signers['certificate'][0]['subjectInfo'];
    $tin = $subjectInfo['1.2.860.********'] ?? null;
//    $pinfl = $subjectInfo['1.2.860.********'] ?? null;

    $username = $currentUser->username;

    if ($tin !== $username) {
        throw new Exception("Elektron raqamli imzo ma'lumotlari foydalanuvchiga mos emas.");
    }
}


function checkPkcsPing()
{

    $arr = array('Content-Type: text/xml; charset="UTF-8"', 'X-Real-IP:*******', 'Host:dxp.uz', 'X-Real-Host:dxp.uz');
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_HTTPHEADER, $arr);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_URL, "http://127.0.0.1:8080/ping");
    $response = curl_exec($ch);

    if (curl_errno($ch)) {
        $error_msg = curl_error($ch);

        return [
            'status' => 'error',
            'message' => $error_msg
        ];
    }

    return $response;
}

function toRoute($route, $scheme = false)
{
    return \yii\helpers\Url::toRoute($route);
}

function isDate($value)
{
    if (!$value) {
        return false;
    }

    try {
        new \DateTime($value);
        return true;
    } catch (\Exception $e) {
        return false;
    }
}

function shortName($fullName)
{
    // Ismni bo'sh joy orqali bo'lib olish
    $parts = explode(' ', $fullName);

    if (count($parts) < 2) {
        return $fullName; // Agar kamida 2 ta qism bo'lmasa, hech narsani o'zgartirmaslik
    }

    // Ismning birinchi harfini olish
    $firstInitial = mb_substr($parts[0], 0, 1, 'UTF-8') . '.';

    // Familiyani to'liq olish
    $lastName = $parts[1];

    return $firstInitial . ' ' . $lastName;
}

function isTin($tin) {
    try {
        if ($tin !== null && strlen($tin) === 9) {
            $sum = 0.0;
            $tin9 = substr($tin, 8, 1);

            $sum += floatval(substr($tin, 0, 1)) * 37.0;
            $sum += floatval(substr($tin, 1, 1)) * 29.0;
            $sum += floatval(substr($tin, 2, 1)) * 23.0;
            $sum += floatval(substr($tin, 3, 1)) * 19.0;
            $sum += floatval(substr($tin, 4, 1)) * 17.0;
            $sum += floatval(substr($tin, 5, 1)) * 13.0;
            $sum += floatval(substr($tin, 6, 1)) * 7.0;
            $sum += floatval(substr($tin, 7, 1)) * 3.0;

            $sum /= 11.0;
            $sum = floor((1.0 - ($sum - floor($sum))) * 9.0);

            if ($tin9 === substr(strval($sum), 0, 1)) {
                return true;
            }
        }
        return false;
    } catch (Exception $e) {
        return false;
    }
}

function isPinfl($pinfl) {
    if (!is_string($pinfl) || trim($pinfl) === '') {
        return false;
    }

    $pinfl = trim($pinfl);

    return preg_match('/^[0-9]{14}$/', $pinfl) &&
        $pinfl[0] !== '0' &&
        bccomp($pinfl, '0') > 0; // bccomp ishlatilmoqda katta sonlar uchun
}

function isEmpty($obj): bool
{
    if (is_null($obj)) {
        return true;
    }

    if (is_string($obj)) {
        return strlen($obj) === 0;
    }

    if (is_array($obj)) {
        return count($obj) === 0;
    }

    if ($obj instanceof Countable) {
        return count($obj) === 0;
    }

    if ($obj instanceof Traversable) {
        foreach ($obj as $_) {
            return false;
        }
        return true;
    }

    if (is_object($obj)) {
        return count(get_object_vars($obj)) === 0;
    }

    return false;
}

function getChallenge(){

    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => 'http://*************:9090/frontend/challenge',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'GET',
    ));

    $response = curl_exec($curl);

    curl_close($curl);

    return $response;
}

