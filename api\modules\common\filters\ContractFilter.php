<?php

namespace api\modules\common\filters;

use Yii;
use api\components\BaseModel;
use api\modules\common\resources\ContractResource;
use common\enums\ContractEnum;
use common\enums\OrderEnum;
use common\enums\UserEnum;
use yii\db\Exception;

class ContractFilter extends BaseModel
{
    public ?string $type = null;


    public function rules(): array
    {
        return [
            ['type', 'required'],
            ['type', 'in', 'range' => [ContractEnum::TYPE_E_SHOP, ContractEnum::TYPE_N_SHOP, ContractEnum::TYPE_AUCTION, ContractEnum::TYPE_TENDER, ContractEnum::TYPE_SELECTION]],
        ];
    }

    /**
     * @throws Exception
     */
    public function getResult()
    {
        $identity = Yii::$app->user->identity;
        if (!$identity)
            throw new Exception('Unauthorized');
        $user_type = $identity->user_type;

        $query = ContractResource::find()->with(['producer']);

        switch ($user_type) {
            case UserEnum::USER_TYPE_CUSTOMER:
                $query->andWhere(['customer_id' => $identity->id]);
                break;
            case UserEnum::USER_TYPE_SUPPLIER:
                $query->andWhere(['producer_id' => $identity->id]);
                break;
            default:
                $query->andWhere('0=1');
        }

        switch ($this->type) {
            case ContractEnum::TYPE_E_SHOP:
                $query->joinWith(['order' => function ($query) {
                        $query->andWhere(['order.type' => OrderEnum::PLATFORM_TYPE_E_SHOP]);
                    }])->andWhere(['not', ['order_id' => null]]);
                break;
            case ContractEnum::TYPE_N_SHOP:
                $query->joinWith(['order' => function ($query) {
                            $query->andWhere(['order.type' => OrderEnum::PLATFORM_TYPE_N_SHOP]);
                    }])->andWhere(['not', ['order_id' => null]]);
                break;
            case ContractEnum::TYPE_AUCTION:
                $query->with(['auction'])->andWhere(['not', ['auction_id' => null]]);
                break;
            case ContractEnum::TYPE_TENDER:
                $query->with('tender')->andWhere(['not', ['tender_id' => null]]);
                break;
            default:
                $query->andWhere('0=1');
        }
        return paginate($query);
    }
}