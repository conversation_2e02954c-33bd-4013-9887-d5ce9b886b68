<?php

namespace api\modules\tender\forms;

use api\components\BaseRequest;
use api\modules\tender\resources\CommissionMemberResource;
use common\enums\StatusEnum;
use Yii;

class CommissionGroupMemberEmailForm extends BaseRequest {

    public $id;
    public CommissionMemberResource $model;

    public function __construct(CommissionMemberResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function getResult()
    {

        // email ga aktivatsiya message yuborish kerak
        return true;

    }

}