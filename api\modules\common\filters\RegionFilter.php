<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\RegionResource;

class RegionFilter extends BaseRequest
{
    public $title;

    public function rules()
    {
        return [
            ['title', 'safe'],
        ];
    }

    public function getResult()
    {
        $query = RegionResource::find()->where('parent_id is null and type != 3');
        if ($this->title) {
            $query->andWhere(['like', 'title', $this->title]);
        }
        return $query->all();
    }
}
