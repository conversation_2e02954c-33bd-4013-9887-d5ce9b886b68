name: Deploy to Server

on:
  push:
    branches:
      - main  # Trigger on push to the main branch. Adjust as needed.

jobs:
  deploy:
    runs-on: ubuntu-latest  # Run the job on an Ubuntu runner.

    steps:
    - name: Checkout repository
      uses: actions/checkout@v2

    - name: Setup SSH key
      run: |
        mkdir -p ~/.ssh
        echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa

    - name: Connect to server and deploy
      run: |
        ssh -o StrictHostKeyChecking=no -i ~/.ssh/id_rsa devuser@************ -p 2255 "cd /var/www/dxp.uz && git pull origin main && composer install && php console/yii migrate --interactive=0"