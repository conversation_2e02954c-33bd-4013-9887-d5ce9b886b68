<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderRequestResource;
use api\modules\tender\resources\TenderRequirementsAnswerResource;
use api\modules\tender\resources\TenderQualificationSelectionResource;
use api\modules\tender\resources\TenderRequestRatingCommissionResource;
use api\modules\tender\resources\TenderRequestRatingResource;
use api\modules\tender\resources\TenderRequirementsResource;
use api\modules\tender\resources\TenderResource;
use api\modules\tender\resources\TenderResultItemResource;
use api\modules\tender\resources\TenderResultResource;
use common\enums\TenderEnum;
use Yii;

class TenderUpdateChairmanWithProtocolForm extends BaseRequest
{
    public TenderResource $model;
    public $vote;
    public $description;

    public function rules()
    {
        return [
            ['vote', 'required', 'message' => t('{attribute} yuborish majburiy')],
            ['vote', 'integer'],
            ['vote', 'checkVote'],
            ['description', 'required', 'when' => function ($model) {
                return $model->vote === TenderEnum::VOTE_NO || $model->vote === "0";
            }],
            ['description', 'string', 'max' => 255]
        ];
    }

    public function checkVote()
    {
        if ($this->vote == TenderEnum::VOTE_YES || $this->vote == TenderEnum::VOTE_NO) {
            return true;
        } else {
            $this->addError("vote", t("Ovoz berish qiymatlari 1 yoki 0 bo'lishi kerak"));
            return false;
        }
    }

    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function getResult()
    {
        date_default_timezone_set('Asia/Tashkent');
        if ($this->model->state != TenderEnum::STATE_READY_TO_MAKE_PROTOCOL) {
            $this->addError("description", t("Tender Rais uchun tasdiqlash holatida emas"));
            return false;
        }

        $transaction = \Yii::$app->db->beginTransaction();

        $commissionId = \Yii::$app->user->identity->commissionMemberId;
        $tenderCom = $this->model->getCommissionMember($commissionId);
        if ($tenderCom->role != TenderEnum::ROLE_CHAIRMAN) {
            $this->addError("description", t("Rais ro'li uchun ruxsat mavjud"));
            return false;
        }

        if ($this->vote == TenderEnum::VOTE_YES) {

            $requests = $this->model->tenderRequest;
            if ($requests == null || ($requests && count($requests) <= 2)) {
                $this->model->state = TenderEnum::STATE_NOT_REALIZED;

                if (!$this->model->save()) {
                    $this->addErrors($this->model->errors);
                    $transaction->rollBack();
                    return false;
                }

                $res = $this->model->notRealized($transaction);
                if (!$res) {
                    $this->addError("error", t("Kutilmagan xatolik"));
                    return false;
                }

                $transaction->commit();
                return true;
            }

            $this->model->state = TenderEnum::STATE_MADE_PROTOCOL;
            $this->model->discussion_end_date = date("Y-m-d H:i:s", strtotime("+10 minutes"));
            if (!$this->model->save()) {
                $transaction->rollBack();
                $this->addErrors($this->model->errors);
                return false;
            }


            foreach ($requests as $request) {

                $break = false;

                $result = new TenderResultResource();
                $result->tender_id = $this->model->id;
                $result->request_id = $request->id;
                $result->company_id = $request->company_id;
                $result->is_winner = TenderEnum::IS_WINNER_NEW;
                $result->technical_ball = 0;
                $result->price_ball = 0;

                /**
                 * secretaryBallCompareMinPassBall #1
                 */
                $tRatingSumBall = TenderRequestRatingResource::find()
                    ->notDeleted()
                    ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
                    ->andWhere(['tender_request_id' => $request->id])
                    ->andWhere(['tender_id' => $this->model->id])
                    ->sum("ball");
                if ($tRatingSumBall < $this->model->min_passing_ball) { // sekretar qo'ygan ball, o'tish balidan kichik bo'lsa yutqazdi
                    $break = true;
                    TenderResultItemResource::createItem($this->model->id, $request->id, $request->company_id, 1);
                }


                $result->technical_ball = $tRatingSumBall;

                /**
                 * disClassification #2
                 */

                if ($request->disclassification == 0 || $request->disclassification_system === 0) {
                    $break = true;
                    TenderResultItemResource::createItem($this->model->id, $request->id, $request->company_id, 2, null, $request->disclassification_text);
                }

                /**
                 * conflictInterest #3
                 */

                if ($request->conflict_interest == 0) {
                    $break = true;
                    TenderResultItemResource::createItem($this->model->id, $request->id, $request->company_id, 3, null, $request->conflict_interest_text);
                }

                /**
                 * #malakaTalab #4
                 */
                $malakaTalablar = TenderQualificationSelectionResource::find() // 5 ta talabga muvofiq baho olmagan bo'lsa yutqazdi, lekin bu holat bo'lmaydi
                ->notDeleted()
                    ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
                    ->andWhere(['tender_request_id' => $request->id])
                    ->andWhere(['state' => TenderEnum::QUALIFIER_STATE_NO])
                    ->all();
                if ($malakaTalablar != null && count($malakaTalablar) > 0) {
                    /**
                     * @var $malaka TenderQualificationSelectionResource
                     */
                    foreach ($malakaTalablar as $malaka) {
                        TenderResultItemResource::createItem($this->model->id, $request->id, $request->company_id, 4, $malaka->id, $malaka->title);
                    }
                    $break = true;
                }

                /**
                 * #tenderTalab #5
                 */

//                $tRequirementsCount = TenderRequirementsResource::find()
//                    ->notDeleted()
//                    ->andWhere(['tender_id' => $this->model->id])
//                    ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
//                    ->count();
//
//                $tRequirementsAnswer = TenderRequirementsAnswerResource::find()
//                    ->notDeleted()
//                    ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
//                    ->andWhere(['tender_id' => $this->model->id])
//                    ->andWhere(['tender_request_id' => $request->id])
//                    ->count();
//                if ($tRequirementsCount != $tRequirementsAnswer) { // tender talablarini hammasiga, company tomonidan javoblar berilmagan bo'lsa yutqazdi
//                    TenderResultItemResource::createItem($this->model->id, $request->id, $request->company_id, 5);
//                    $break = true;
//                }

                if ($break) {
                    $request->state = TenderEnum::REQUEST_STATE_BAD;
                    $request->is_winner = TenderEnum::IS_WINNER_BAD;
                    if (!$request->save()) {
                        $this->addErrors($request->errors);
                        $transaction->rollBack();
                        return false;
                    }
                    $result->is_winner = TenderEnum::IS_WINNER_BAD;//bular golib aniqlashda qatnashmaydi
                } else {
                    $request->state = TenderEnum::REQUEST_STATE_GOOD;
                    $result->is_winner = TenderEnum::IS_WINNER_NEW;
                    if (!$request->save()) {
                        $this->addErrors($request->errors);
                        $transaction->rollBack();
                        return false;
                    }
                }
                if (!$result->save()) {
                    $this->addErrors($result->errors);
                    $transaction->rollBack();
                    return false;
                }

            } // request yutqazganlarni terib chiqildi

            /**
             * tender baholash mezoni
             */
            if ($this->model->criteria_evaluation_proposals == TenderEnum::CRITERIA_EVALUATION_PROPOSALS_MIN_PRICE) { // eng past narx bergan yutadi

                $canWinnerSql = "select t.id
                                from tender_request t
                                where t.tender_id = " . $this->model->id . "
                                  and t.state != " . TenderEnum::REQUEST_STATE_BAD . "
                                  and t.is_winner != " . TenderEnum::IS_WINNER_BAD . "
                                  and t.status = " . TenderEnum::STATUS_ACTIVE . "
                                order by t.price, t.created_at limit 2";

                $canWinner = Yii::$app->db->createCommand($canWinnerSql)->queryAll(); // eng past narx bergan yutadi

                if ($canWinner == null || !isset($canWinner[0]) || count($canWinner) < 2) {
                    $this->model->state = TenderEnum::STATE_NOT_REALIZED;
                    if ($this->model->save()) {

                        $res = $this->model->notRealized($transaction);
                        if (!$res) {
                            $this->addError("error", t("Kutilmagan xatolik"));
                            return false;
                        }

                        $transaction->commit();
                        return true;
                    } else {
                        $this->addErrors($this->model->errors);
                        $transaction->rollBack();
                        return false;
                    }
                }
                $winnerRequestId = $canWinner[0]['id'];
                $secondWinnerRequestId = $canWinner[1]['id'];


                TenderRequestResource::updateAll([
                    'state' => TenderEnum::REQUEST_STATE_GOOD,
                    'is_winner' => TenderEnum::IS_WINNER_NO,
                    'updated_at' => date("Y-m-d H:i:s"),
                    'updated_by' => Yii::$app->user->id
                ], [
                    'and',
                    ['tender_id' => $this->model->id],
                    ['status' => TenderEnum::STATUS_ACTIVE],
                    ['!=', 'state', TenderEnum::REQUEST_STATE_BAD],
                    ['!=', 'is_winner', TenderEnum::IS_WINNER_BAD]
                ]);

                TenderResultResource::updateAll([
                    'is_winner' => TenderEnum::IS_WINNER_NO,
                    'updated_at' => date("Y-m-d H:i:s"),
                    'updated_by' => Yii::$app->user->id
                ], [
                    'and',
                    ['tender_id' => $this->model->id],
                    ['is_winner' => TenderEnum::IS_WINNER_NEW]
                ]);

                Yii::$app->db->createCommand()
                    ->update('tender_request', ['is_winner' => TenderEnum::IS_WINNER_YES, 'state' => TenderEnum::REQUEST_STATE_WINNER], 'id=' . $winnerRequestId)
                    ->execute();
                Yii::$app->db->createCommand()
                    ->update('tender_result', ['is_winner' => TenderEnum::IS_WINNER_YES], 'request_id=' . $winnerRequestId)
                    ->execute();

                Yii::$app->db->createCommand()
                    ->update('tender_request', ['is_winner' => TenderEnum::IS_SECOND_WINNER_YES, 'state' => TenderEnum::REQUEST_STATE_SECOND_WINNER], 'id=' . $secondWinnerRequestId)
                    ->execute();
                Yii::$app->db->createCommand()
                    ->update('tender_result', ['is_winner' => TenderEnum::IS_SECOND_WINNER_YES], 'request_id=' . $secondWinnerRequestId)
                    ->execute();

                $transaction->commit();
                return true;

            } else {
                $step_1_sql = "select t.id, t.price
                                from tender_request t
                               where t.tender_id = " . $this->model->id . "
                                  and t.state != " . TenderEnum::REQUEST_STATE_BAD . "
                                  and t.is_winner != " . TenderEnum::IS_WINNER_NO . "
                                  and t.status = " . TenderEnum::STATUS_ACTIVE . "
                                order by t.price, t.created_at";

                $step_1 = Yii::$app->db->createCommand($step_1_sql)->queryAll();

                if ($step_1 == null || !isset($step_1[0]) || count($step_1) < 2) {
                    $this->model->state = TenderEnum::STATE_NOT_REALIZED;
                    if ($this->model->save()) {

                        $res = $this->model->notRealized($transaction);
                        if (!$res) {
                            $this->addError("error", t("Kutilmagan xatolik"));
                            return false;
                        }

                        $transaction->commit();
                        return true;
                    } else {
                        $this->addErrors($this->model->errors);
                        $transaction->rollBack();
                        return false;
                    }
                }

                $first100_percent_id = $step_1[0]['id'];
                $first100_percent_price = $step_1[0]['price'];

                $step_2 = [];
                $step_3 = [];

                $step_2[$first100_percent_id] = 100;
                foreach ($step_1 as $requestStep1) {

                    if ($requestStep1['id'] == $first100_percent_id)
                        continue;

                    $step_2[$requestStep1['id']] = ($first100_percent_price / $requestStep1['price']) * 100;
                }


                $pricePart = $this->model->price_part;
                $step_3[$first100_percent_id] = $pricePart;
                Yii::$app->db->createCommand()
                    ->update('tender_result', ['price_ball' => $step_3[$first100_percent_id]], 'request_id=' . $first100_percent_id)
                    ->execute();
                foreach ($step_2 as $requestStep2Id => $requestPercentStep2) {

                    if ($requestStep2Id == $first100_percent_id)
                        continue;

                    $step_3[$requestStep2Id] = ($pricePart * $requestPercentStep2) / 100;

                    $this->updateTenderResult($requestStep2Id, 'price_ball', $step_3[$requestStep2Id]);

                }
                unset($step_1);
                unset($step_2);
//                return [
//                    'step1' => $step_1,
//                    'step2' => $step_2,
//                    'step3' => $step_3,
//                    'priceP' => $pricePart,
//                    'first100_percent_id' => $first100_percent_id,
//                    'first100_percent_price' => $first100_percent_price
//                ];

                $maxBall = TenderRequirementsResource::find()->where(['tender_id' => $this->model->id, 'status' => TenderEnum::STATUS_ACTIVE])->sum('max_ball');

                $step4_sql = "select t.tender_request_id, sum(t.ball) as sumBall
                               from tender_request_rating t
                              where t.tender_id = " . $this->model->id . "
                               and t.status = " . TenderEnum::STATUS_ACTIVE . "
                              group by t.tender_request_id
                              order by sum(t.ball) desc ";
                $step_4_data = Yii::$app->db->createCommand($step4_sql)->queryAll();

                $step_4 = [];
                $step_5 = [];

                foreach ($step_4_data as $ratingBallStep4) {
                    $step_4[$ratingBallStep4['tender_request_id']] = ($ratingBallStep4['sumBall'] / $maxBall) * 100;
                }

                $ballPart = $this->model->technical_part;

                foreach ($step_4 as $reqIdStep5 => $sumBallStep5) {

                    $step_5[$reqIdStep5] = ($ballPart * $sumBallStep5) / 100;

                    $this->updateTenderResult($reqIdStep5, 'technical_ball', $step_5[$reqIdStep5]);
                }
                unset($step_4);

//                return [
//                    'step4d' => $step_4_data,
//                    'step4' => $step_4,
//                    'step5' => $step_5,
//                    'ballPart' => $ballPart,
//                    'first100_ball_percent_id' => $first100_ball_percent_id,
//                    'first100_ball_percent' => $first100_ball_percent,
//                ];

                //step6

                $step_6 = [];
                foreach ($step_5 as $reqIdStep5k => $sumBallStep5) {

                    $step_6[$reqIdStep5k] = $sumBallStep5 + $step_3[$reqIdStep5k];

                    $this->updateTenderResult($reqIdStep5k, 'total_ball', $step_6[$reqIdStep5k]);

                }
                //$step_61 = $step_6;

                arsort($step_6);

                //$first_value = reset($step_6); // First element's value
                $first_key = key($step_6); // g'olib, eng ko'p ball olgan
                unset($step_6[$first_key]);
                $second_key = key($step_6); //ikkinchi g'olib

                //return $first_key;

                TenderRequestResource::updateAll([
                    'state' => TenderEnum::REQUEST_STATE_GOOD,
                    'is_winner' => TenderEnum::IS_WINNER_NO,
                    'updated_at' => date("Y-m-d H:i:s"),
                    'updated_by' => Yii::$app->user->id
                ], [
                    'and',
                    ['tender_id' => $this->model->id],
                    ['status' => TenderEnum::STATUS_ACTIVE],
                    ['!=', 'state', TenderEnum::REQUEST_STATE_BAD],
                    ['!=', 'is_winner', TenderEnum::IS_WINNER_BAD]
                ]);

                TenderResultResource::updateAll([
                    'is_winner' => TenderEnum::IS_WINNER_NO,
                    'updated_at' => date("Y-m-d H:i:s"),
                    'updated_by' => Yii::$app->user->id
                ], [
                    'and',
                    ['tender_id' => $this->model->id],
                    ['is_winner' => TenderEnum::IS_WINNER_NEW]
                ]);

                Yii::$app->db->createCommand()
                    ->update('tender_request', ['is_winner' => TenderEnum::IS_WINNER_YES], 'id=' . $first_key)
                    ->execute();
                Yii::$app->db->createCommand()
                    ->update('tender_result', ['is_winner' => TenderEnum::IS_WINNER_YES], 'request_id=' . $first_key)
                    ->execute();

                Yii::$app->db->createCommand()
                    ->update('tender_request', ['is_winner' => TenderEnum::IS_SECOND_WINNER_YES], 'id=' . $second_key)
                    ->execute();
                Yii::$app->db->createCommand()
                    ->update('tender_result', ['is_winner' => TenderEnum::IS_SECOND_WINNER_YES], 'request_id=' . $second_key)
                    ->execute();

//                return [
//                    'arr' =>$step_61,
//                    'arrs' =>$step_6,
//                    'req' => $first_value,
//                    'per' => $first_key
//                ];

                $transaction->commit();


            }


            //protokol shakllanishi
        } else {

            $this->model->state = TenderEnum::STATE_READY_TO_RATING;
            if ($this->model->save()) {
                TenderRequestRatingCommissionResource::updateAll(['status' => TenderEnum::STATUS_NOT_ACTIVE], [
                    'tender_id' => $this->model->id,
                    'status' => TenderEnum::STATUS_ACTIVE
                ]);
                TenderQualificationSelectionResource::updateAll(['state' => TenderEnum::QUALIFIER_STATE_NEW], [
                    'tender_id' => $this->model->id,
                    'status' => TenderEnum::STATUS_ACTIVE
                ]);
                TenderRequestRatingResource::updateAll(['status' => TenderEnum::STATUS_NOT_ACTIVE], [
                    'tender_id' => $this->model->id,
                    'status' => TenderEnum::STATUS_ACTIVE
                ]);

                $transaction->commit();
            } else {
                $this->addErrors($this->model->errors);
                $transaction->rollBack();
                return false;
            }
        }

        return true;
    }

    private function updateTenderResult($requestId, $ballType, $value)
    {
        Yii::$app->db->createCommand()
            ->update('tender_result', [$ballType => $value], 'request_id=' . $requestId)
            ->execute();
    }

}