<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderCommissionVoteResource;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;
use Yii;

class TenderCommissionChairmanVoteForm extends BaseRequest
{

    public TenderResource $model;
    public TenderCommissionVoteResource $tenderVoting;

    public $vote;
    public $description;

    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;
        $this->tenderVoting = new TenderCommissionVoteResource();

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            ['vote','required', 'message' => t('{attribute} yuborish majburiy')],
            ['vote','integer'],
            ['vote','checkVote'],
            ['description','required','when'=>function($model) {
                return $model->vote === TenderEnum::VOTE_NO || $model->vote === "0";
            }],
            ['description', 'string', 'max' => 255]
        ];
    }
    public function checkVote(){
        if($this->vote == TenderEnum::VOTE_YES || $this->vote == TenderEnum::VOTE_NO){
            return true;
        } else {
            $this->addError("vote", t("Ovoz berish qiymatlari 1 yoki 0 bo'lishi kerak"));
            return false;
        }
    }

    public function getResult()
    {

        if($this->model->state == TenderEnum::STATE_ACCEPT_MODERATOR){

            $commissionId = \Yii::$app->user->identity->commissionMemberId;

            if($this->model->getCommissionVote($commissionId)){
                $this->addError('vote', t("Avval bu tender uchun ovoz bergansiz"));

                return false;
            }

            $memberCount = $this->model->getTenderCommissionMembersCount();
            $voteCount = $this->model->getCommissionVoteYesCount();
            //  ovozlar soni komissiya soni yarmidan otsa, raisga o'tadi
            if(($memberCount-1)/2 < $voteCount){
                $this->model->state = TenderEnum::STATE_READY_FOR_CHAIRMAN;
                $this->model->save();
            }

            $tenderCom = $this->model->getCommissionMember($commissionId);

            $this->tenderVoting->tender_id = $this->model->id;
            $this->tenderVoting->status = TenderEnum::STATUS_ACTIVE;
            $this->tenderVoting->commission_member_id = $commissionId;
            $this->tenderVoting->role = $tenderCom->role;
            $this->tenderVoting->vote = $this->vote;
            $this->tenderVoting->description = $this->description;

            if($this->tenderVoting->save()){
                return true;
            } else {
                $this->addErrors($this->tenderVoting->errors);
                return false;
            }
        }
    }
}