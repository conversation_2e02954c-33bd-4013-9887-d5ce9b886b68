<?php

use common\models\auction\Auction;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\widgets\DetailView;
$this->title = 'Аукцион';
?>
    
<?= DetailView::widget([
    'model' => $model,
    'attributes' => [
        'id',
        'total_sum',
        [
            'attribute' => 'current_price',
            'value' => function($m){
                return $m->currentPrice;
            },
        ],
        [
            'attribute' => 'auctionRequests',
            'value' => function($m){
                return count($m->auctionRequests) + 0;
            }
        ],
        [
            'attribute' => 'status',
            'value' => function($m){
                return $m->statusName;
            },
            'filter' => Auction::getStatuses()
        ],
        [
            'format' => 'html',
            'attribute' => 'classifier_category_id',
            'value' => ($model->getClassifierCategory() ? $model->getClassifierCategory()->one()->title_uz :"")

        ],
        [
            'format' => 'html',
            'attribute' => 'company_id',
            'value' => ($model->getCompany() ?
                Html::a('<i class="fa fa-list"></i>', ['company/index']) . ' ' .
                Html::a('<i class="fa fa-circle-arrow-right"></i> ' . $model->getCompany()->one()->title, ['company/view', 'id' => $model->getCompany()->one()->id,])
                : '<span class="label label-warning">?</span>'),
        ],
        'statusName',
        'auction_end',
        'created_at',
        'updated_at',
    ],
]); ?>

<?php $form = ActiveForm::begin(
    [
        'options' => ['enctype' => 'multipart/form-data'],
        'id' => 'Product',
        //            'layout' => 'horizontal',
        'enableClientValidation' => true,
        'errorSummaryCssClass' => 'error-summary alert alert-error',
        'action' => toRoute('/auction/update?id=' . $model->id),
        'method' => 'post'
    ]
);
?>


    <?= $form->field($model, 'status')->dropDownList( Auction::getStatuses()) ?>
                
    <?= $form->field($model, 'cancel_reason')->textInput() ?>
    <?= $form->field($model, 'cancel_date')->hiddenInput(['value' => date("Y-m-d H:i:s")])->label(false) ?>
    <?= Html::submitButton(
        '<span class="fa fa-check"></span> ' . ($model->isNewRecord ? Yii::t('cruds', 'Создать') : Yii::t('cruds', 'Сохранить')),
        [
            'id' => 'save-' . $model->formName(),
            'class' => 'btn btn-success'
        ]
    );
    ?>

<?php ActiveForm::end(); ?>
