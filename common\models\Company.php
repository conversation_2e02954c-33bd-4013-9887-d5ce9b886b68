<?php

namespace common\models;

use api\modules\common\resources\CompanyVirtualAccountResource;
use common\enums\OperationTypeEnum;
use Yii;
use common\components\ActiveRecordMeta;
use common\models\auction\Auction;

/**
 * This is the model class for table "company".
 *
 * @property int $id
 * @property int $organization_type
 * @property int $resident
 * @property string|null $title
 * @property string|null $tin
 * @property string|null $pinfl
 * @property string|null $address
 * @property int|null $status
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $country_id
 * @property int|null $region_id
 * @property int|null $district_id
 * @property int|null $organization_legal_form_id
 * @property int|null $economic_activities_type_id
 * @property string|null $phone
 * @property string|null $director
 * @property string|null $passport_valid_date
 * @property string|null $passport
 * @property boolean $is_phone_confirmed
 *
 * @property CommissionGroup[] $commissionGroups
 * @property CommissionMember[] $commissionMembers
 * @property PlanSchedule[] $planSchedules
 * @property TenderCommissionMember[] $tenderCommissionMembers
 * @property Tender[] $tenders
 * @property int $availableBalance
 * @property int $blockedBalance
 * @property OrganizationLegalForm $organizationLegalForm
 * @property EconomicActivitiesType $economicActivitiesType
 */
class Company extends ActiveRecordMeta
{
    const BUDJET = 1;
    const CORPORATIV = 0;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'company';
    }

    //    /**
    //     * {@inheritdoc}
    //     */
    //    public function rules()
    //    {
    //        return [
    //            [['status', 'created_by', 'updated_by'], 'integer'],
    //            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
    //            [['title'], 'string', 'max' => 255],
    //            [['tin'], 'string', 'max' => 9],
    //            [['pinfl'], 'string', 'max' => 14],
    //        ];
    //    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('main', 'ID'),
            'title' => Yii::t('main', 'Title'),
            'tin' => Yii::t('main', 'Tin'),
            'pinfl' => Yii::t('main', 'Pinfl'),
            'status' => Yii::t('main', 'Status'),
            'created_at' => Yii::t('main', 'Created At'),
            'updated_at' => Yii::t('main', 'Updated At'),
            'deleted_at' => Yii::t('main', 'Deleted At'),
            'created_by' => Yii::t('main', 'Created By'),
            'updated_by' => Yii::t('main', 'Updated By'),
        ];
    }

    /**
     * Gets query for [[CommissionGroups]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCommissionGroups()
    {
        return $this->hasMany(CommissionGroup::class, ['company_id' => 'id']);
    }

    /**
     * Gets query for [[CommissionMembers]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCommissionMembers()
    {
        return $this->hasMany(CommissionMember::class, ['company_id' => 'id']);
    }

    /**
     * Gets query for [[PlanSchedules]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getPlanSchedules()
    {
        return $this->hasMany(PlanSchedule::class, ['company_id' => 'id']);
    }

    /**
     * Gets query for [[TenderCommissionMembers]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTenderCommissionMembers()
    {
        return $this->hasMany(TenderCommissionMember::class, ['company_id' => 'id']);
    }

    /**
     * Gets query for [[Tenders]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTenders()
    {
        return $this->hasMany(Tender::class, ['company_id' => 'id']);
    }

    /**
     * Gets query for [[Auctions]]
     * @return \yii\db\ActiveQuery
     */
    public function getAuctions()
    {
        return $this->hasMany(Auction::class, ['company_id' => 'id']);
    }

    public function getOrganizationLegalForm()
    {
        return $this->hasOne(OrganizationLegalForm::class, ['id' => 'organization_legal_form_id']);
    }

    public function getEconomicActivitiesType()
    {
        return $this->hasOne(EconomicActivitiesType::class, ['id' => 'economic_activities_type_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCompanyBankAccount()
    {
        return $this->hasOne(CompanyBankAccount::className(), ['company_id' => 'id']);
    }

    /**
     * Gets query for [[CompanyBalance]]
     * @return \yii\db\ActiveQuery
     */
    public function getRegion()
    {
        return $this->hasOne(Region::class, ['id' => 'region_id']);
    }

    /**
     * Gets query for [[CompanyBalance]]
     * @return \yii\db\ActiveQuery
     */
    public function getDistrict()
    {
        return $this->hasOne(Region::class, ['id' => 'district_id']);
    }

    /**
     * Gets query for [[CompanyBalance]]
     * @return \yii\db\ActiveQuery
     */
    public function getUsers()
    {
        return $this->hasMany(User::class, ['company_id' => 'id']);
    }

    public function getDirectorShortName()
    {
        return shortName($this->director);
    }

    public function getDirectorFullName()
    {
        $fullName = explode(' ', $this->director);
        return $fullName[0] . " " . $fullName[1];
    }

    public function isBudget(): bool
    {
        return $this->organization_type === self::BUDJET;
    }
    public function getAvailableBalance()
    {
        return CompanyVirtualAccountResource::find()
                ->where([
                    'company_id' => $this->id,
                    'prefix_account' => $this->isBudget() ? OperationTypeEnum::P_B_31101 : OperationTypeEnum::P_K_30101
                ])
                ->sum('price') * 1;
    }

    public function getBlockedBalance()
    {
        return CompanyVirtualAccountResource::find()
                ->where(['company_id' => $this->id])
                ->andWhere([
                    'in',
                    'prefix_account',
                    $this->isBudget()
                        ? [OperationTypeEnum::P_B_31202,OperationTypeEnum::P_B_31301,OperationTypeEnum::P_B_31501,]
                        : [OperationTypeEnum::P_K_30201,OperationTypeEnum::P_K_30202,OperationTypeEnum::P_K_30301,OperationTypeEnum::P_K_30501,]
                ])->sum('price') * 1;
    }
}
