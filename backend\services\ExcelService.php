<?php

namespace backend\services;


use PhpOffice\PhpSpreadsheet\IOFactory;
use common\models\BlackList;
use Yii;

class ExcelService
{


    /**
     * @throws \PHPExcel_Reader_Exception
     * @throws \PHPExcel_Exception
     */
    public function import($modelImport)
    {
        $modelImport->fileImport = \yii\web\UploadedFile::getInstance($modelImport, 'fileImport');

        if ($modelImport->fileImport && $modelImport->validate()) {
            $inputFileType = IOFactory::identify($modelImport->fileImport->tempName);
            $objReader = IOFactory::createReader($inputFileType);
            $objPHPExcel = $objReader->load($modelImport->fileImport->tempName);
            $items = $objPHPExcel->getActiveSheet()->toArray();
            if (is_array($items)) {
                BlackList::deleteAll();
                foreach ($items as $index => $item) {
                    if ($index > 0 ) {
                                $model = new BlackList();
                                $model->title = (string)$item[0];
                                $model->inn = (integer)$item[1];
                                $model->region = (string)$item[2];
                                $model->number_date = (string)$item[3];
                                $model->start_date =  date("Y-m-d",strtotime($item['4']));
                                $model->end_date =  date("Y-m-d",strtotime($item['5']));

                                if (!$model->save()) {
                                    dd($model->getErrors());
                                }
                            }
                        }
                }
            Yii::$app->getSession()->setFlash('success', 'Success');
        } else {
            Yii::$app->getSession()->setFlash('error', 'Error');
        }

    }


}