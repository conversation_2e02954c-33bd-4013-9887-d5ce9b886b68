<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderRequestResource;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;
use Yii;
use yii\web\NotFoundHttpException;

class TenderRequestDisclassificationForm extends BaseRequest
{

    public TenderResource $model;
    public $disclassification_text;
    public $disclassification;
    public $tender_request_id;

    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [['tender_request_id', 'disclassification'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['disclassification_text'], 'safe'],
            [['disclassification'], 'integer'],
            [['tender_request_id'], 'exist', 'skipOnError' => true, 'targetClass' => TenderRequestResource::class, 'targetAttribute' => ['tender_request_id' => 'id']],
        ];

    }

    public function getResult()
    {
        if ($this->model->state != TenderEnum::STATE_READY_TO_RATING) {
            $this->addError("grades", t("Baholash bosqichida emas"));

            return false;
        }
        $commissionId = \Yii::$app->user->identity->commissionMemberId;
        $tenderCom = $this->model->getCommissionMember($commissionId);
        if ($tenderCom->role != TenderEnum::ROLE_SECRETARY) {
            $this->addError("grades", t("Sekretar roli uchun ruxsat mavjud"));
            return false;
        }

        $request = TenderRequestResource::find()->notDeleted()->andWhere(['tender_id' => $this->model->id, 'status' => TenderEnum::STATUS_ACTIVE, 'id' => $this->tender_request_id])->one();
        if (!$request) {
            $this->addError("error", t("Taklif topilmadi"));
            return false;
        }
        $request->disclassification_text = $this->disclassification_text;
        $request->disclassification = $this->disclassification;
        if ($request->save()) {
            return true;
        } else {
            $this->addErrors($request->errors);
            return false;
        }
    }
}