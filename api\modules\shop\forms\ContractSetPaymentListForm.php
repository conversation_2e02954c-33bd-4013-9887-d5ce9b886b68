<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use common\enums\OperationTypeEnum;
use common\models\VirtualTransaction;
use Yii;
use api\modules\shop\resources\ContractResource;
use common\enums\CompanyTransactionEnum;
use common\enums\ContractEnum;
use common\enums\StatusEnum;
use common\models\CompanyBalance;
use common\models\CompanyTransaction;
use common\models\DifferentialCompanies;
use yii\db\Exception;
use yii\web\NotFoundHttpException;

class ContractSetPaymentListForm extends BaseRequest
{

    public ?ContractResource $model;
    public $id;



    public function rules()
    {
        return [
            [['id'], 'required'],
        ];
    }


    /**
     * @throws Exception
     * @throws NotFoundHttpException
     * @throws \yii\base\Exception
     * @throws \Throwable
     */
    public function getResult()
    {
        $this->model = ContractResource::findOne(['id' => $this->id, 'deleted_at' => null]);
        if (!$this->model)
            throw new NotFoundHttpException("Product not found");
        $transaction = \Yii::$app->db->beginTransaction();
        $user = \Yii::$app->user->identity;
        $isBudget = $user->isBudget;
        $company = $user->company;
        $company_id = $company->id;
        if ($this->model->customer_id != $company_id) {
            $this->addError("error", t("Shartnoma sizga tegishli emas"));
            return false;
        }
        if ($this->model->status != ContractEnum::STATUS_SIGNED) {
            $this->addError("error", t("Shartnoma imzolangan xolatda emas, to'lov qilib bo'lmaydi"));
            return false;
        }
        //todo so'rab berishsa qilamz
        $differentialPercent = null;
        if(!$isBudget){
            $differential = DifferentialCompanies::findOne(['tin' => $company->tin, 'status' => StatusEnum::STATUS_ACTIVE]);
            if ($differential) {
                $differentialPercent = $differential->percent;
            }
        }

        $this->model->status = ContractEnum::STATUS_PAYMENT_END;
        $this->model->customer_pay_date = date("Y-m-d H:i:s");
        $needPrice = $this->model->need_price;
        $totalPrice = $this->model->price;
        $zalog = $totalPrice - $needPrice;
        if (null != $differentialPercent) {
            $differentialPrice = ($totalPrice * $differentialPercent) / 100;
            if ($differentialPrice > 0) {
                $needPrice = $differentialPrice - $zalog;
            } else if ($differentialPrice == 0) {
                $needPrice = 0;
            }

        }
        $this->model->need_price = 0;

        if ($this->model->save()) {

            if (!$isBudget) {

//                $companyBalance = CompanyBalance::find()->where(['company_id' => $company_id])->one();
//                if ($companyBalance->available < $needPrice) {
//                    $this->addError("error", t("Hisobda mablag' yetarli emas"));
//                    $transaction->rollBack();
//                    return false;
//                }
                if (!hasMoney($company, $needPrice)) {
                    $this->addError("error", t("Hisobda mablag' yetarli emas"));
                    $transaction->rollBack();
                    return false;
                }

                if ($needPrice) {
//                    $pay = new CompanyTransaction([
//                        'company_id' => $company_id,
//                        'contract_id' => $this->model->id,
//                        'auction_id' => $this->model->auction_id,
//                        'order_id' => $this->model->order_id,
//                        'amount' => $needPrice,
//                        'type' => CompanyTransactionEnum::TYPE_BLOCK_PAYMENT_FOR_CONTRACT,
//                        'description' => Yii::t("main", "Shartnoma uchun to'lov qildi"),
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'transaction_date' => date("Y-m-d H:i:s"),
//                    ]);
//                    if (!$pay->save()) {
//                        $this->addError("pay", $pay->errors);
//                    }
                    $productName = null;
                    $product_id = null;
                    if ($this->model->tender_id !== null) {
                        $productName = OperationTypeEnum::PRODUCT_NAME_TENDER;
                        $product_id = $this->model->tender_id;
                    } elseif ($this->model->auction_id !== null) {
                        $productName = OperationTypeEnum::PRODUCT_NAME_AUCTION;
                        $product_id = $this->model->auction_id;
                    } elseif ($this->model->order_id !== null) {
                        $productName = OperationTypeEnum::PRODUCT_NAME_ORDER;
                        $product_id = $this->model->order_id;
                    }
                    try {
                        VirtualTransaction::saveTransaction(
                            $company,
                            $company,
                            OperationTypeEnum::P_K_30101,
                            OperationTypeEnum::P_K_30301,
                            $needPrice,
                            Yii::t("main", "Shartnoma uchun to'lov qildi"),
                            $productName,
                            $product_id,
                            $this->model->id,
                            OperationTypeEnum::BLOCK_TRANSACTION_FULL_PAYMENT
                        );
                    } catch (\Exception $e) {
                        $transaction->rollBack();
                        $this->addError("pay", $e->getMessage());
                        return false;
                    }
                }

            }

            $transaction->commit();
            return $this->model->id;
        }

        $this->addErrors($this->model->errors);
        return false;
    }
}