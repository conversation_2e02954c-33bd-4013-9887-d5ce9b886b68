<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\common\resources\DistrictResource;

class DistrictFilter extends BaseRequest
{
  public $title;
  public $region_id;

  public function rules()
  {
    return [
      ['title', 'safe'],
      ['region_id', 'integer'],
    ];
  }

  public function getResult()
  {
    $query = DistrictResource::find();

    if ($this->title) {
      $query->where(['like', 'title', $this->title]);
    }
    if ($this->region_id) {
      $query->andWhere(['parent_id' => $this->region_id]);
    }

    return $query->all();
  }
}
