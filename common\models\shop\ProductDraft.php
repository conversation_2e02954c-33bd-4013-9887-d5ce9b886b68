<?php

namespace common\models\shop;

use api\modules\common\resources\FileResource;
use api\modules\shop\resources\FavoriteResource;
use api\modules\shop\resources\ProductCommentResource;
use api\modules\shop\resources\ProductModeratorLogResource;
use common\components\ActiveRecordMeta;
use common\models\Classifier;
use common\models\ClassifierCategory;
use common\models\Company;
use common\models\CompanyBankAccount;
use common\models\Region;
use common\models\shop\Cart;
use common\models\shop\OrderList;
use common\models\shop\ProductFile;
use common\models\shop\ProductFile;
use common\models\Unit;
use common\traits\FindOrFail;
use common\traits\NotDeleted;
use common\traits\SoftDelete;
use Yii;

/**
 * This is the model class for table "product_draft".
 *
 * @property int $id
 * @property int|null $classifier_category_id
 * @property int|null $classifier_id
 * @property int|null $company_id
 * @property string|null $account_number
 * @property string|null $organ
 * @property string|null $title
 * @property string|null $brand_title
 * @property string|null $description
 * @property int|null $year
 * @property int|null $quantity
 * @property int|null $unit_id
 * @property float|null $price
 * @property int|null $min_order
 * @property int|null $max_order
 * @property string|null $type
 * @property int|null $country_id
 * @property string|null $unit_price
 * @property string|null $made_in
 * @property string|null $platform_display
 * @property int|null $state
 * @property int|null $delivery_period
 * @property int|null $delivery_period_type
 * @property int|null $warranty_period
 * @property int|null $warranty_period_type
 * @property int|null $expiry_period
 * @property int|null $expiry_period_type
 * @property int|null $status
 * @property string|null $active_date
 * @property string|null $inactive_date
 * @property int|null $is_have_license
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $delete_reason
 *
 * @property Classifier $classifier
 * @property ClassifierCategory $classifierCategory
 * @property Company $company
 * @property Unit $unit
 */
class ProductDraft extends ActiveRecordMeta
{

    use SoftDelete;
    use NotDeleted;
    use FindOrFail;

    public static function getPeriodType($index = null){
        $arr = [
            1 => 'Yil',
            2 => 'Oy',
            3 => 'Kun',
        ];
        return isset($arr[$index]) && $arr[$index] ? $arr[$index] : $index;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'product_draft';
    }

    /**
     * {@inheritdoc}
     */
//    public function rules()
//    {
//        return [
//            [['classifier_category_id', 'classifier_id', 'company_id', 'account_number', 'organ', 'title', 'brand_title', 'description', 'year', 'quantity', 'unit_id', 'price', 'min_order', 'max_order', 'type', 'country_id', 'unit_price', 'made_in', 'platform_display', 'state', 'delivery_period', 'delivery_period_type', 'warranty_period', 'warranty_period_type', 'expiry_period', 'expiry_period_type', 'status', 'active_date', 'inactive_date', 'is_have_license', 'created_at', 'updated_at', 'deleted_at', 'created_by', 'updated_by'], 'default', 'value' => null],
//            [['classifier_category_id', 'classifier_id', 'company_id', 'year', 'quantity', 'unit_id', 'min_order', 'max_order', 'country_id', 'state', 'delivery_period', 'delivery_period_type', 'warranty_period', 'warranty_period_type', 'expiry_period', 'expiry_period_type', 'status', 'is_have_license', 'created_by', 'updated_by'], 'integer'],
//            [['description'], 'string'],
//            [['price'], 'number'],
//            [['active_date', 'inactive_date', 'created_at', 'updated_at', 'deleted_at'], 'safe'],
//            [['account_number', 'title', 'brand_title', 'type', 'unit_price', 'made_in', 'platform_display'], 'string', 'max' => 255],
//            [['organ'], 'string', 'max' => 11],
//            [['classifier_category_id'], 'exist', 'skipOnError' => true, 'targetClass' => ClassifierCategory::class, 'targetAttribute' => ['classifier_category_id' => 'id']],
//            [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => Classifier::class, 'targetAttribute' => ['classifier_id' => 'id']],
//            [['company_id'], 'exist', 'skipOnError' => true, 'targetClass' => Company::class, 'targetAttribute' => ['company_id' => 'id']],
//            [['unit_id'], 'exist', 'skipOnError' => true, 'targetClass' => Unit::class, 'targetAttribute' => ['unit_id' => 'id']],
//        ];
//    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => t("ID"),
            'classifier_category_id' => t('Classifier Category ID'),
            'classifier_id' => t('Classifier ID'),
            'company_id' => t('Company ID'),
            'account_number' => t('Account Number'),
            'title' => t('Title'),
            'brand_title' => t('Brand Title'),
            'description' => t('Description'),
            'year' => t('Year'),
            'quantity' => t('Quantity'),
            'unit_id' => t('Unit ID'),
            'price' => t('Price'),
            'min_order' => t('Min Order'),
            'max_order' => t('Max Order'),
            'type' => t('Type'),
            'country_id' => t('Country ID'),
            'unit_price' => t('Unit Price'),
            'made_in' => t('Made In'),
            'platform_display' => t('Platform Display'),
            'state' => t('State'),
            'delivery_period' => t('Delivery Period'),
            'delivery_period_type' => t('Delivery Period Type'),
            'warranty_period' => t('Warranty Period'),
            'warranty_period_type' => t('Warranty Period Type'),
            'expiry_period' => t('Expiry Period'),
            'expiry_period_type' => t('Expiry Period Type'),
            'status' => t('Status'),
            'active_date' => t('Active Date'),
            'inactive_date' => t('Inactive Date'),
            'is_have_license' => t('Is Have License'),
            'created_at' => t('Created At'),
            'updated_at' => t('Updated At'),
            'deleted_at' => t('Deleted At'),
            'delete_reason' => t('Delete Reason'),
            'created_by' => t('Created By'),
            'updated_by' => t('Updated By'),
        ];
    }

    public function getCarts()
    {
        return $this->hasMany(Cart::class, ['product_id' => 'id']);
    }

    public function getBankAccount()
    {
        return $this->hasOne(CompanyBankAccount::class, ['id' => 'account_number']);
    }

    /**
     * Gets query for [[Classifier]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClassifier()
    {
        return $this->hasOne(Classifier::class, ['id' => 'classifier_id']);
    }

    /**
     * Gets query for [[ClassifierCategory]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClassifierCategory()
    {
        return $this->hasOne(ClassifierCategory::class, ['id' => 'classifier_category_id']);
    }

    /**
     * Gets query for [[Company]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCompany()
    {
        return $this->hasOne(Company::class, ['id' => 'company_id']);
    }

    /**
     * Gets query for [[Unit]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUnit()
    {
        return $this->hasOne(Unit::class, ['id' => 'unit_id']);
    }

    public function getIsOwnerProduct()
    {
        if (Yii::$app->user->isGuest) {
            return false;
        }
        return Yii::$app->user->identity->company_id == $this->company_id;
    }

    public function getModeratorLog()
    {
        return $this->hasMany(ProductModeratorLogResource::class, ['product_id' => 'id']);
    }

    public function getImages()
    {
        return $this->hasMany(FileResource::class, ['id' => 'file_id'])->orderBy(['is_main' => SORT_DESC])->via('productImages');
    }

    public function getProductImages()
    {
        return $this->hasMany(ProductDraftFile::class, ['product_id' => 'id'])->andWhere([ProductDraftFile::tableName() . '.type' => ProductDraftFile::TYPE_IMAGE]);
    }


}
