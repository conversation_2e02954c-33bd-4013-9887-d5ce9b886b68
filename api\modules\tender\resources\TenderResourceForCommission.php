<?php


namespace api\modules\tender\resources;


use api\modules\client\resources\TenderRequestResource;
use common\enums\TenderEnum;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\Tender;
use yii\web\NotFoundHttpException;

class TenderResourceForCommission extends Tender
{

    public function fields()
    {
        return [
            'id',
            'lot',
            'updated_at',
            'end_date',
            'title',
            'state',
            'criteria_evaluation_proposals',
            'tenderTotalPrice' => function($model){
                return $model->getTenderTotalPriceBase();
            },
            'company',
            'commissionMemberRole',
            'tenderRequestCount'
        ];
    }

    public function extraFields()
    {
        return [

        ];
    }

    public function getCompany()
    {
        return $this->hasOne(CompanyResource::class, ['id' => 'company_id']);
    }

    public function getTenderRequestRating()
    {
        return $this->hasMany(TenderRequestRatingResource::class, ['tender_id' => 'id']);
    }

    public function getCommissionMemberRole()
    {
        $model = TenderCommissionMemberResource::find()->notDeleted()
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_id' => $this->id])
            ->andWhere(['commission_member_id' => \Yii::$app->user->identity->commissionMemberId])
            ->one();
        return $model ? $model->role : "yoq";
    }


    public function getTenderClassifiers()
    {
        return $this->hasMany(TenderClassifierResource::class, ['tender_id' => 'id'])->andWhere([TenderClassifierResource::tableName() . '.status' => TenderEnum::STATUS_ACTIVE]);
    }

//    public function getTenderTotalPrice()
//    {
//        $price = 0;
//        foreach ($this->tenderClassifiers as $classifier) {
//            $price += $classifier->price;
//        }
//        return $price;
//    }

    public function getTenderRequest()
    {
        return TenderRequestResource::find()
            ->notDeleted()
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_id' => $this->id])
            ->all();
    }

    public function getTenderRequestCount()
    {
        return count($this->tenderRequest);
    }

    public function getCommissionMember(int $commissionId)
    {
        $model = TenderCommissionMemberResource::find()->notDeletedAndFromCompany()
            ->andWhere(['tender_commission_member.tender_id' => $this->id, 'tender_commission_member.commission_member_id' => $commissionId, 'tender_commission_member.status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_commission_member.status' => TenderEnum::STATUS_ACTIVE])
            ->one();
        if (!$model) new NotFoundHttpException("Ma'lumot topilmadi");
        return $model;
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}