<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;
use common\models\CompanyBalance;
use common\models\TenderModeratorLog;
use Yii;
use yii\base\Exception;
use function foo\func;

class AnnounceForm extends BaseRequest
{

    public ProductResource $model;


    public function __construct(ProductResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return [

        ];
    }


    /**
     * @throws Exception
     */
    public function getResult()
    {
        $company = Yii::$app->user->identity->company;
        $company_id = $this->model->company_id;

        if($company->id != $company_id) {
            $this->addError("error", t("Sizga tegishli emas"));
            return false;
        }

        $totalSum = $this->model->price * env('SHOP_ZALOG_PERSENT', 0.003);

        $companyBalance = CompanyBalance::find()->andWhere(['company_id' => $company_id])->one();

        if (!$companyBalance || ($companyBalance && $companyBalance->available < $totalSum)) {
            throw new Exception(t("Balansda yetarli mablag' mavjud emas"));
        }
        $this->model->state = ProductEnum::SHOP_STATE_ACTIVE;
        $this->model->active_date = date('Y-m-d H:i:s');
//        $this->model->inactive_date = addDaysExcludingWeekends(date("Y-m-d H:i:s"), 15);
        $this->model->inactive_date = date("Y-m-d H:i:s", strtotime("+15 minutes"));
        if($this->model->save()){
            return true;
        } else {
            $this->addErrors($this->model->errors);
            return false;
        }
    }
}