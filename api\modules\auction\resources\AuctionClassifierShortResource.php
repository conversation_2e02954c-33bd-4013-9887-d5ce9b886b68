<?php

namespace api\modules\auction\resources;

use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\PlanScheduleShortResource;
use api\modules\common\resources\UnitResource;
use common\models\auction\AuctionClassifier;

class AuctionClassifierShortResource extends AuctionClassifier
{
    public function fields()
    {
        return [
            'id',
            'quantity',
            'price' => function ($model) {
                return $model->price / 100;
            },
            'total_sum' => function ($model) {
                return $model->total_sum / 100;
            },
            'description',
            'classifier',
            'unit'
        ];
    }

    public function extraFields()
    {
        return [
            'classifier',
            'unit',
            'auction',
            'planScheduleClassifier'
        ];
    }

    public function getUnit()
    {
        return $this->hasOne(UnitResource::class, ['id' => 'unit_id']);
    }

    public function getClassifier()
    {
        return $this->hasOne(ClassifierResource::class, ['id' => 'classifier_id']);
    }

    public function getPlanScheduleClassifier()
    {
        return $this->hasOne(PlanScheduleClassifierResource::class, ['id' => 'plan_schedule_classifier_id']);
    }

    public function getPlanSchedule()
    {
        return $this->hasOne(PlanScheduleShortResource::class, ['id' => 'plan_schedule_id']);
    }

//  public static function find()
//  {
//    return new NotDeletedFromCompanyQuery(get_called_class());
//  }

//  public static function findOne($id)
//  {
//    return new NotDeletedFromCompanyQuery(get_called_class());
//  }
}
