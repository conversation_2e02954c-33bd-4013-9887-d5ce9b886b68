<?php

namespace api\modules\common\filters;

use api\components\BaseRequest;
use api\modules\common\resources\PlanScheduleClassifierResource;

class ClassifierWithPlanScheduleFilter extends BaseRequest
{
    public $title;
    public $plan_schedule_id;

    public function rules()
    {
        return [
            [['plan_schedule_id'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['plan_schedule_id'], 'integer'],
            [['title'], 'safe'],
        ];
    }

    public function getResult()
    {
        return PlanScheduleClassifierResource::find()
            ->joinWith('classifier')
            ->where(['plan_schedule_id' => $this->plan_schedule_id])->all();
    }
}
