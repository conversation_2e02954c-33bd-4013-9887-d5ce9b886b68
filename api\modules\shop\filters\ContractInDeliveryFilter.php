<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\shop\resources\CartResource;
use api\modules\shop\resources\ContractResource;
use common\enums\ContractEnum;
use Yii;

class ContractInDeliveryFilter extends BaseRequest
{
    public $producer_id;
    public $customer_id;
    public $order_id;
    public $status;
    public $pageNo = 0;
    public $pageSize = 10;
    public function rules()
    {
        return [
            [['pageNo', 'pageSize'], 'integer'],
            [['producer_id','status','customer_id','order_od'], 'safe']
        ];
    }

    public function getResult()
    {
        $model = ContractResource::find()
            ->andWhere(['status'=>ContractEnum::STATUS_PAYMENT_END]);

        $company_id = Yii::$app->user->identity->company_id;
        $model->andWhere([
            'or',
            ['producer_id' => $company_id],
            ['customer_id' => $company_id]
        ]);

//        if ($this->producer_id) {
//            $model->andWhere(['producer_id' => $this->producer_id]);
//        }
//        if ($this->customer_id) {
//            $model->andWhere(['customer_id' => $this->customer_id]);
//        }

        $model->orderBy([ 'created_at'=> SORT_DESC]);
        return paginate($model);
    }
}
