<?php

namespace api\modules\shop\resources;

use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\CompanyShortResource;
use common\models\auction\Auction;
use common\models\auction\AuctionOffer;
use yii\helpers\ArrayHelper;

class AuctionExternalResource extends Auction
{
    public function fields()
    {
        return [
            'id',
            'lot',
            'classifiers',
        ];
    }

    public function getClassifiers()
    {
        $auctionClassifiers = $this->auctionClassifiers;
        $classfiers = ClassifierResource::find()->where(['id' => ArrayHelper::getColumn($auctionClassifiers, 'classifier_id')])->all();
        return $classfiers;
    }
}
