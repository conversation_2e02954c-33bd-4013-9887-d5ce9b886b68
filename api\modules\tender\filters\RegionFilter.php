<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\RegionResource;

class RegionFilter extends BaseRequest
{
    public $title;

    public function rules (){
        return [
            ['title', 'safe'],
        ];
    }

    public function getResult()
    {
        $query = RegionResource::find();
        if($this->title){
            $query->where(['like','title', $this->title]);
        }
        return $query->all();
    }
}