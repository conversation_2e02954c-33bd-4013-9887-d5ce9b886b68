<?php

namespace api\modules\shop\controllers;

use api\components\ApiController;
use api\modules\shop\filters\OrderListFilter;
use api\modules\shop\forms\OrderForm;
use api\modules\shop\forms\ProductCommentForm;
use api\modules\shop\resources\OrderResource;
use api\modules\shop\resources\ProductCommentResource;
use common\behaviors\RoleAccessBehavior;
use Yii;

/**
 * Default controller for the `shop` module
 */
class ProductCommentController extends ApiController
{
    public function behaviors()
    {
        $parent = parent::behaviors();
        $parent['accessByRole'] = [
            'class' => RoleAccessBehavior::class,
            'rules' => [
                'create' => ['user'],
            ],
        ];
        return $parent;
    }
    public function actionCreate()
    {

        return $this->sendResponse(
            new ProductCommentForm(new ProductCommentResource()),
            Yii::$app->request->bodyParams
        );
    }
}
