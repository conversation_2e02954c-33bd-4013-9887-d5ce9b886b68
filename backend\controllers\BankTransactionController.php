<?php

namespace backend\controllers;

use backend\forms\ExcelImportForm;
use backend\services\ExcelService;
use common\models\bank\BankTransactionIn;
use common\models\bank\BankTransactionOut;
use common\models\bank\search\BankTransactionInSearch;
use common\models\bank\search\BankTransactionOutSearch;
use Yii;
use common\models\BlackList;
use common\models\BlackListSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * BankTransactionController implements the CRUD actions for BlackList model.
 */
class BankTransactionController extends BackendController
{

    /**
     * Lists all BlackList models.
     * @return mixed
     */
    public function actionIn()
    {
        $searchModel = new BankTransactionInSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }
    public function actionInView($id)
    {
        $model = BankTransactionIn::findOne($id);
        return $this->render('in_view', [
            'model' => $model,
        ]);
    }
    /**
     * Lists all BlackList models.
     * @return mixed
     */
    public function actionOut()
    {
        $searchModel = new BankTransactionOutSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        return $this->render('out_index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionOutView($id)
    {
        $model = BankTransactionOut::findOne($id);
        return $this->render('out_view', [
            'model' => $model,
        ]);
    }
}
