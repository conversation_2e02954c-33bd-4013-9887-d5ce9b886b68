<?php

namespace common\services;

use yii\base\Component;
use yii\base\InvalidConfigException;

class SmsService extends Component
{
    public static function generateSmsMessage(string $message,int $rand, string $pattern = "0000"): array|string|null
    {
        return preg_replace("/$pattern/", $rand, $message);
    }

    /**
     * @throws InvalidConfigException
     */
    public function getToken(): ?string
    {
        $response = (new RequestService())
            ->setBaseUrl(env('SMS_LOGIN_HOST'))
            ->setRequestMethod('POST')
            ->setParam([
                'email' => env('SMS_USERNAME'),
                'password' => env('SMS_PASSWORD'),
            ])
            ->send();
        return $response['data']['token'] ?? null;
    }

    /**
     * @throws InvalidConfigException
     * @throws \Exception
     */
    public function sendSms(string $phone, string $message): bool
    {
        $params = [
            'mobile_phone' => $phone,
            'message' => $message,
            'from' => 4546,
            'callback_url' => 'https://xarid.ebirja.uz/',
        ];
        $token = $this->getToken();
        $response = (new RequestService())
            ->setBaseUrl(env('SMS_HOST'))
            ->setRequestMethod('POST')
            ->setToken($token)
            ->setParam($params)
            ->send();

        if ($response === null)
        {
            throw new \Exception('SMS bilan aloqa yoq. ');
        }

        return isset($response['status']) && $response['status'] === 'waiting';
    }
}