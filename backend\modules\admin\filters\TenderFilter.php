<?php


namespace backend\modules\admin\filters;


use api\components\BaseRequest;
use api\modules\backend\resources\TenderResource;
use common\enums\TenderEnum;

class TenderFilter extends BaseRequest
{
    public TenderResource $model;

    public $type;

    public function rules(){
        return [
            ['type','integer'],
            ['type' ,'in' , 'range' => [TenderEnum::TYPE_INVITE , TenderEnum::TYPE_TENDER]],
        ];
    }

    public function __construct($type , $params = [])
    {
        $this->type = $type;

        parent::__construct($params);
    }

    public function getResult()
    {
        $model = TenderResource::find()->where(['type' => $this->type]);
        $model->orderBy(['updated_at' => SORT_ASC]);
        return paginate($model);
    }
}