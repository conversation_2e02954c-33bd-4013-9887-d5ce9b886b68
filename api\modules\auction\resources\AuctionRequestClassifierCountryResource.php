<?php

namespace api\modules\auction\resources;

use api\modules\common\resources\RegionResource;
use common\models\auction\AuctionRequestClassifierCountry;

class AuctionRequestClassifierCountryResource extends AuctionRequestClassifierCountry
{

    public function fields()
    {
        return [
            'country',
            'auction_classifier_id'
        ];
    }

    public function getCountry()
    {
        return $this->hasOne(RegionResource::class, ['id' => 'country_id']);
    }
}