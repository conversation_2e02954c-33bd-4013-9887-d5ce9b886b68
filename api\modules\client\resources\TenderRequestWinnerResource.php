<?php


namespace api\modules\client\resources;


use api\modules\tender\resources\CompanyResource;
use common\models\Company;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderRequest;

class TenderRequestWinnerResource extends TenderRequest
{

    public function fields(){
        return [
            'id',
            'price',
            'price_qqs',
            'company'
        ];
    }
    public function getCompany()
    {
        return $this->hasOne(CompanyResource::class, ['id' => 'company_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}