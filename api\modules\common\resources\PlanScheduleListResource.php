<?php

namespace api\modules\common\resources;

use common\enums\StatusEnum;
use common\models\PlanSchedule;

class PlanScheduleListResource extends PlanSchedule
{
    public function fields(): array
    {
        return [
            'id',
            'company',
            'title',
            'year',
            'quarter',
            'total_product_count'
        ];
    }

    public function extraFields(): array
    {
        return [
            'classifiers'
        ];
    }


    public function getCompany()
    {
        return $this->hasOne(CompanyShortResource::class, ['id' => 'company_id']);
    }

    public function getClassifiers()
    {
        return PlanScheduleClassifierShortResource::find()
            ->where(['plan_schedule_id' => $this->id, 'status' => StatusEnum::STATUS_ACTIVE])
            ->all();
    }
}