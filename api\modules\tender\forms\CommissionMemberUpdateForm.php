<?php

namespace api\modules\tender\forms;

use api\components\BaseRequest;
use api\modules\auth\resources\UserResource;
use api\modules\tender\resources\CommissionMemberResource;
use common\enums\StatusEnum;
use common\enums\TenderEnum;
use common\enums\UserEnum;
use common\models\User;
use Yii;
use yii\base\Exception;

class CommissionMemberUpdateForm extends BaseRequest
{

    //public $tin;
    public $pinfl;
    public $fullname;
    public $passport_serie;
    public $passport_number;
    public $mail;
    public $phone;
    public $birthday;
    public $position;
    public $company_name;

    public CommissionMemberResource $model;

    public function __construct(CommissionMemberResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [['pinfl', 'fullname', 'passport_serie', 'passport_number', 'mail', 'phone', 'birthday', 'position', 'company_name'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['pinfl', 'fullname', 'passport_serie', 'passport_number', 'mail', 'phone', 'birthday', 'position', 'company_name'], 'safe'],
            ['mail', 'email', 'message' => t("Elektron pochta noto'g'ri kiritildi")],
            ['pinfl', 'isPinfl'],
            ['pinfl', 'string', 'length' => 14],
            ['passport_number', 'isPassportNumber'],
            ['passport_serie', 'string', 'length' => 2, 'message' => t("{attribute} uzunligi 2 ta belgi bo'lishi kerak")],
            ['passport_number', 'string', 'length' => 7, 'message' => t("{attribute} uzunligi 7 ta belgi bo'lishi kerak")]
        ];
    } //AA 4659903

    public function attributeLabels()
    {
        return [
            'id' => Yii::t('main', 'ID'),
            'company_id' => Yii::t('main', 'Company ID'),
            'status' => Yii::t('main', 'Status'),
            'pinfl' => Yii::t('main', 'JSHSHIR'),
            'fullname' => Yii::t('main', 'F.I.SH'),
            'passport_serie' => Yii::t('main', 'Pasport seriyasi'),
            'passport_number' => Yii::t('main', 'Pasport raqami'),
            'mail' => Yii::t('main', 'Elektron manzil'),
            'phone' => Yii::t('main', 'Telefon'),
            'birthday' => Yii::t('main', 'Tug’ilgan yili'),
            'position' => Yii::t('main', 'Lavozim'),
            'company_name' => Yii::t('main', 'Kompaniya'),
        ];
    }

    public function isPinfl($attribute)
    {
        if (!preg_match('/^[0-9]{14}$/', $this->$attribute)) {
            $this->addError($attribute, t("{attribute} uzunligi 14 ta butun sondan iborat bo'lishi kerak"));
        }
    }

    public function isPassportNumber($attribute)
    {
        if (!preg_match('/^[0-9]{7}$/', $this->$attribute)) {
            $this->addError($attribute, t("Pasport nomeri raqamlari 7 ta butun sondan iborat bo'lishi kerak"));
            return false;
        }
        return true;
    }

    public function getResult()
    {
        if($this->pinfl != $this->model->pinfl){
            $check = CommissionMemberResource::find()->where(['pinfl' => $this->pinfl])->one();
            if ($check) {
                throw new Exception(t("Komisiya azosi avval qo'shilgan"));
            }
        }
        $user = UserResource::find()->where(['id' => $this->model->user_id])->one();
        if (!$user) {
            throw new Exception(t("Foydanaluvchining akkounti topilmadi."));
        }
        $transaction = Yii::$app->db->beginTransaction();
        $this->model->pinfl = $this->pinfl;
        $this->model->fullname = $this->fullname;
        $this->model->passport_serie = $this->passport_serie;
        $this->model->passport_number = $this->passport_number;
        $this->model->mail = $this->mail;
        $this->model->phone = $this->phone;
        $this->model->birthday = date("Y-m-d", strtotime($this->birthday));
        $this->model->position = $this->position;
        $this->model->company_name = $this->company_name;
        $this->model->status = TenderEnum::STATUS_ACTIVE;

        if ($this->model->save()) {
            $user->username = $this->pinfl;
            $user->email = $this->mail;
            $user->generateAuthKey();
            $user->setPassword($this->pinfl);
            $user->status = UserEnum::STATUS_ACTIVE;
            $user->company_id = $this->model->company_id;

            if (!$user->save()) {
                $transaction->rollBack();
                $this->addErrors($user->errors);
                return false;
            }
            $transaction->commit();
            return true;
        } else {
            $transaction->rollBack();
            $this->addErrors($this->model->errors);
            return false;
        }
    }
}