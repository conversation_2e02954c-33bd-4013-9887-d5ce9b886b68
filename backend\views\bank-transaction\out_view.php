<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/**
 * @var yii\web\View $this
 * @var common\models\bank\BankTransactionOut $model
 */

$this->params['breadcrumbs'][] = ['label' => t("Bank transaction Out"), 'url' => ['out']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="black-list-view">
    <div class="card">

        <div class="card-body">
            <?php echo DetailView::widget([
                'model' => $model,
                'attributes' => [
                    'id',
                    'bank_id',
                    'state',
                    'type',
                    [
                        'attribute' => 'amount',
                        'value' => function($model){
                            return number_format($model->amount / 100);
                        }
                    ],
                    'commission_amount',
                    'purpose',
                    'service_id',
                    'receiver_mfo',
                    'receiver_account',
                    'company_tin',
                    'company.title',
                    'doc_id',
                    'response_code',
                    'payment_status',
                    'company_transaction_id',
                    'request_body',
                    'created_at',
                ],
            ]) ?>
        </div>
    </div>
</div>
