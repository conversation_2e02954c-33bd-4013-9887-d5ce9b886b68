<?php


namespace api\modules\common\controllers;


use api\modules\common\filters\AffiliatesFilter;
use api\modules\common\forms\AffiliatesCreateForm;
use api\modules\common\forms\CompanyBalanceWithdraw;
use api\modules\common\resources\AffiliatesResource;
use Yii;
use api\modules\common\filters\CompanyTransactionExcelExportFilter;
use api\modules\common\filters\CompanyTransactionFilter;
use api\modules\common\resources\TransactionRefundsResource;
use api\components\ApiController;
use api\modules\common\filters\CompanyBankAccountBudgetFilter;
use api\modules\common\filters\CompanyBankAccountFilter;
use api\modules\common\forms\CompanyBankAccountForm;
use api\modules\common\forms\CompanyBankAccountSetMainForm;
use common\enums\PkcsEnum;
use common\models\CompanyBankAccount;
use yii\web\NotFoundHttpException;

class CompanyController extends ApiController
{

    public function actionTransaction()
    {
        return $this->sendResponse(
            new CompanyTransactionFilter(),
            Yii::$app->request->queryParams
        );
    }
    public function actionTransactionExcelExport()
    {
        return $this->sendFile(
            new CompanyTransactionExcelExportFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionBalanceWithdraw(): array
    {
        return $this->sendResponse(
            new CompanyBalanceWithdraw(new TransactionRefundsResource),
            Yii::$app->request->bodyParams
        );
    }

    public function actionBankAccount()
    {
        return $this->sendResponse(
            new CompanyBankAccountFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionBankAccountBudget()
    {
        //common/company/bank-account
        return $this->sendResponse(
            new CompanyBankAccountBudgetFilter(),
            Yii::$app->request->queryParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionMainBankAccount(): array
    {
        $model = CompanyBankAccount::find()->where(['company_id' => Yii::$app->user->identity->company_id, 'is_main' => 1])->one();
        if (!$model) throw new NotFoundHttpException("Hisob raqami topilmadi");

        return $this->sendModel($model);
    }

    public function actionAddBankAccount(): array
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs7 = $this->verifyPkcs7($body);
        return $this->sendResponsePost(
            new CompanyBankAccountForm(),
            $decodedPkcs7,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_COMPANY_ADD_BANK_ACCOUNT
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionBankAccountSetMain($id): array
    {
        return $this->sendResponse(
            new CompanyBankAccountSetMainForm($this->findBankAccountOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws NotFoundHttpException
     */
    private function findBankAccountOne($id): CompanyBankAccount
    {
        $model = CompanyBankAccount::findOne(['company_id' => Yii::$app->user->identity->company_id, 'id' => $id]);
        if (!$model) throw new NotFoundHttpException("Hisob raqami topilmadi");

        return $model;
    }

    public function actionAffiliatesList()
    {
        return $this->sendResponse(
            new AffiliatesFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionAffiliatesCreate()
    {
        return $this->sendResponse(
            new AffiliatesCreateForm(new AffiliatesResource()),
            Yii::$app->request->bodyParams
        );
    }


}