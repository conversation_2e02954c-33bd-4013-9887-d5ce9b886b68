<?php


namespace api\modules\common\controllers;


use api\components\ApiController;
use api\modules\common\filters\AffiliatesFilter;
use api\modules\common\filters\BalanceWithdrawToAccountFilter;
use api\modules\common\filters\CompanyBalanceFilter;
use api\modules\common\filters\CompanyBalanceHistoryFilter;
use api\modules\common\filters\CompanyBankAccountBudgetFilter;
use api\modules\common\filters\CompanyBankAccountFilter;
use api\modules\common\forms\AffiliatesCreateForm;
use api\modules\common\forms\CompanyBankAccountForm;
use api\modules\common\forms\CompanyBankAccountSetMainForm;
use api\modules\common\forms\CompanyBankAccountUpdateForm;
use api\modules\common\resources\AffiliatesResource;
use common\models\CompanyBankAccount;
use Yii;
use yii\web\NotFoundHttpException;

class CompanyController extends ApiController
{
    public function actionBalance()
    {
        return $this->sendResponse(
            new CompanyBalanceFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionBalanceHistory()
    {
        return $this->sendResponse(
            new CompanyBalanceHistoryFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionBalanceWithdrawToAccount()
    {
        return $this->sendResponse(
            new BalanceWithdrawToAccountFilter(),
            Yii::$app->request->queryParams
        );
    }


    public function actionBankAccount()
    {
        return $this->sendResponse(
            new CompanyBankAccountFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionBankAccountBudget()
    {
        //common/company/bank-account
        return $this->sendResponse(
            new CompanyBankAccountBudgetFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionMainBankAccount()
    {
        $model = CompanyBankAccount::find()->where(['company_id' => Yii::$app->user->identity->company_id, 'is_main' => 1])->one();
        if (!$model) throw new NotFoundHttpException("Hisob raqami topilmadi");

        return $this->sendModel($model);
    }

    public function actionAddBankAccount()
    {
        return $this->sendResponse(
            new CompanyBankAccountForm(),
            Yii::$app->request->bodyParams
        );
    }

    public function actionBankAccountSetMain($id)
    {
        return $this->sendResponse(
            new CompanyBankAccountSetMainForm($this->findBankAccountOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionUpdateBankAccount($id)
    {
        return $this->sendResponse(
            new CompanyBankAccountUpdateForm($this->findBankAccountOne($id)),
            Yii::$app->request->bodyParams
        );
    }


    private function findBankAccountOne($id)
    {
        $model = CompanyBankAccount::find()->where(['company_id' => Yii::$app->user->identity->company_id, 'id' => $id])->one();
        if (!$model) throw new NotFoundHttpException("Hisob raqami topilmadi");

        return $model;
    }

    public function actionAffiliatesList()
    {
        return $this->sendResponse(
            new AffiliatesFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionAffiliatesCreate()
    {
        return $this->sendResponse(
            new AffiliatesCreateForm(new AffiliatesResource()),
            Yii::$app->request->bodyParams
        );
    }

}