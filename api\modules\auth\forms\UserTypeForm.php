<?php

namespace app\modules\auth\forms;

use common\enums\UserEnum;
use Yii;
use api\components\BaseRequest;
use api\modules\auth\resources\UserResource;
use common\models\UserToken;
use yii\db\Exception;
use yii\web\UnauthorizedHttpException;

class UserTypeForm extends BaseRequest
{
    /**
     * @throws Exception
     * @throws UnauthorizedHttpException
     */
    public function getResult()
    {
        /** @var UserResource $user */
        $user = Yii::$app->user->identity;

        if (!$user) {
            throw new UnauthorizedHttpException("Avval tizimga kiring");
        }
        if (!is_null($user->user_type) && is_int($user->user_type))
        {
            if ($user->user_type == UserEnum::USER_TYPE_CUSTOMER) {
                $user->user_type = UserEnum::USER_TYPE_SUPPLIER;
            } else {
                $user->user_type = UserEnum::USER_TYPE_CUSTOMER;
            }
            $user->save(false);
        }
        return true;
    }
}
