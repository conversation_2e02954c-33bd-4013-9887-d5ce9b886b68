<?php

namespace api\modules\auction\resources;

use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\CompanyResource;
use api\modules\common\resources\FileResource;
use api\modules\common\resources\RegionResource;
use api\modules\common\resources\CompanyShortResource;
use common\models\auction\Auction;
use common\models\auction\AuctionHistory;
use common\models\auction\AuctionOffer;
use yii\helpers\ArrayHelper;

class AuctionResource extends Auction
{
    public function fields()
    {
        return [
            'id',
            'lot',
            'status',
            'total_sum' => function($model){
                return $model->total_sum / 100;
            },
            'updated_at',
            'auction_end',
            'cancel_reason',
            'cancel_date',
            'delivery_period',
            'address',
            'created_at',
            'currentPrice',
            'age',
            'delivery_basis',
            'responsible_person',
            'responsible_person_phone',
        ];
    }

    public function extraFields()
    {
        return [
            'classifiers',
            'company',
            'companyShort',
            'offers',
            'offers.company',
            'files',
            'auctionClassifiers',
            'auctionConditions',
            'history',
            'moderating',
            'auctionRequestCount',
        ];
    }

    public static function generateLotNumber($id)
    {

        $YY = substr(date('Y'), -2);
        $P = env('LOT_OPERATOR_NUMBER', 5);
        $CUSTOMER_TYPE = env('LOT_CUSTOMER_TYPE', 2);
        $K = 1;
        $CCC = '007';

        return $YY . $P . $CUSTOMER_TYPE . $K . $CCC . sprintf('%06d', $id);

    }

    public function getClassifiers()
    {
        $auctionClassifiers = $this->auctionClassifiers;
        $classfiers = ClassifierResource::find()->where(['id' => ArrayHelper::getColumn($auctionClassifiers, 'classifier_id')])->all();
        return $classfiers;
    }

    public function getCompany()
    {
        return $this->hasOne(CompanyResource::class, ['id' => 'company_id']);
    }

    public function getCompanyShort()
    {
        return $this->hasOne(CompanyShortResource::class, ['id' => 'company_id']);
    }

    public function getAuctionRequestCount()
    {
        return AuctionOffer::find()->where(['auction_id' => $this->id])->count();
    }

    public function getClassifierCategory()
    {
        return $this->hasOne(ClassifierCategoryResource::class, ['id' => 'classifier_category_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getRegion()
    {
        return $this->hasOne(RegionResource::class, ['id' => 'region_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getAuctionClassifiers()
    {
//        return AuctionClassifierResource::find()->where(['auction_id' => $this->id])->all();
        return $this->hasMany(AuctionClassifierResource::class, ['auction_id' => 'id']);
    }


    public function getAuctionConditions()
    {
        return AuctionConditionResource::find()->where(['auction_id' => $this->id])->asArray()->all();
//        return $this->hasMany(AuctionConditionResource::class, ['auction_id' => 'id']);
    }

    public function getHistory()
    {
        return AuctionHistory::find()->where(['auction_id' => $this->id])->orderBy(['created_at' => SORT_ASC])->asArray()->all();
//        return $this->hasMany(AuctionConditionResource::class, ['auction_id' => 'id']);
    }

    public function getModerating()
    {
        return TenderModeratorLogResource::find()->where(['auction_id' => $this->id])->orderBy(['created_at' => SORT_ASC])->one();
//        return $this->hasMany(AuctionConditionResource::class, ['auction_id' => 'id']);
    }

    /**
     * Gets query for [[AuctionFiles]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getAuctionFiles()
    {
        return $this->hasMany(AuctionFileResource::class, ['auction_id' => 'id']);
    }

    /**
     * Gets query for [[Files]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getFiles()
    {
        return $this->hasMany(FileResource::class, ['id' => 'file_id'])->viaTable('auction_file', ['auction_id' => 'id']);
    }
}
