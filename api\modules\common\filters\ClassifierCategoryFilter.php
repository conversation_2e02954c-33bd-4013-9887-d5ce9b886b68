<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;

class ClassifierCategoryFilter extends BaseRequest
{
  public $plan_schedule_id;

  public function rules()
  {
    return [
      ['plan_schedule_id', 'integer'],
    ];
  }

  public function getResult()
  {
    $model = ClassifierCategoryResource::find()
      ->join("inner join", "classifier", "classifier.classifier_category_id=classifier_category.id")
      ->join("inner join", "plan_schedule_classifier", "plan_schedule_classifier.classifier_id=classifier.id");

    if($this->plan_schedule_id) {
        $model->andWhere(['plan_schedule_classifier.plan_schedule_id' => $this->plan_schedule_id]);
    } else {
        $model->andWhere('0=1');
    }


    return $model->all();
  }
}
