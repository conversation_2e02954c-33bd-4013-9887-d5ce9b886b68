<?php

use yii\db\Migration;

class m250617_110730_create_after_virtual_transaction_insert_trigger extends Migration
{
    /**
     * {@inheritdoc}
     * @throws \yii\db\Exception
     */
    public function up()
    {
        // Drop the trigger and function if they already exist
        $this->execute("DROP TRIGGER IF EXISTS virtual_transaction_after_insert_trigger ON virtual_transaction;");
        $this->execute("DROP FUNCTION IF EXISTS virtual_transaction_after_insert_func();");

        // Create trigger function
        $this->execute("
        CREATE FUNCTION virtual_transaction_after_insert_func()
        RETURNS TRIGGER AS \$\$
        BEGIN
            IF NEW.prefix_account NOT IN ('50111', '50113', '50114') THEN
                IF (NEW.credit = 0 AND NEW.debit > 0) THEN
                    UPDATE company_virtual_account 
                    SET price = GREATEST(COALESCE(price, 0) - NEW.debit, 0),
                        updated_at = NEW.updated_at
                    WHERE id = NEW.debit_account_id 
                    AND company_id = NEW.debit_company_id;
                ELSIF (NEW.debit = 0 AND NEW.credit > 0) THEN
                    UPDATE company_virtual_account 
                    SET price = COALESCE(price, 0) + NEW.credit,
                        updated_at = NEW.updated_at
                    WHERE id = NEW.credit_account_id 
                    AND company_id = NEW.credit_company_id;
                END IF;
            END IF;
            RETURN NULL;
        END;
        \$\$ LANGUAGE plpgsql;
    ");

        // Create trigger
        $this->execute("
        CREATE TRIGGER virtual_transaction_after_insert_trigger
        AFTER INSERT ON virtual_transaction
        FOR EACH ROW
        EXECUTE FUNCTION virtual_transaction_after_insert_func();
    ");

        // Check if trigger exists
        $check = "
        SELECT COUNT(*) 
        FROM pg_trigger 
        WHERE tgname = 'virtual_transaction_after_insert_trigger';
    ";
        $row = Yii::$app->db->createCommand($check)->queryScalar();
        if (!$row) {
            throw new \yii\db\Exception('Create trigger failed');
        }
    }

    /**
     * {@inheritdoc}
     * @throws \yii\db\Exception
     */
    public function down()
    {
        // Drop the trigger and function if they already exist
        $this->execute("DROP TRIGGER IF EXISTS virtual_transaction_after_insert_trigger ON virtual_transaction;");
        $this->execute("DROP FUNCTION IF EXISTS virtual_transaction_after_insert_func();");
    }
}
