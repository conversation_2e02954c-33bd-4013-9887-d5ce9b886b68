<?php


namespace api\modules\backend\filters;


use api\components\BaseRequest;
use api\modules\backend\resources\TenderResource;
use common\enums\TenderEnum;

class TenderHistoryFilter extends BaseRequest
{
    public $pageNo = 0;
    public $pageSize = 10;

    public function rules (){
        return [
            [['pageNo', 'pageSize'], 'integer'],
        ];
    }

    public function getResult()
    {
        $model = TenderResource::find()->join('inner join', 'tender_moderator_log', 'tender_moderator_log.tender_id=tender.id')
            ->where('tender.state != '.TenderEnum::STATE_NEW)
            ->andWhere(['tender_moderator_log.created_by' => \Yii::$app->user->id]);

        $pagination = new \yii\data\Pagination([
            'totalCount' => $model->count(),
            'pageSize' => $this->pageSize,
            'page' => $this->pageNo
        ]);

        $model->offset($pagination->offset);
        $model->limit($pagination->pageSize);

        return [
            'meta' => [
                'pageCount' => $pagination->pageCount,
                'pageSize' => $pagination->pageSize,
                'pageNo' => $pagination->page,
                'totalCount' => $pagination->totalCount
            ],
            'data' => $model->all(),

        ];


    }
}