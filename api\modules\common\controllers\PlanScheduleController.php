<?php

namespace api\modules\common\controllers;

use api\components\ApiController;
use api\modules\common\filters\PlanScheduleBudgetFilter;
use api\modules\common\filters\PlanScheduleFilter;
use api\modules\common\filters\PlanScheduleListFilter;
use api\modules\common\filters\PlanScheduleShortFilter;
use api\modules\common\forms\PlanScheduleBudgetForm;
use api\modules\common\forms\PlanScheduleBudgetUpdateForm;
use api\modules\common\forms\PlanScheduleForm;
use api\modules\common\forms\PlanScheduleUpdateForm;
use api\modules\common\resources\PlanScheduleResource;
use Yii;
use yii\filters\auth\HttpBearerAuth;
use yii\web\NotFoundHttpException;

/**
 * Group controller for the `plan-schedule` module
 */
class PlanScheduleController extends ApiController
{
    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['bearerAuth'] = [
            'class' => HttpBearerAuth::class,
            'except' => [
                'index',
            ]
        ];
        return $parent;
    }

    public function actionIndex()
    {
        //common/plan-schedule/index
        return $this->sendResponse(
            new PlanScheduleFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionList(): array
    {
        return $this->sendResponse(
            new PlanScheduleListFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionIndexBudget()
    {
        //common/plan-schedule/index-budget
        return $this->sendResponse(
            new PlanScheduleBudgetFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionShortList()
    {
        return $this->sendResponse(
            new PlanScheduleShortFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionCreate()
    {
        //common/plan-schedule/create
        return $this->sendResponse(
            new PlanScheduleForm(),
            Yii::$app->request->bodyParams
        );
    }

    public function actionCreateBudget()
    {
        //common/plan-schedule/create-budget
        return $this->sendResponse(
            new PlanScheduleBudgetForm(),
            Yii::$app->request->bodyParams
        );
    }

//    public function actionUpdateBudget($id)
//    {
//        //common/plan-schedule/update-budget
//        return $this->sendResponse(
//            new PlanScheduleBudgetUpdateForm($this->findOne($id)),
//            Yii::$app->request->bodyParams
//        );
//    }

//    public function actionUpdate($id)
//    {
//        //common/plan-schedule/update
//        return $this->sendResponse(
//            new PlanScheduleUpdateForm($this->findOne($id)),
//            Yii::$app->request->bodyParams
//        );
//    }

    public function actionView($id)
    {
        //common/plan-schedule/view
        return $this->sendModel($this->findOne($id));
    }


    private function findOne($id)
    {
        $model = PlanScheduleResource::find()->notDeletedAndFromCompany()->where(['id' => $id])->one();

        if (!$model) throw new NotFoundHttpException("Plan schedule is not found");

        return $model;
    }
}
