<?php


namespace app\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderRequestResource;
use yii\base\Exception;

class TenderForSecretaryRateLocalProducerForm extends BaseRequest
{

    public TenderRequestResource $model;
    public $vote;

    /**
     * @throws Exception
     */
    public function __construct($id, $params = [])
    {
        $model = TenderRequestResource::findOne(['id' => $id]);
        if (!$model) {
            throw new Exception('Tender request not found');
        }
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [['vote'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['vote'], 'checkState'],
            [['vote'], 'default', 'value' => 0],
        ];

    }

    public function checkState()
    {
        if ($this->vote == 0 || $this->vote == 1) {
            return true;
        } else {
            $this->addError("vote", t("State qiymatlari, Muvofiq = 0, Muvofiq emas = 1"));
            return false;
        }
    }

    /**
     * @throws \yii\db\Exception
     */
    public function getResult(): bool
    {

        if (!$this->model->preference_local_producer) {
            $this->addError("vote", t("Yetkazib beruvchi mahalliy ishlab chiqaruvchi emas"));
            return false;
        }
        $this->model->is_preference_local_producer = $this->vote;
        if (!$this->model->save()) {
            $this->addErrors($this->model->errors);
            return false;
        }
        return true;
    }
}