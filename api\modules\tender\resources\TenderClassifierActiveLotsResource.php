<?php


namespace api\modules\tender\resources;


use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\UnitResource;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderClassifier;
use function foo\func;

class TenderClassifierActiveLotsResource extends TenderClassifier
{

    public function fields()
    {
        return [
            'id',
            'title' => function ($model) {
                return $model->classifier->title_uz;
            },
            'classifier_id',
            'number_purchased',
            'unit' => function ($model) {
                return $model->planScheduleClassifier->unit;
            },
            'description',
            'category' => function ($model) {
                return $model->classifier->classifierCategory;
            },
            'price' => function ($model) {
                return $model->price / 100;
            },
            'expiry_date_value',
            'expiry_date_unit',
            'delivery_period',
            'month' => function ($model) {
                return $model->planScheduleClassifier->month;
            }

        ];
    }


    public function getUnit()
    {
        return $this->hasOne(UnitResource::class, ['id' => 'unit_id']);
    }

    public function getClassifier()
    {
        return $this->hasOne(ClassifierResource::class, ['id' => 'classifier_id']);
    }

    public function getPlanScheduleClassifier()
    {
        return $this->hasOne(PlanScheduleClassifierResource::class, ['id' => 'plan_schedule_classifier_id']);
    }

}