<?php


namespace api\modules\tender\resources;


use common\enums\TenderEnum;
use common\models\Tender;

class TenderResourceForClient extends Tender
{
    public function fields()
    {
        return [
            'id',
            'lot',
            'updated_at',
            'end_date',
            'title',
            'state',
            'purchase_currency',
            'criteria_evaluation_proposals',
            'tenderTotalPrice' => function($model){
                return $model->getTenderTotalPriceBase();
            },
            'company' => function ($model) {
                return $model->company ? $model->company->title . " ( " . $model->company->tin . " )" : '';
            },
            'request_at' => function ($model) {
                return $model->tenderRequest ? $model->tenderRequest->created_at : '';
            },
            'request_state' => function ($model) {
                return $model->tenderRequest ? $model->tenderRequest->status : '';
            },
            'request_status' => function ($model) {
                return $model->tenderRequest ? $model->tenderRequest->is_winner : '';
            },
            'request_id' => function ($model) {
                return $model->tenderRequest ? $model->tenderRequest->id : '';
            },
            'protest' => function ($model) {
                $protest = $model->protest;
                return $protest ? ['description' => $protest->description, 'status' => $protest->status, 'created_at' => $protest->created_at] : '';
            },
        ];
    }

    public function extraFields()
    {
        return [

        ];
    }

    public function getProtest()
    {
        return TenderDiscussionResource::find()->where(['tender_id' => $this->id])->one();
    }

    public function getCompany()
    {
        return $this->hasOne(CompanyResource::class, ['id' => 'company_id']);
    }

    public function getTenderRequestRating()
    {
        return $this->hasMany(TenderRequestRatingResource::class, ['tender_id' => 'id']);
    }

    public function getCommissionMemberRole()
    {
        $model = TenderCommissionMemberResource::find()->notDeleted()
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_id' => $this->id])
            ->andWhere(['commission_member_id' => \Yii::$app->user->identity->commissionMemberId])
            ->one();
        return $model ? $model->role : "yoq";
    }


    public function getTenderClassifiers()
    {
        return $this->hasMany(TenderClassifierResource::class, ['tender_id' => 'id'])->andWhere([TenderClassifierResource::tableName() . '.status' => TenderEnum::STATUS_ACTIVE]);;
    }

//    public function getTenderTotalPrice()
//    {
//        $price = 0;
//        foreach ($this->tenderClassifiers as $classifier) {
//            $price += $classifier->price;
//        }
//        return $price;
//    }

    public function getTenderRequest()
    {
        return TenderRequestResource::find()
//            ->notDeleted()
//            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_id' => $this->id])
            ->andWhere(['company_id' => \Yii::$app->user->identity->company_id])
            ->orderBy(['created_at' => SORT_DESC])
            ->one();
    }


}