<?php


namespace api\modules\client\resources;


use api\modules\common\resources\CompanyResource;
use api\modules\common\resources\DistrictResource;
use api\modules\common\resources\FileResource;
use api\modules\common\resources\RegionResource;
use common\enums\TenderEnum;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\Tender;

class TenderResource extends Tender
{
    public function fields(){
        return [
            'id',
            'type',
            'lot',
            'created_at',
            'tenderTotalPrice' => function($model){
                return $model->getTenderTotalPriceBase();
            },
            'title',
            'language',
            'purchase_currency',
            'description',
            'amount_deposit',
            'accept_guarantee_bank',
            'method_payment',
            'advance_payment_percentage',
            'advance_payment_percentage',
            'advance_payment_period',
            'unblocking_type',
            'contact_fio',
            'contact_position',
            'contact_phone',
            'address',
            'delivery_phone',
            'publish_days',
            'end_date',
            'preference_local_producer',
            'expertise_conclusion_number',
            'expertise_conclusion_date'
        ];
    }

    public function extraFields (){
        return [
            'contactFile',
            'technicalDocumentFile',
            'technicalTaskFile',
            'planSchedule',
            'region',
            'district',
            'tenderTotalPrice' => function($model){
                return $model->getTenderTotalPriceBase();
            },
            'tenderClassifiers',
            'tenderRequirements',
            'company',
            'myRequest',
            'myAnswers',
            'qualificationSelection'
        ];
    }

    public function getMyRequest(){
        return TenderRequestResource::find()->notDeleted()->andWhere(['tender_id' => $this->id, 'company_id' => \Yii::$app->user->identity->company_id])->one();
    }

    public function getMyAnswers(){
        return TenderRequirementsAnswerResource::find()->notDeletedAndFromCompany()->andWhere(['tender_id' => $this->id])->all();
    }

    public function getQualificationSelection()
    {
        return TenderQualificationSelectionResource::find()->notDeletedAndFromCompany()->andWhere(['tender_id' => $this->id])->all();
    }


    public function getCompany()
    {
        return $this->hasOne(CompanyResource::class, ['id' => 'company_id']);
    }


    public function getContactFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'contact_file_id']);
    }
//
//
    public function getDistrict()
    {
        return $this->hasOne(DistrictResource::class, ['id' => 'district_id']);
    }

    public function getRegion()
    {
        return $this->hasOne(RegionResource::class, ['id' => 'region_id']);
    }
//
//
    public function getTechnicalDocumentFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'technical_document_file_id']);
    }

    public function getTechnicalTaskFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'technical_task_file_id']);
    }
//
//    /**
//     * Gets query for [[TenderClassifiers]].
//     *
//     * @return \yii\db\ActiveQuery
//     */
    public function getTenderClassifiers()
    {
        return TenderClassifierResource::find()->notDeleted()->andWhere(['tender_id' => $this->id])->andWhere('status='.TenderEnum::STATUS_ACTIVE )->all();
    }

    public function getTenderRequirements()
    {
        return TenderRequirementsResource::find()->notDeleted()->andWhere(['tender_id' => $this->id])->andWhere('status='.TenderEnum::STATUS_ACTIVE )->all();
    }



    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

//    public function getTenderTotalPrice(){
//        $price = 0;
//        foreach ($this->tenderClassifiers as $classifier){
//            $price+=$classifier->price;
//        }
//        return $price;
//    }


}