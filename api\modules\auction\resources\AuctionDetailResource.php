<?php

namespace api\modules\auction\resources;

use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\CompanyBankAccountBudgetResource;
use api\modules\common\resources\FileResource;
use api\modules\common\resources\RegionResource;
use common\enums\StatusEnum;
use common\models\auction\Auction;
use common\models\auction\AuctionHistory;

class AuctionDetailResource extends Auction
{
    public function fields()
    {
        return [
            'id',
            'lot',
            'company_id',
            'account_id' => function ($model) {
                $account = CompanyBankAccountBudgetResource::findOne(['account' => $model->account]);
                return $account->id;
            },
            'total_sum' => function ($model) {
                return $model->total_sum / 100;
            },
            'cancel_reason',
            'auction_end',
            'cancel_date',
            'payment_status',
            'payment_date',
            'delivery_period',
            'payment_period',
            'receiver_email',
            'receiver_phone',
            'region_id',
            'zip_code',
            'address',
            'created_at',
            'nextPrice',
            'currentPrice',
            'currentOffer',
            'account',
            'delivery_basis',
            'responsible_person',
            'responsible_person_phone',

        ];
    }

    public function extraFields()
    {
        return [
            'company',
            'offers',
            'offers.company',
            'files',
            'auctionClassifiers',
            'auctionConditions',
            'history',
            'moderating',
            'auctionFiles'
        ];
    }

    public function getClassifierCategory()
    {
        return $this->hasOne(ClassifierCategoryResource::class, ['id' => 'classifier_category_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getRegion()
    {
        return $this->hasOne(RegionResource::class, ['id' => 'region_id']);
    }


    public function getAuctionClassifiers()
    {
        return AuctionClassifierResource::find()->where(['auction_id' => $this->id, 'status' => StatusEnum::STATUS_ACTIVE])->all();
//        return $this->hasMany(AuctionClassifierResource::class, ['auction_id' => 'id']);
    }


    public function getAuctionConditions()
    {
        return AuctionConditionResource::find()->where(['auction_id' => $this->id, 'status' => StatusEnum::STATUS_ACTIVE])->all();
//        return $this->hasMany(AuctionConditionResource::class, ['auction_id' => 'id']);
    }

    public function getHistory()
    {
        return AuctionHistory::find()->where(['auction_id' => $this->id])->orderBy(['created_at' => SORT_ASC])->all();
//        return $this->hasMany(AuctionConditionResource::class, ['auction_id' => 'id']);
    }

    public function getModerating()
    {
        return TenderModeratorLogResource::find()->where(['auction_id' => $this->id])->orderBy(['created_at' => SORT_ASC])->one();
//        return $this->hasMany(AuctionConditionResource::class, ['auction_id' => 'id']);
    }


    public function getAuctionFiles()
    {
        return AuctionFileResource::find()->where(['auction_id' => $this->id, 'deleted_at' => null])->orderBy(['created_at' => SORT_ASC])->all();
//
//        return $this->hasMany(AuctionFileResource::class, ['auction_id' => 'id']);
    }

//    /**
//     * Gets query for [[Files]].
//     *
//     * @return \yii\db\ActiveQuery
//     */
//    public function getFiles()
//    {
//        return $this->hasMany(FileResource::class, ['id' => 'file_id'])->viaTable('auction_file', ['auction_id' => 'id']);
//    }
}
