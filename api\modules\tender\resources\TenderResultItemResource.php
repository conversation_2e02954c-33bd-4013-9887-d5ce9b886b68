<?php


namespace api\modules\tender\resources;


use common\models\TenderResultItem;

class TenderResultItemResource extends TenderResultItem
{

    public static function createItem($tenderId, $requestId, $companyId, $step, $qualification_selection_id = null, $description = null)
    {
        $model = new self();
        $model->tender_id = $tenderId;
        $model->request_id = $requestId;
        $model->company_id = $companyId;
        $model->step = $step;
        $model->qualification_selection_id = $qualification_selection_id;
        $model->description = $description;
        return $model->save(false);
    }
}