<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderActionRequestResource;
use api\modules\tender\resources\TenderResource;
use common\enums\OperationTypeEnum;
use common\enums\TenderEnum;
use common\models\Tender;
use common\models\VirtualTransaction;
use console\models\TenderCommissionVote;
use console\models\TenderQualificationSelection;
use console\models\TenderRequest;
use console\models\TenderRequestValues;
use console\models\TenderRequirementsAnswer;

class TenderActionRequestSecretaryEndForm extends BaseRequest
{

    public TenderActionRequestResource $model;

    public function __construct(TenderActionRequestResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function getResult()
    {
        $commissionId = \Yii::$app->user->identity->commissionMemberId;
        $tender = $this->model->tender;
        $date = date("Y-m-d H:i:s");
        /**
         * @var $tender TenderResource
         */
        $tenderCom = $tender->getCommissionMember($commissionId);
        if ($tenderCom->role != TenderEnum::ROLE_SECRETARY) {
            $this->addError("error", t("Faqat sekretar roli uchun ruxsat mavjud"));
            return false;
        }

        if ($this->model->status == TenderEnum::TENDER_ACTION_STATUS_READY && $this->model->type == TenderEnum::TENDER_ACTION_TYPE_CANCEL) {
            $transaction = \Yii::$app->db->beginTransaction();
            $tender->state = TenderEnum::STATE_CANCELLED;
            if (!$tender->save()) {
                $transaction->rollBack();
                $this->addErrors($tender->getErrors());
                return false;
            }

            $zalogs = VirtualTransaction::find()
                ->where([
                'tender_id' => $tender->id,
                'operation_type' => OperationTypeEnum::BLOCK_SALE_DEPOSIT,
                'parent_id' => null,
                ])
                ->andWhere(['>' , 'credit' , 0])
                ->all();
            echo ("zalog soni:" . count($zalogs)) . "\n";

            /** @var VirtualTransaction $zalog */
            foreach ($zalogs as $zalog) {
                $revertID = VirtualTransaction::saveTransaction(
                    $zalog->creditCompany,
                    $zalog->debitCompany,
                    OperationTypeEnum::P_K_30201, // blocked deposit dan
                    $zalog->creditCompany->isBudget() ? OperationTypeEnum::P_B_31101 : OperationTypeEnum::P_K_30101, // asosiy hisobga
                    $zalog->credit,
                    'Tender bekor qilingani uchun garov qaytarildi',
                    OperationTypeEnum::PRODUCT_NAME_TENDER,
                    $zalog->tender_id,
                    null,
                    OperationTypeEnum::UNBLOCK_SALE_DEPOSIT
                );

                $zalog->parent_id = $revertID;
                if (!$zalog->save()) {
                    $transaction->rollBack();
                    $this->addErrors($zalog->errors);
                    return false;
                }

//                echo "zalog qaytarildi, transaction ID: " . $revertID . "\n";
            }

            // Kommissiya qaytarish
            $block_kommisions = VirtualTransaction::find()
                ->where([
                    'tender_id' => $tender->id,
                    'operation_type' => OperationTypeEnum::BLOCK_SALE_COMMISSION,
                    'parent_id' => null,
                ])
                ->andWhere(['>', 'credit', 0])
                ->all();

            /** @var VirtualTransaction $commission */
            foreach ($block_kommisions as $commission) {
                $revertID = VirtualTransaction::saveTransaction(
                    $commission->creditCompany,
                    $commission->debitCompany,
                    OperationTypeEnum::P_K_30202, // blocked commission dan
                    $commission->creditCompany->isBudget() ? OperationTypeEnum::P_B_31101 : OperationTypeEnum::P_K_30101, // asosiy hisobga
                    $commission->credit,
                    'Tender bekor qilingani uchun komisiya qaytarildi',
                    OperationTypeEnum::PRODUCT_NAME_TENDER,
                    $commission->tender_id,
                    null,
                    OperationTypeEnum::UNBLOCK_SALE_COMMISSION
                );

                $commission->parent_id = $revertID;
                if (!$commission->save()) {
                    $transaction->rollBack();
                    $this->addErrors($commission->errors);
                    return false;
                }

//                echo "komisiya qaytarildi, transaction ID: " . $revertID . "\n";
            }
            $tenderRequest = TenderRequest::find()
                ->where('tender_id = ' . $tender->id . ' and status = 300')
                ->all();
            if ($tenderRequest && count($tenderRequest) > 0) {
                foreach ($tenderRequest as $req) {

                    $req->state = TenderEnum::REQUEST_STATE_REJECTED;
                    $req->status = TenderEnum::STATUS_NOT_ACTIVE;
                    $req->is_winner = TenderEnum::IS_WINNER_NO;
                    if ($req->save()) {

                        TenderRequestValues::updateAll(
                            ['status' => TenderEnum::STATUS_NOT_ACTIVE],
                            ['tender_request_id' => $req->id, 'status' => TenderEnum::STATUS_ACTIVE]
                        );

                        TenderQualificationSelection::updateAll(['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s")], [
                            'tender_id' => $tender->id,
                            'tender_request_id' => $req->id,
                            'status' => TenderEnum::STATUS_ACTIVE
                        ]);

                        TenderRequirementsAnswer::updateAll(['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s")], [
                            'tender_id' => $tender->id,
                            'tender_request_id' => $req->id,
                            'status' => TenderEnum::STATUS_ACTIVE
                        ]);

                    } else {
                        $transaction->rollBack();
                        $this->addErrors($req->errors);
                        return false;
                    }

                }
            }

            TenderCommissionVote::updateAll(
                ['status' => TenderEnum::STATUS_NOT_ACTIVE, 'updated_at' => $date],
                ['status' => TenderEnum::STATUS_ACTIVE, 'tender_id' => $tender->id]
            );

            $this->model->status = TenderEnum::TENDER_ACTION_STATUS_DONE;
            if (!$this->model->save()) {
                $transaction->rollBack();
                $this->addErrors($this->model->getErrors());
                return false;
            }
            $transaction->commit();
            return true;

        } else {
            $this->addError("error", t("So'rov tender bekor qilish uchun yakunlanmagan."));
            return false;
        }

    }
}