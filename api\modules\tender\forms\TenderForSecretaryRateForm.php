<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\client\resources\TenderRequestResource;
use api\modules\client\resources\TenderRequirementsAnswerResource;
use api\modules\tender\resources\TenderQualificationSelectionResource;
use api\modules\tender\resources\TenderRequestRatingResource;
use api\modules\tender\resources\TenderRequirementsResource;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;
use common\models\TenderActionRequest;
use Yii;
use yii\web\ForbiddenHttpException;

class TenderForSecretaryRateForm extends BaseRequest
{

    public TenderResource $model;
    public $grades = [];

    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [['grades'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['grades'], 'safe']
        ];

    }

    public function getResult()
    {
        if ($this->model->state != TenderEnum::STATE_READY_TO_RATING) {
            $this->addError("grades", t("Baholash bosqichida emas"));
            return false;
        }

        $checkCancelRequest = TenderActionRequest::find()->where(['tender_id' => $this->model->id])->andWhere(['status' => [TenderEnum::TENDER_ACTION_STATUS_NEW, TenderEnum::TENDER_ACTION_STATUS_READY_FOR_PROTOCOL, TenderEnum::TENDER_ACTION_STATUS_READY]])->one();
        if($checkCancelRequest){
            $this->addError("grades", t("Bekor qilish so'rovi mavjud, Baholash mumkin emas"));
            return false;
        }

        if (count($this->model->tenderRequirementExpert) != count($this->grades)) {
            $this->addError("grades", t("Barcha takliflar baholanishi kerak"));
            return false;
        }

        $commissionId = \Yii::$app->user->identity->commissionMemberId;
        $tenderCom = $this->model->getCommissionMember($commissionId);
        if($tenderCom->role != TenderEnum::ROLE_SECRETARY){
            $this->addError("grades", t("Sekretar roli uchun ruxsat mavjud"));
            return false;
        }


        $transaction = \Yii::$app->db->beginTransaction();

        foreach ($this->grades as $rate) {

            $tQualification = TenderQualificationSelectionResource::find()
                ->notDeleted()
                ->andWhere([
                    'tender_id' => $this->model->id,
                    'tender_request_id' => $rate['tender_request_id'],
                    'status' => TenderEnum::STATUS_ACTIVE,
                    'state' => TenderEnum::QUALIFIER_STATE_NEW
                ])
                ->exists();
            if ($tQualification) {
                $this->addError("grades", t("Malaka tanlovlarini baholash kerak"));
                return false;
            }

            $tRequirementAnswer = TenderRequirementsAnswerResource::find()->notDeleted()
                ->andWhere([
                    'id' => $rate['tender_requirement_answer_id'],
                    'tender_id' => $this->model->id
                ])
                ->andWhere([
                    'tender_request_id' => $rate['tender_request_id']
                ])
                ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
                ->one();

            if (!$tRequirementAnswer) {
                $this->addError("tender_requirement_answer_id", t("Taklif topilmadi"));
                return false;
            }

            $tenderRequirements = TenderRequirementsResource::findOne($tRequirementAnswer->tender_requirements_id);
            if (!$tenderRequirements) {
                $this->addError("tender_requirement_answer_id", t("Tender talabi topilmadi"));
                return false;
            }
            if (!isset($rate['ball'])) {
                $this->addError("ball", t("ball yuborilmagan"));
                return false;
            }

            if ($rate['ball'] >= $tenderRequirements->min_ball && $rate['ball'] <= $tenderRequirements->max_ball) {
                $rating = TenderRequestRatingResource::find()
                    ->notDeleted()
                    ->andWhere(['tender_id' => $this->model->id])
                    ->andWhere(['tender_requirement_answer_id' => $tRequirementAnswer->id])
                    ->andWhere(['tender_request_id' => $rate['tender_request_id']])
                    ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
                    ->one();
                if (!$rating) {
                    $rating = new TenderRequestRatingResource();
                    $rating->ball = $rate['ball'];
                    $rating->description = isset($rate['description']) ? $rate['description'] : '';
                    $rating->tender_requirement_answer_id = $tRequirementAnswer->id;
                    $rating->tender_request_id = $rate['tender_request_id'];
                    $rating->tender_id = $this->model->id;
                    $rating->status = TenderEnum::STATUS_ACTIVE;
                } else {
                    if (isset($rate['ball']))
                        $rating->ball = $rate['ball'];
                    if (isset($rate['description']))
                        $rating->description = $rate['description'];
                }

                if (!$rating->save()) {
                    $transaction->rollBack();
                    $this->addErrors($rating->errors);
                    return false;
                }

            } else {
                $transaction->rollBack();
                $this->addError("ball", t("Ball chegarasiga tushmadi"));

                return false;
            }
        }

        $transaction->commit();
        return true;
    }
}