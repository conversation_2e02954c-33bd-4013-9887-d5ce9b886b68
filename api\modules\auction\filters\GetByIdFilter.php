<?php

namespace api\modules\auction\filters;

use api\components\BaseRequest;
use api\modules\auction\resources\AuctionResource;

class GetByIdFilter extends BaseRequest
{
  public $id;

  public function __construct($id)
  {
    $this->id = $id;
  }

  public function rules()
  {
    return [
      ['id', 'required', 'message' => t('{attribute} yuborish majburiy')],
      ['id', 'integer'],
    ];
  }

  public function getResult()
  {
    return $this->id;
    return AuctionResource::findOne($this->id);
    //   ->with([
    //   'classifierCategory',
    //   'company',
    //   'offers',
    //   'auctionFiles',
    // ]);
  }
}
