<?php

namespace backend\services;


use common\models\Contract;
use PhpOffice\PhpSpreadsheet\IOFactory;
use common\models\BlackList;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Yii;
use yii\helpers\ArrayHelper;
use yii\helpers\FileHelper;

class ContractExcelService
{
    public function contractExcel($year,$month)
    {
        $contracts = Contract::find();
        if ($year){
            $contracts->andWhere(['extract(year from created_at)' => $year]);
        }
        if ($month){
            $contracts->andWhere(['extract(month from created_at)' => $month]);
        }
        $contracts = $contracts->all();
        return self::contractExcelFile($contracts);
    }

    public function contractExcelFile($contracts)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setCellValue('A1', '№');
        $sheet->setCellValue('B1', 'Yaratilgan vaqti');
        $sheet->setCellValue('C1', 'Turi');
        $sheet->setCellValue('D1', 'Shartnoma predmeti');
        $sheet->setCellValue('E1', 'Boshlangʻich qiymat');
        $sheet->setCellValue('F1', 'Mahsulot narxi');
        $sheet->setCellValue('G1', 'Shartnoma summasi');
        $sheet->setCellValue('H1', 'Buyurtmachi STIRI');
        $sheet->setCellValue('I1', 'Buyurtmachi nomi');
        $sheet->setCellValue('J1', 'Buyurtmachi hududi');
        $sheet->setCellValue('K1', 'Yetkazib beruvchi STIRI');
        $sheet->setCellValue('L1', 'Yetkazib beruvchi nomi');
        $sheet->setCellValue('M1', 'Yetkazib beruvchi hududi');

        // Styling
        $sheet->getStyle('A1:Y18')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A1:Y18')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $sheet->getStyle('A1:Y18')->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);
        $sheet->getStyle('A1:Y18')->getAlignment()->setWrapText(true);
        $sheet->getStyle('A1:Y18')->getFont()->setBold(true)->setSize(15)->setName('Times New Roman');


        $sheet->getRowDimension(2)->setRowHeight(80);
        $sheet->getRowDimension(3)->setRowHeight(80);
        $sheet->getRowDimension(4)->setRowHeight(30);
        $sheet->getRowDimension(5)->setRowHeight(30);
        $sheet->getRowDimension(6)->setRowHeight(30);
        $sheet->getRowDimension(7)->setRowHeight(30);

        $sheet->getColumnDimension('A')->setWidth(10);
        $sheet->getColumnDimension('B')->setWidth(30);
        $sheet->getColumnDimension('C')->setWidth(20);
        $sheet->getColumnDimension('D')->setWidth(50);
        $sheet->getColumnDimension('E')->setWidth(30);
        $sheet->getColumnDimension('F')->setWidth(30);
        $sheet->getColumnDimension('G')->setWidth(30);
        $sheet->getColumnDimension('H')->setWidth(30);
        $sheet->getColumnDimension('I')->setWidth(50);
        $sheet->getColumnDimension('J')->setWidth(50);
        $sheet->getColumnDimension('K')->setWidth(20);
        $sheet->getColumnDimension('L')->setWidth(50);
        $sheet->getColumnDimension('M')->setWidth(50);
        $sheet->getColumnDimension('N')->setWidth(50);
        $sheet->getColumnDimension('O')->setWidth(50);

        $row = 2;
        foreach ($contracts as $keys=>$contract){
            $i = 0;
            $sheet->setCellValue(ArrayHelper::getValue(self::getKeys(), $i++) . $row, ++$keys);
            $sheet->setCellValue(ArrayHelper::getValue(self::getKeys(),  $i++) . $row, $contract->created_at);
            $sheet->setCellValue(ArrayHelper::getValue(self::getKeys(),  $i++) . $row, $contract->type);
            $sheet->setCellValue(ArrayHelper::getValue(self::getKeys(),  $i++) . $row, $contract->predmet);
            $sheet->setCellValue(ArrayHelper::getValue(self::getKeys(),  $i++) . $row, $contract->startSum);
            $sheet->setCellValue(ArrayHelper::getValue(self::getKeys(),  $i++) . $row, $contract->productSum);
            $sheet->setCellValue(ArrayHelper::getValue(self::getKeys(),  $i++) . $row, $contract->price);
            $sheet->setCellValue(ArrayHelper::getValue(self::getKeys(),  $i++) . $row, $contract->customer ? $contract->customer->tin : "");
            $sheet->setCellValue(ArrayHelper::getValue(self::getKeys(),  $i++) . $row, $contract->customer ? $contract->customer->title : "");
            $sheet->setCellValue(ArrayHelper::getValue(self::getKeys(),  $i++) . $row,  $contract->customer ? ($contract->customer->region ? $contract->customer->region->title_uz : "") : "");
            $sheet->setCellValue(ArrayHelper::getValue(self::getKeys(),  $i++) . $row, $contract->producer ? $contract->producer->tin : "");
            $sheet->setCellValue(ArrayHelper::getValue(self::getKeys(),  $i++) . $row, $contract->producer ? $contract->producer->title : "");
            $sheet->setCellValue(ArrayHelper::getValue(self::getKeys(),  $i++) . $row,  $contract->producer ? ($contract->producer->region ? $contract->producer->region->title_uz : "") : "");
            $row++;
        }

        $fileName = 'report4.xlsx';
        $writer = new Xlsx($spreadsheet);

        $file_folder = Yii::getAlias('@backend/web') . '/contract';
        $file_path = $file_folder . '/' . $fileName;
        FileHelper::removeDirectory($file_folder);
        FileHelper::createDirectory($file_folder, $mode = 0775, $recursive = true);
        $writer->save($file_path);
        return $file_path;

    }
    public function getKeys()
    {
        return [
            'A',
            'B',
            'C',
            'D',
            'E',
            'F',
            'G',
            'H',
            'I',
            'J',
            'K',
            'L',
            'M',
            'N',
            'O',
            'P',
            'Q',
            'R',
            'S',
            'T',
            'U',
            'V',
            'W',
            'X',
            'Y',
            'Y',
            'AA',
            'AB',
            'AC',
            'AD',
        ];

    }


}