<?php


namespace api\modules\common\resources;


use common\models\CompanyBankAccount;
use common\models\query\NotDeletedFromCompanyQuery;

class CompanyBankAccountResource extends CompanyBankAccount
{
    public function fields()
    {
        return [
            'id',
            'account',
            'currency_code',
            'is_main',
            'mfo'
        ];
    }

    public function extraFields()
    {
        return [
            'bank'
        ];
    }

    public function getBank()
    {
        return $this->hasOne(BankShortResource::class, ['mfo' => 'mfo']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}