<?php

namespace api\modules\tender\forms;

use api\components\BaseRequest;
use api\modules\tender\resources\TenderQualificationSelectionResource;
use api\modules\tender\resources\TenderRequestRatingResource;
use api\modules\tender\resources\TenderRequestResource;
use api\modules\tender\resources\TenderRequirementsAnswerResource;
use api\modules\tender\resources\TenderRequirementsResource;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;
use common\models\TenderQualificationSelection;
use Yii;
use yii\helpers\ArrayHelper;

class TenderRequestCheckRatingEndForm extends BaseRequest
{

    public TenderResource $model;

    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }


    public function getResult()
    {

        if ($this->model->state != TenderEnum::STATE_READY_TO_RATING) {
            $this->addError("grades", t("Baholash bosqichida emas"));
            return false;
        }

        $commissionId = \Yii::$app->user->identity->commissionMemberId;
        $tenderCom = $this->model->getCommissionMember($commissionId);
        if ($tenderCom->role != TenderEnum::ROLE_SECRETARY) {
            $this->addError("grades", t("Sekretar roli uchun ruxsat mavjud"));
            return false;
        }

        $transaction = Yii::$app->db->beginTransaction();


        $check = TenderRequestResource::find()
            ->notDeleted()
            ->andWhere(['tender_id' => $this->model->id])
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->count();

        if ($check < 2) {
            $this->model->state = TenderEnum::STATE_NOT_REALIZED;
            if (!$this->model->save()) {

                $transaction->rollBack();

                $this->addErrors($this->model->errors);
                return [
                    'success' => false,
                    'tab' => -1
                ];
            }

            $res = $this->model->notRealized($transaction);
            if (!$res) {
                $this->addError("error", t("Kutilmagan xatolik"));
                return false;
            }
            $transaction->commit();

            return [
                "success" => true
            ];
        }

        /**
         * @var $qualification TenderQualificationSelection
         * Malaka tanlovlari
         */
        $qualification = TenderQualificationSelectionResource::find()
            ->notDeleted()
            ->andWhere(['tender_id' => $this->model->id])
            ->andWhere(['state' => TenderEnum::QUALIFIER_STATE_NEW])
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->one();
        if ($qualification) {
            $transaction->rollBack();
            return [
                "success" => false,
                "tab" => 1,
                "requestId" => $qualification->tender_request_id,
                'field' => 'qualificationSelection'
            ];
        }

        /**
         * @var $req TenderRequirementsAnswerResource
         * Baholanganini tekshiramiz
         */

        $expert = TenderRequirementsResource::find()->where(['tender_id' => $this->model->id, 'evaluation_method' => 'expert', 'status' => TenderEnum::STATUS_ACTIVE])->all();
        $reqId = ArrayHelper::getColumn($expert, 'id');

        $requirementAnswerArrIds = ArrayHelper::getColumn(TenderRequirementsAnswerResource::find()
            ->notDeleted()
            ->andWhere(['tender_id' => $this->model->id])
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_requirements_id' => $reqId])
            ->all(), 'id');

        $requirementRatingIds = ArrayHelper::getColumn(TenderRequestRatingResource::find()
            ->notDeleted()
            ->andWhere(['tender_id' => $this->model->id])
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->all(), 'tender_requirement_answer_id');

        $fArq = array_diff($requirementAnswerArrIds, $requirementRatingIds);
        if ($fArq && count($fArq) > 0) {
            $req = TenderRequirementsAnswerResource::find()
                ->notDeleted()
                ->andWhere(['in', 'id', $fArq])
                ->andWhere(['tender_id' => $this->model->id])
                ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
                ->one();
            $transaction->rollBack();
            return [
                'success' => false,
                'tab' => 3,
                'requestId' => $req->tender_request_id,
                'field' => 'requirements'
            ];
        }

        /**
         * @var $requestDisf TenderRequestResource
         * disflikatsiya yoki manfaatlar to'qnashyuvini baholashi
         * */

        $requestDisf = TenderRequestResource::find()
            ->notDeleted()
            ->andWhere(['tender_id' => $this->model->id])
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere('disclassification is null or conflict_interest is null')
            ->one();
        if ($requestDisf) {
            $transaction->rollBack();
            return [
                'success' => false,
                'tab' => 4,
                'requestId' => $requestDisf->id,
                'field' => $requestDisf->disclassification_text == null ? 'disclassification_text' : 'conflict_interest_text'
            ];
        }

        $this->model->state = TenderEnum::STATE_READY_TO_VOTE;
        if (!$this->model->save()) {
            $transaction->rollBack();
            $this->addErrors($this->model->errors);
            return [
                'success' => false,
                'tab' => -1
            ];
        }

        $transaction->commit();

        return [
            "success" => true
        ];

    }

}