<?php


namespace api\modules\common\resources;


use common\models\ClassifierCategory;
use common\models\query\NotDeletedFromCompanyQuery;

class ClassifierCategoryResource extends ClassifierCategory
{
    public function fields()
    {
        return [
            'id',
            'title_ru',
            'title_en',
            'title_uz',
            'title_uzk',
            'code',
        ];
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}