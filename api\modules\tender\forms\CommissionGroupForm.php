<?php

namespace api\modules\tender\forms;

use api\components\BaseRequest;
use api\modules\tender\resources\CommissionGroupResource;
use common\enums\StatusEnum;
use Yii;

class CommissionGroupForm extends BaseRequest {

    public $title;

    public CommissionGroupResource $model;

    public function __construct(CommissionGroupResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules (){
        return [
            ['title', 'required', 'message' => t('{attribute} yuborish majburiy')],
            ['title', 'safe']
        ];
    }
    public function attributeLabels()
    {
        return [
            'title' => Yii::t('main', 'Guruh nomi'),
        ];
    }

    public function getResult()
    {
        $this->model->title = $this->title;
        $this->model->company_id = Yii::$app->user->identity->company_id;
        $this->model->status = StatusEnum::STATUS_ACTIVE;

        return $this->model->save();
    }
}