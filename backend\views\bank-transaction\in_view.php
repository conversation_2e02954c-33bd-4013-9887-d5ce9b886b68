<?php

use yii\helpers\Html;
use yii\widgets\DetailView;

/**
 * @var yii\web\View $this
 * @var common\models\bank\BankTransactionIn $model
 */

$this->params['breadcrumbs'][] = ['label' => t("Bank transaction In"), 'url' => ['in']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="black-list-view">
    <div class="card">

        <div class="card-body">
            <?php echo DetailView::widget([
                'model' => $model,
                'attributes' => [
                    'id',
                    'bank_id',
                    [
                        'attribute' => 'amount',
                        'value' => function($model){
                            return number_format($model->amount / 100);
                        }
                    ],
                    'doc_number',
                    'type',
                    'mfo_payer',
                    'account_payer',
                    'name_payer',
                    'inn_payer',
                    'mfo_receiver',
                    'account_receiver',
                    'name_receiver',
                    'inn_receiver',
                    'purpose',
                    'status',
                    'transaction_state',
                    'payment_date',
                    'doc_date',
                    'created_at',
                ],
            ]) ?>
        </div>
    </div>
</div>
