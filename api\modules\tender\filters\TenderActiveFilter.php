<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderActiveResource;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;

class TenderActiveFilter extends BaseRequest
{
    public $lot;

    public function rules()
    {
        return [
            [['lot'], 'safe'],
        ];
    }

    public function getResult()
    {
        $model = TenderActiveResource::find()->notDeletedAndFromCompany();
        $model->andWhere('state=' . TenderEnum::STATE_READY);
        if ($this->lot) {
            $model->andWhere(['like', 'lot', $this->lot]);
        }
        $model->orderBy(['end_date' => SORT_DESC]);
        return paginate($model);


    }
}