<?php

namespace api\modules\common\controllers;

use api\components\ApiController;
use api\modules\common\filters\BankFilter;
use api\modules\common\filters\BhmFilter;
use api\modules\common\filters\EconomicActivitiesTypeFilter;
use api\modules\common\filters\OrganizationLegalFormFilter;
use common\models\Contract;
use kartik\mpdf\Pdf;
use yii\web\Controller;
use yii\web\NotFoundHttpException;

/**
 * Default controller for the `common` module
 */
class DefaultController extends ApiController
{
    /**
     * Renders the index view for the module
     * @return string
     */
    public function actionIndex()
    {
        return "common module ishlayapti";
    }

    public function actionMfoList()
    {
        return $this->sendResponse(
            new BankFilter(),
            \Yii::$app->request->queryParams
        );
    }

    public function actionEconomicActivitiesType()
    {
        return $this->sendResponse(
            new EconomicActivitiesTypeFilter(),
            \Yii::$app->request->queryParams
        );
    }

    public function actionOrganizationLegalForm()
    {
        return $this->sendResponse(
            new OrganizationLegalFormFilter(),
            \Yii::$app->request->queryParams
        );
    }

    /**
     * @var \common\models\Contract $model
     * */
    public function actionContractPdf($id, $l, $d)
    {

        $model = Contract::find()
            ->andWhere(['id' => $id])
            ->one();

        if (!$model) throw new NotFoundHttpException("Protokol shakllantirilmagan.");


        if (date('YmdHis', strtotime($model->created_at)) != $d) {
            throw new NotFoundHttpException("Qalbaki shakllantirilgan");
        }

        switch ($l) {
            case Contract::TYPE_AUCTION_PREFIX:
            {
                $auction = $model->auction;
                if ($auction == null || $l != $auction->lot) {
                    throw new NotFoundHttpException("Qalbaki shakllantirilgan");
                }
            }
            case Contract::TYPE_ESHOP_PREFIX :
            {
                $shop = $model->order;
                if ($shop == null || $l != $shop->lot_number) {
                    throw new NotFoundHttpException("Qalbaki shakllantirilgan");
                }
            }
            case Contract::TYPE_TENDER_PREFIX :
            {
                $tender = $model->tender;
                if ($tender == null || $l != $tender->lot) {
                    throw new NotFoundHttpException();
                }
            }
        }

        $file = $model->file;
        if ($file) {
            return $file->path;
        }


//        $path_to_email_template = '@api/modules/shop/files/auction_contract.php';
//        $content = \Yii::$app->view->renderFile($path_to_email_template, ['model' => $model]);
//
//        $pdf = new Pdf([
//            'mode' => Pdf::MODE_UTF8,
//            'format' => Pdf::FORMAT_A4,
//            'orientation' => Pdf::ORIENT_PORTRAIT,
//            'destination' => Pdf::DEST_BROWSER,
//            'content' => $content,
//            'cssFile' => '@api/modules/shop/files/css/pdf.css',
//        ]);
//
//        $pdf->filename = 'lot-' . $model->auction->lot . '-' . '.pdf';
//        return $pdf->render();
    }


    public function actionBhm()
    {
        ///commmon/default/bhm
        return $this->sendResponse(
            new BhmFilter(),
            \Yii::$app->request->queryParams
        );
    }

}
