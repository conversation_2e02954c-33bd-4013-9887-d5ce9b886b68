<?php

namespace api\modules\common\resources;

use common\models\PlanScheduleProperties;

class PlanSchedulePropertiesResource extends PlanScheduleProperties
{
    public function fields()
    {
        return [
            'prop_numb', 'val_numb', 'prop_name', 'val_name'
        ];
    }

    public function getClassifier()
    {
        return $this->hasOne(ClassifierResource::class, ['id' => 'classifier_id']);
    }

}