<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\client\resources\TenderDiscussionResource;
use api\modules\common\resources\VirtualTransactionResource;
use api\modules\tender\resources\TenderQualificationSelectionResource;
use api\modules\tender\resources\TenderRequestRatingCommissionResource;
use api\modules\tender\resources\TenderRequestRatingResource;
use api\modules\tender\resources\TenderResource;
use common\enums\BankServiceEnum;
use common\enums\CompanyTransactionEnum;
use common\enums\OperationTypeEnum;
use common\enums\TenderEnum;
use common\models\bank\BankTransactionOut;
use common\models\CompanyTransaction;
use common\models\TenderRequest;
use common\models\VirtualTransaction;
use Yii;
use yii\base\Exception;
use yii\web\ForbiddenHttpException;

class TenderUpdateChairmanMakeDiscussionProtocolForm extends BaseRequest
{

    public TenderResource $model;
    public $vote;

    public function rules()
    {
        return [
            ['vote', 'required', 'message' => t('{attribute} yuborish majburiy')],
            ['vote', 'integer'],
            ['vote', 'checkVote']
        ];
    }

    public function checkVote()
    {
        if ($this->vote == TenderEnum::VOTE_YES || $this->vote == TenderEnum::VOTE_NO) {
            return true;
        } else {
            $this->addError("vote", t("Ovoz berish qiymatlari 1 yoki 0 bo'lishi kerak"));
            return false;
        }
    }


    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    /**
     * @throws \yii\db\Exception
     * @throws \Throwable
     * @throws Exception
     */
    public function getResult()
    {
        date_default_timezone_set('Asia/Tashkent');
        if ($this->model->state != TenderEnum::STATE_DISCUSSION_END_MADE_PROTOCOL) {
            $this->addError("description", t("Tender Rais uchun muxokama protokoli shakllantirish holatida emas"));

            return false;
        }

        $commissionId = \Yii::$app->user->identity->commissionMemberId;
        $tenderCom = $this->model->getCommissionMember($commissionId);
        if ($tenderCom->role != TenderEnum::ROLE_CHAIRMAN) {
            $this->addError("description", t("Rais ro'li uchun ruxsat mavjud"));
            return false;
        }
        $transaction = Yii::$app->db->beginTransaction();

        if ($this->vote == TenderEnum::VOTE_YES) {

            $this->model->state = TenderEnum::STATE_MADE_DISCUSSION_PROTOCOL;
            if (!$this->model->save()) {

                $this->addErrors($this->model->errors);
                return false;
            }

            /**
             * Yutqazganlarni zaloglari qaytarildi
             * @var $win TenderRequest
             */
            $win = $this->model->tenderRequestWinner;
//            $date = date("Y-m-d H:i:s");
//
//            $company_transaction_zalog = CompanyTransaction::find()->where([
//                'tender_id' => $this->model->id,
//                'type' => CompanyTransactionEnum::TYPE_ZALOG,
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                'reverted_id' => null
//            ])->andWhere(['!=', 'company_id', $win->company_id])->all();
//
//            foreach ($company_transaction_zalog as $item) {
//
//                $revert = new CompanyTransaction([
//                    'company_id' => $item->company_id,
//                    'amount' => $item->amount,
//                    'tender_id' => $item->tender_id,
//                    'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'description' => \Yii::t("main", "Garov qaytarildi"),
//                    'transaction_date' => $date
//                ]);
//
//                if ($revert->save()) {
//                    $item->reverted_id = $revert->id;
//                    $item->save(false);
//                } else {
//                    $this->addErrors($revert->errors);
//                    $transaction->rollBack();
//                    return false;
//                }
//
//            }

            $company_transaction_zalog = VirtualTransactionResource::find()->where([
                'tender_id' => $this->model->id,
                "operation_type" => OperationTypeEnum::BLOCK_SALE_DEPOSIT,
                "parent_id" => null
            ])->andWhere(['>','credit',0])->all();
            foreach ($company_transaction_zalog as $item) {
                try {
                    /** @var VirtualTransactionResource  $item */
                    $revert = VirtualTransaction::saveTransaction(
                        $item->creditCompany,
                        $item->debitCompany,
                        $item->creditAccount->prefix_account,
                        $item->debitAccount->prefix_account,
                        $item->credit,
                        \Yii::t("main", "Garov qaytarildi"),
                        OperationTypeEnum::PRODUCT_NAME_TENDER,
                        $this->model->id,
                        null,
                        OperationTypeEnum::UNBLOCK_SALE_DEPOSIT,
                    );
                    $item->parent_id = $revert;
                    if (!$item->save())
                    {
                        $transaction->rollBack();
                        $this->addErrors($item->errors);
                        return false;
                    }
                } catch (Exception $exception) {
                    $transaction->rollBack();
                    $this->addError("error", $exception->getMessage());
                    return false;
                }
            }

            /**
             * komisiya xarajatga o'tkazildi
             */

//            $comissions = CompanyTransaction::find()
//                ->where(['tender_id' => $this->model->id, 'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION, 'reverted_id' => null])
//                ->andWhere(['in', 'company_id', [$this->model->company_id, $win->company_id]])->all();
//
//
//            foreach ($comissions as $company_transaction1) {
//                $revert = new CompanyTransaction([
//                    'company_id' => $company_transaction1->company_id,
//                    'tender_id' => $this->model->id,
//                    'amount' => $company_transaction1->amount,
//                    'type' => CompanyTransactionEnum::TYPE_REVERT_BLOCK_COMMISION,
//                    'description' => Yii::t("main", "Komissiya summasi bandlashdan chiqarildi"),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => $date,
//                ]);
//                if ($revert->save()) {
//                    $company_transaction1->reverted_id = $revert->id;
//                    $company_transaction1->save(false);
//                } else {
//                    $transaction->rollBack();
//                    $this->addErrors($revert->errors);
//                    return false;
//                }
//            }
//
//
//            foreach ($comissions as $company_transaction) {
//                $cc = new CompanyTransaction([
//                    'company_id' => $company_transaction->company_id,
//                    'tender_id' => $this->model->id,
//                    'amount' => $company_transaction->amount,
//                    'type' => CompanyTransactionEnum::TYPE_COMMISSION,
//                    'description' => Yii::t("main", "Komissiya xarajatga o'tkazildi"),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => $date,
//                ]);
//                if (!$cc->save()) {
//                    $transaction->rollBack();
//                    $this->addErrors($cc->errors);
//                    return false;
//                }
//
//                $transactionOut = new BankTransactionOut();
//                $transactionOut->amount = (int)$company_transaction->amount;
//                $transactionOut->purpose = $cc->tender_id . " terder bo'yicha komisiya xarajatga o'tkazildi";
//                $transactionOut->type = BankServiceEnum::BANK_TRANSACTION_TYPE_COMMISSION;
//                $transactionOut->company_transaction_id = $cc->id;
//                $transactionOut->service_id = (string)BankServiceEnum::SERVICE_ID;
//                $transactionOut->company_id = $company_transaction->company_id;
//                $transactionOut->state = BankServiceEnum::BANK_TRANSACTION_TYPE_STATE_NEW;
//
//                if (!$transactionOut->save()) {
//                    $transaction->rollBack();
//                    $this->addErrors($transactionOut->errors);
//                    return false;
//                }
//            }

            // Tender producer va customer commission blokirovkadan yechildi.
            $commissions = VirtualTransactionResource::find()
                ->where([
                    "tender_id" => $this->model->id,
                    "operation_type" => OperationTypeEnum::BLOCK_SALE_COMMISSION,
                    "parent_id" => null,
                ])->andWhere(['>', 'credit', 0])
                ->andWhere(['in','credit_company_id',[$this->model->company_id, $win->company_id]])->all();
            foreach ($commissions as $commission) {
                try {
                    /** @var VirtualTransactionResource  $commission */
                    $revertID = VirtualTransaction::saveTransaction(
                        $commission->creditCompany,
                        $commission->debitCompany,
                        $commission->creditAccount->prefix_account,
                        $commission->debitAccount->prefix_account,
                        $commission->credit,
                        Yii::t("main", "Komissiya summasi bandlashdan chiqarildi"),
                        OperationTypeEnum::PRODUCT_NAME_TENDER,
                        $this->model->id,
                        null,
                        OperationTypeEnum::UNBLOCK_SALE_COMMISSION,
                    );
                    $commission->parent_id = $revertID;
                    if (!$commission->save()) {
                        $transaction->rollBack();
                        $this->addErrors($commission->errors);
                        return false;
                    }
                } catch (Exception $ex) {
                    $transaction->rollBack();
                    $this->addError('error', $ex->getMessage());
                    return false;
                }
            }

            // TODO bitim bo'yicha kommissiya vositachilik yig'imiga blokirovka qilindi.
            foreach ($commissions as $commission2) {
                try {
                    /** @var VirtualTransactionResource  $commission2 */
                   $_company = $commission2->creditCompany;
                   VirtualTransaction::saveTransaction(
                        $_company,
                        $_company,
                        $_company->isBudget() ? OperationTypeEnum::P_B_31101 : OperationTypeEnum::P_K_30101,
                        $_company->isBudget() ? OperationTypeEnum::P_B_31501 : OperationTypeEnum::P_K_30501,
                        $commission2->credit,
                        Yii::t("main", "Bitim uchun komisiya summasi blokirovka qilindi."),
                        OperationTypeEnum::PRODUCT_NAME_TENDER,
                        $this->model->id,
                        null,
                        OperationTypeEnum::HOLD_TRANSACTION_COMMISSION,
                    );
                } catch (Exception $ex) {
                    $transaction->rollBack();
                    $this->addError('error', $ex->getMessage());
                    return false;
                }
            }

            // TODO bitim bo'yicha kommissiya vositachilik yig'imi daromadga olindi.
            $_commissions = VirtualTransactionResource::find()
                ->where([
                    "tender_id" => $this->model->id,
                    "operation_type" => OperationTypeEnum::HOLD_TRANSACTION_COMMISSION,
                    "parent_id" => null,
                ])->andWhere(['>', 'credit', 0])->all();
            foreach ($_commissions as $commission3) {
                try {
                    /** @var VirtualTransactionResource  $commission3 */
                    $_company = $commission3->creditCompany;
                    $revertID = VirtualTransaction::saveTransaction(
                        $_company,
                        _company(),
                        $_company->isBudget() ? OperationTypeEnum::P_B_31501 : OperationTypeEnum::P_K_30501,
                        $_company->isBudget() ? OperationTypeEnum::A_50113 : OperationTypeEnum::A_50111,
                        $commission3->credit,
                        Yii::t("main", "Bitim bo’yicha vositachilik yigʻimini toʻlash."),
                        OperationTypeEnum::PRODUCT_NAME_TENDER,
                        $this->model->id,
                        null,
                        OperationTypeEnum::PAY_TRANSACTION_COMMISSION,
                    );
                    $commission3->parent_id = $revertID;
                    if (!$commission3->save()) {
                        $transaction->rollBack();
                        $this->addErrors($commission3->errors);
                        return false;
                    }

                    $transactionOut = new BankTransactionOut();
                    $transactionOut->amount = (int)$commission3->credit;
                    $transactionOut->purpose = $commission3->tender_id . " terder bo'yicha komisiya xarajatga o'tkazildi";
                    $transactionOut->type = BankServiceEnum::BANK_TRANSACTION_TYPE_COMMISSION;
                    $transactionOut->company_transaction_id = $revertID;
                    $transactionOut->service_id = (string)BankServiceEnum::SERVICE_ID;
                    $transactionOut->company_id = $commission3->credit_company_id;
                    $transactionOut->state = BankServiceEnum::BANK_TRANSACTION_TYPE_STATE_NEW;

                    if (!$transactionOut->save()) {
                        $transaction->rollBack();
                        $this->addErrors($transactionOut->errors);
                        return false;
                    }
                } catch (Exception $ex) {
                    $transaction->rollBack();
                    $this->addError('error', $ex->getMessage());
                    return false;
                }
            }

            /**
             * komisiya o'ziga qaytarildi
             */

//            $commissionLoser = CompanyTransaction::find()
//                ->where(['tender_id' => $this->model->id, 'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION, 'reverted_id' => null])
//                ->andWhere(['not in', 'company_id', [$this->model->company_id, $win->company_id]])->all();
//
//            foreach ($commissionLoser as $company_transaction1) {
//                $revert = new CompanyTransaction([
//                    'company_id' => $company_transaction1->company_id,
//                    'tender_id' => $this->model->id,
//                    'amount' => $company_transaction1->amount,
//                    'type' => CompanyTransactionEnum::TYPE_REVERT_BLOCK_COMMISION,
//                    'description' => Yii::t("main", "Komissiya summasi bandlashdan chiqarildi"),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => $date,
//                ]);
//                if ($revert->save()) {
//                    $company_transaction1->reverted_id = $revert->id;
//                    $company_transaction1->save(false);
//                } else {
//                    $transaction->rollBack();
//                    $this->addErrors($revert->errors);
//                    return false;
//                }
//            }
            $commissions = VirtualTransactionResource::find()
                ->where([
                    "tender_id" => $this->model->id,
                    "operation_type" => OperationTypeEnum::BLOCK_SALE_COMMISSION,
                    "parent_id" => null,
                ])->andWhere(['>', 'credit', 0])->all();
            foreach ($commissions as $company_transaction1) {
                /** @var $company_transaction1 VirtualTransaction */
                try {
                    $revertID = VirtualTransaction::saveTransaction(
                        $company_transaction1->creditCompany,
                        $company_transaction1->debitCompany,
                        $company_transaction1->creditAccount->prefix_account,
                        $company_transaction1->debitAccount->prefix_account,
                        $company_transaction1->credit,
                        Yii::t("main", "Komissiya summasi bandlashdan chiqarildi"),
                        OperationTypeEnum::PRODUCT_NAME_TENDER,
                        $this->model->id,
                        null,
                        OperationTypeEnum::UNBLOCK_SALE_COMMISSION,
                    );
                    $company_transaction1->parent_id = $revertID;
                    if (!$company_transaction1->save()) {
                        $transaction->rollBack();
                        $this->addErrors($company_transaction1->errors);
                        return false;
                    }
                } catch (Exception $ex) {
                    $transaction->rollBack();
                    $this->addError("error", $ex->getMessage());
                    return false;
                }
            }

        } else {
            $dis = TenderDiscussionResource::find()->where(['tender_id' => $this->model->id])->count();
            if ($dis != 0) {
                $transaction->rollBack();
                $this->addError('error', t("Qayta baholashga yuborish uchun, e'tirozlar mavjud emas"));
                return false;

            }

            $this->model->state = TenderEnum::STATE_READY_TO_RATING;
            if ($this->model->save()) {
                $userId = Yii::$app->user->id;

                TenderDiscussionResource::updateAll([
                    'status' => TenderEnum::STATUS_NOT_ACTIVE,
                    'updated_at' => date("Y-m-d H:i:s"),
                    'updated_by' => \Yii::$app->user->id
                ], ['tender_id' => $this->model->id, 'status' => TenderEnum::STATUS_ACTIVE]
                );

                TenderRequestRatingCommissionResource::updateAll([
                    'status' => TenderEnum::STATUS_NOT_ACTIVE,
                    'updated_by' => $userId,
                    'updated_at' => date("Y-m-d H:i:s")
                ], [
                    'tender_id' => $this->model->id,
                    'status' => TenderEnum::STATUS_ACTIVE
                ]);
                TenderQualificationSelectionResource::updateAll([
                    'state' => TenderEnum::QUALIFIER_STATE_NEW,
                    'updated_by' => $userId,
                    'updated_at' => date("Y-m-d H:i:s")
                ], [
                    'tender_id' => $this->model->id,
                    'status' => TenderEnum::STATUS_ACTIVE
                ]);

                TenderRequestRatingResource::updateAll([
                    'status' => TenderEnum::STATUS_NOT_ACTIVE,
                    'updated_by' => $userId,
                    'updated_at' => date("Y-m-d H:i:s")
                ], [
                    'tender_id' => $this->model->id,
                    'status' => TenderEnum::STATUS_ACTIVE
                ]);
            } else {
                $transaction->rollBack();

                $this->addErrors($this->model->errors);
                return false;
            }
        }
        $transaction->commit();
        return true;

    }
}