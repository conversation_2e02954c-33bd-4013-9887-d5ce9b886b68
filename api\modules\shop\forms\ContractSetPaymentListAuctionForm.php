<?php


namespace api\modules\shop\forms;


use api\components\BaseModel;
use api\components\BaseRequest;
use api\modules\shop\resources\ContractResource;
use common\enums\CompanyEnum;
use common\enums\CompanyTransactionEnum;
use common\enums\ContractEnum;
use common\models\Company;
use common\models\CompanyTransaction;
use Yii;

class ContractSetPaymentListAuctionForm extends BaseModel
{

    public ContractResource $model;
    public $pkcs7;


    public function __construct(ContractResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        $parent = parent::rules();
        $child =  [

        ];
        return  array_merge($parent,$child);
    }




    public function getResult()
    {

        $transaction = \Yii::$app->db->beginTransaction();
        $company_id = \Yii::$app->user->identity->company_id;
        if ($this->model->customer_id != $company_id) {
            $this->addError("error", t("Shartnoma sizga tegishli emas"));
            return false;
        }
        if ($this->model->status != ContractEnum::STATUS_SIGNED) {
            $this->addError("error", t("Shartnoma imzolangan xolatda emas, to'lov qilib bo'lmaydi"));
            return false;
        }

        $this->model->status = ContractEnum::STATUS_PAYMENT_END;
        $this->model->customer_pay_date = date("Y-m-d H:i:s");
        if ($this->model->save()) {
            $pay = new CompanyTransaction([
                'company_id' => $company_id,
                'contract_id' => $this->model->id,
                'auction_id' => $this->model->auction_id,
                'amount' => $this->model->price,
                'type' => CompanyTransactionEnum::TYPE_BLOCK_PAYMENT_FOR_CONTRACT,
                'description' => Yii::t("main", "Shartnoma uchun to'lov qildi"),
                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                'transaction_date' => date("Y-m-d H:i:s"),
            ]);
            if (!$pay->save()) {
                $this->addError("pay", $pay->errors);
                $transaction->rollBack();
                return false;
            }
            $transaction->commit();
            return true;
        }

        $this->addErrors($this->model->errors);
        return false;
    }
}