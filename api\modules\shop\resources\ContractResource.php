<?php

namespace api\modules\shop\resources;

use api\modules\common\resources\CompanyShortResource;
use api\modules\common\resources\CompanyTransactionResource;
use api\modules\common\resources\FileResource;
use common\enums\CompanyTransactionEnum;
use common\enums\ContractEnum;
use common\models\Company;
use common\models\Contract;
use common\models\ContractRequest;
use common\models\shop\Cart;
use common\models\shop\Order;
use yii\helpers\ArrayHelper;

class ContractResource extends Contract
{
    public function fields()
    {
        return [
            'customer_id',
            'producer_id',
            'order_id',
            'created_at',
            'price' => function ($model) {
                return $model->price / 100;
            },
            'need_price' => function ($model) {
                return $model->price / 100;
            },
            'status',
            'customer_signed',
            'producer_signed',
            'producer_sign_date',
            'customer_sign_date',
            'customer_pay_date',
            'customer_mark_delivered_date',
            'customer_mark_delivered_date',
            'customer_cancel_date',
            'producer_cancel_date',
            'stateName',
            'facture_file_id',
            'id',
            'number',
            'reserve',
            'payment_last_date'
        ];
    }

    public function extraFields()
    {
        return [
            'order',
            'auction',
            'producer',
            'customer',
            'contractCancelRequest',
            'transaction',
            'factureFile',
            'file',
            'checkReserve'
        ];
    }

    public function getContractCancelRequest()
    {
        return $this->hasMany(ContractCancelRequestResource::class, ['contract_id' => 'id'])->andWhere(['status' => ContractEnum::STATUS_REQUEST_NEW])->orderBy(['id' => SORT_DESC]);
    }


    public function getAuction()
    {
        return $this->hasOne(AuctionMyLotsResource::class, ['id' => 'auction_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getOrder()
    {
        return $this->hasOne(OrderResource::class, ['id' => 'order_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'file_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCustomer()
    {
        return $this->hasOne(CompanyShortResource::class, ['id' => 'customer_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProducer()
    {
        return $this->hasOne(CompanyShortResource::class, ['id' => 'producer_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getTransaction()
    {
        return $this->hasOne(CompanyTransactionResource::class, ['product_id' => 'order_id'])->andWhere([CompanyTransactionResource::tableName() . '.type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION]);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFactureFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'facture_file_id']);
    }

    public function getCheckReserve()
    {
        $request = ContractRequest::findOne(['contract_id' => $this->id]);
        return $request !== null ? $request->status : null;
    }
}
