<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\UnitResource;
use common\models\News;

class SimilarNewsFilter extends BaseRequest
{
    public $title;
    public $id;

    public function rules()
    {
        return [
            ['title', 'safe'],
            ['id', 'safe'],
        ];
    }

    public function getResult()
    {
        $query = News::find()->andWhere(['status'=>1])->andWhere(['not in','id',$this->id]);

        if ($this->title) {
            $query->where(['like', 'title', $this->title]);
        }

        return $query->all();
    }
}
