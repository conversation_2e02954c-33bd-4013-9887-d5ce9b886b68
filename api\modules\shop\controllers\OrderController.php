<?php

namespace api\modules\shop\controllers;

use api\components\ApiController;
use api\modules\shop\filters\CancelOrderListFilter;
use api\modules\shop\filters\NoOfferListFilter;
use api\modules\shop\filters\OrderFinishedFilter;
use api\modules\shop\filters\OrderListFilter;
use api\modules\shop\filters\OrderRejectFilter;
use api\modules\shop\filters\OrderWaitingFilter;
use api\modules\shop\forms\AcceptDmbatForm;
use api\modules\shop\forms\OrderForm;
use api\modules\shop\forms\OrderUpdateForm;
use api\modules\shop\resources\OrderResource;
use api\modules\shop\resources\ProductResource;
use common\behaviors\RoleAccessBehavior;
use common\enums\PkcsEnum;
use Yii;

/**
 * Default controller for the `shop` module
 */
class OrderController extends ApiController
{
    public function behaviors()
    {
        $parent = parent::behaviors();
        $parent['accessByRole'] = [
            'class' => RoleAccessBehavior::class,
            'rules' => [
                'index' => ['user'],
                'create' => ['user'],
                'update' => ['user'],
                'finished' => ['user'],
                'waiting' => ['user'],
                'reject' => ['user'],
                'no-offer' => ['user'],
                'cancel-order' => ['user'],
            ],
        ];
        return $parent;
    }

    public function actionIndex()
    {
        return $this->sendResponse(
            new OrderListFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionCreate()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPks7 = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new OrderForm(new OrderResource()),
            $decodedPks7,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_ORDER_CREATE
        );
    }
    public function actionUpdate($id)
    {
        return $this->sendResponse(
            new OrderUpdateForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }
    public function actionFinished()
    {
        return $this->sendResponse(
            new OrderFinishedFilter(),
            Yii::$app->request->bodyParams
        );
    }
    public function actionWaiting()
    {
        return $this->sendResponse(
            new OrderWaitingFilter(),
            Yii::$app->request->queryParams
        );
    }
    public function actionReject()
    {
        return $this->sendResponse(
            new OrderRejectFilter(),
            Yii::$app->request->queryParams
        );
    }
    public function actionNoOffer()
    {
        return $this->sendResponse(
            new NoOfferListFilter(),
            Yii::$app->request->queryParams
        );
    }
    public function actionCancelOrder()
    {
        return $this->sendResponse(
            new CancelOrderListFilter(),
            Yii::$app->request->queryParams
        );
    }
    private function findOne($id)
    {
        $model = OrderResource::findOne($id);

        if ($model instanceof OrderResource){
            return $model;
        }
        throw new \Exception("OrderResource not found");
    }

}
