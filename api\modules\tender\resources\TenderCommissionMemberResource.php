<?php


namespace api\modules\tender\resources;


use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderCommissionMember;

class TenderCommissionMemberResource extends TenderCommissionMember
{
    public function fields()
    {
        return [
            'role', 'status', 'commissionGroupMember', 'commissionMember', 'tender'
        ];
    }



    public function getCommissionGroupMember()
    {
        return $this->hasOne(CommissionGroupMemberResource::class, ['id' => 'commission_group_member_id']);
    }

    /**
     * Gets query for [[CommissionMember]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCommissionMember()
    {
        return $this->hasOne(CommissionMemberShortResource::class, ['id' => 'commission_member_id']);
    }

    /**
     * Gets query for [[Company]].
     *
     * @return \yii\db\ActiveQuery
     */
//    public function getCompany()
//    {
//        return $this->hasOne(Company::class, ['id' => 'company_id']);
//    }

    public function getTender()
    {
        return $this->hasOne(TenderShortResource::class, ['id' => 'tender_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

}