<?php
//
//
//namespace api\modules\shop\forms;
//
//
//use api\components\BaseRequest;
//use api\modules\common\resources\FileResource;
//use api\modules\shop\resources\CartResource;
//use api\modules\shop\resources\ContractResource;
//use api\modules\shop\resources\OrderRequestResource;
//use api\modules\shop\resources\OrderResource;
//use api\modules\shop\resources\ProductResource;
//use common\enums\CompanyTransactionEnum;
//use common\enums\ContractEnum;
//use common\enums\ShopEnum;
//use common\enums\StatusEnum;
//use common\models\Company;
//use common\models\CompanyTransaction;
//use common\models\Contract;
//use common\models\shop\Order;
//use common\models\shop\OrderRequest;
//use common\models\shop\Product;
//use kartik\mpdf\Pdf;
//use Yii;
//use yii\web\UploadedFile;
//
//class ContractForm extends BaseRequest
//{
//
//    public ContractResource $model;
//
//    public $customer_id;
//    public $producer_id;
//    public $order_id;
//    public $price;
//
//    public function __construct(ContractResource $model, $params = [])
//    {
//        $this->model = $model;
//
//        parent::__construct($params);
//    }
//
//    public function rules()
//    {
//        return [
//            [['customer_id', 'producer_id', 'order_id', 'price'], 'required', 'message' => t('{attribute} yuborish majburiy')],
//            [['order_id'], 'exist', 'skipOnError' => true, 'targetClass' => OrderResource::class, 'targetAttribute' => ['order_id' => 'id']],
//            [['customer_id'], 'exist', 'skipOnError' => true, 'targetClass' => Company::class, 'targetAttribute' => ['customer_id' => 'id']],
//            [['producer_id'], 'exist', 'skipOnError' => true, 'targetClass' => Company::class, 'targetAttribute' => ['producer_id' => 'id']],
//        ];
//    }
//
//
//    public function getResult()
//    {
//        $transaction = \Yii::$app->db->beginTransaction();
//
//        $order = OrderResource::findOne($this->order_id);
//
//        $order->updateAttributes([
//            'shop_end' => date("Y-m-d H:i:s"),
//            'status' => ShopEnum::ORDER_STATUS_INACTIVE
//        ]);
//        $customer_id = $order->user->company_id;
//        $producer_id = $order->company_id;
//
//        $winner_price = $order->getAllOrderRequests() ? $order->getAllOrderRequests()->min("price") : 0;
//
//        if ($winner_price > 0) {
//            $winner = OrderRequest::find()->where(['order_id' => $order->id, 'price' => $winner_price])->orderBy('created_at asc')->one();
//
//            if ($winner) {
//                $winner->is_winner = 1;
//                $winner->save();
//                $producer_id = $winner->company_id;
//            }
//        } else {
//            $winner_price = $order->total_sum + $order->shipping_sum;
//        }
//
//        //TODO zaloglarni qaytarish
//        $zalogs = CompanyTransaction::find()->where(['order_id' => $order->id, 'type' => CompanyTransactionEnum::TYPE_ZALOG])->andWhere(['not in', 'company_id', [$customer_id, $producer_id]])->all();
//
//        foreach ($zalogs as $company_transaction) {
//            $revert = new CompanyTransaction([
//                'company_id' => $company_transaction->company_id,
//                'contract_id' => null,
//                'order_id' => $company_transaction->order_id,
//                'amount' => $company_transaction->amount,
//                'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
//                'description' => Yii::t("main", "Savdo bo'lmagani uchun zalog summa blokdan chiqarildi"),
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                'transaction_date' => date("Y-m-d H:i:s"),
//            ]);
//
//            if ($revert->save()) {
//                $company_transaction->reverted_id = $revert->id;
//                $company_transaction->save(false);
//            }
//        }
//
//        //TODO kommisiyalarni qaytarish
//        $block_kommisions_from_losers = CompanyTransaction::find()->where(['order_id' => $order->id, 'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION])->andWhere(['not in', 'company_id', [$customer_id, $producer_id]])->all();
//
//        foreach ($block_kommisions_from_losers as $company_transaction) {
//
//            $revert = new CompanyTransaction([
//                'company_id' => $company_transaction->company_id,
//                'contract_id' => null,
//                'order_id' => $company_transaction->order_id,
//                'amount' => $company_transaction->amount,
//                'type' => CompanyTransactionEnum::TYPE_REVERT_BLOCK_COMMISION,
//                'description' => Yii::t("main", "Savdo bo'lmagani uchun komissiya summasi blokdan chiqarildi"),
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                'transaction_date' => date("Y-m-d H:i:s"),
//            ]);
//
//
//            if ($revert->save()) {
//                $company_transaction->reverted_id = $revert->id;
//                $company_transaction->save(false);
//            }
//        }
//
//        $contract = new Contract([
//            'customer_id' => $customer_id,
//            'producer_id' => $producer_id,
//            'order_id' => $order->id,
//            'price' => $winner_price,
//            'customer_signed' => 1,
//            'customer_sign_date' => date("Y-m-d H:i:s"),
//            'producer_signed' => 1,
//            'producer_sign_date' => date("Y-m-d H:i:s"),
//            'customer_mark_delivered_date' => $order->product->deliveryDate,
//            'status' => ContractEnum::STATUS_SIGNED
//        ]);
//
//        if (!$contract->save()) {
//            $transaction->rollBack();
//            $this->addErrors($contract->errors);
//            return false;
//        }
//
//        //TODO generate pdf file
//        $path_to_email_template = '@api/modules/shop/files/shop_contract.php';
//        $content = Yii::$app->view->renderFile($path_to_email_template, ['model' => $contract]);
//
//        $pdf = new Pdf(['mode' => Pdf::MODE_UTF8,
//            'format' => Pdf::FORMAT_A4,
//            'orientation' => Pdf::ORIENT_PORTRAIT,
//            'destination' => Pdf::DEST_BROWSER,
//            'content' => $content,
//            'cssFile' => '@api/modules/shop/files/css/pdf.css',
//        ]);
//
//        $fayl_filename = '/source/' . str_replace('.pdf', '', 'contract_file') . '_' . (int) microtime(true) . '.pdf';
//
//        $pdf->output($content, \Yii::getAlias('@storage').'/web' . $fayl_filename, 'F');
//
//        $file = new FileResource();
//
//        $file->path = $fayl_filename;
//        $file->title = 'contract_file';
//        $file->size = 1234;
//        $file->type = 'application/pdf';
//        $file->status = StatusEnum::STATUS_ACTIVE;
//
//        if (!$file->save()) {
//            unlink($fayl_filename);
//        }
//        $contract->updateAttributes(['file_id'=>$file->id]);
//
//        $transaction->commit();
//        return true;
//
//    }
//}