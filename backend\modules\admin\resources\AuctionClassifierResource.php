<?php


namespace backend\modules\admin\resources;


use common\models\auction\AuctionClassifier;

class AuctionClassifierResource extends AuctionClassifier
{
    public function fields()
    {
        return [
            'id',
            'auction_id',
            'classifier_id',
            'quantity',
            'price' => function ($model) {
                return $model->price / 100;
            },
            'total_sum' => function ($model) {
                return $model->total_sum / 100;
            },
            'description',
            'classifier',
            'status',
        ];
    }

    public function extraFields()
    {
        return [
            'classifier',
            'auction',
        ];
    }
}