<?php


namespace api\modules\backend\resources;


use common\models\TenderRequirements;

class TenderRequirementsResource extends TenderRequirements
{

    public function rules()
    {
        return [
            [[ 'value', 'value_type', 'necessary',
                'max_ball', 'min_ball', 'comparative_weight'], 'integer'],
            [['title', 'description'], 'string', 'max' => 255],
        ];
    }
    public function extraFields()
    {
        return ['tender'];
    }

    public function getTender()
    {
        return $this->hasOne(TenderResource::class, ['id' => 'tender_id']);
    }

}