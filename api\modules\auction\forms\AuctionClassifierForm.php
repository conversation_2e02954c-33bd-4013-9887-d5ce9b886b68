<?php

namespace api\modules\auction\forms;

use api\components\BaseRequest;
use api\modules\auction\resources\AuctionClassifierResource;

class AuctionClassifierForm extends BaseRequest
{
  public AuctionClassifierResource $model;

  public $auction_id;
  public $classifier_id;
  public $quantity;
  public $unit_id;
  public $price;
  public $total_sum;
  public $description;

  public function __construct($model = null, $params = [])
  {
    $this->model = $model ?? new AuctionClassifierResource();

    parent::__construct($params);
  }

  /**
   * {@inheritdoc}
   */
  public function rules()
  {
    return [
      [[
        'auction_id',
        'classifier_id',
        'quantity',
        'unit_id',
        'price',
        'total_sum',
        'description',
      ], 'safe'],
        [[
            'auction_id',
            'classifier_id',
            'quantity',
            'unit_id',
            'price',
            'total_sum',
            'description',
        ], 'required', 'message' => t("{attribute} yuborish majburiy")
        ]
    ];
  }

  public function getResult()
  {
    $this->model->auction_id = $this->auction_id;
    $this->model->classifier_id = $this->classifier_id;
    $this->model->quantity = $this->quantity;
    $this->model->unit_id = $this->unit_id;
    $this->model->price = $this->price * 100;
    $this->model->total_sum = $this->total_sum * 100;
    $this->model->description = $this->description;

    return $this->model->save();
  }
}
