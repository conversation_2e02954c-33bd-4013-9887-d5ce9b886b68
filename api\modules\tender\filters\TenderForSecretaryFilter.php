<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderResourceForCommission;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;

class TenderForSecretaryFilter extends BaseRequest
{

    public $lot;

    public function rules()
    {
        return [
            [['lot'], 'safe'],
        ];
    }

    public function getResult()
    {
        $commissionId = \Yii::$app->user->identity->commissionMemberId;

        $model = TenderResourceForCommission::find()
            ->join('inner join', 'tender_commission_member', 'tender.id = tender_commission_member.tender_id')
            ->notDeleted()
            ->andWhere(['in', 'state', [TenderEnum::STATE_READY_TO_RATING, TenderEnum::STATE_READY_TO_VOTE, TenderEnum::STATE_READY_TO_MAKE_PROTOCOL]])
            ->andWhere([
                'tender_commission_member.commission_member_id' => $commissionId,
                'tender_commission_member.status' => TenderEnum::STATUS_ACTIVE,
                'tender_commission_member.role' => TenderEnum::ROLE_SECRETARY
            ]);
        if ($this->lot) {
            $model->andWhere(['like', 'tender.lot', $this->lot]);
        }
        $model->orderBy(['end_date' => SORT_ASC]);

        return paginate($model);

    }
}