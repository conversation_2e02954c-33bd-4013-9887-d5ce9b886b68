<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\shop\resources\OrderRequestResource;
use api\modules\shop\resources\OrderResource;
use common\enums\ShopEnum;
use Yii;

class OrderWaitingFilter extends BaseRequest
{
    public $pageNo = 0;
    public $pageSize = 10;
    public function rules()
    {
        return [
            [['pageNo', 'pageSize'], 'integer'],
        ];
    }

    public function getResult()
    {
        $model = OrderResource::find()
            ->andWhere([OrderResource::tableName().'.status'=>ShopEnum::ORDER_STATUS_WAITING])
            ->andWhere(['order.user_id' => Yii::$app->user->id]);
        $model->orderBy('order.id desc');
        return paginate($model);
    }
}
