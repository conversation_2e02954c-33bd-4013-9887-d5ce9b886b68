<?php

namespace backend\controllers;

use api\modules\backend\resources\TenderModeratorLogResource;
use common\enums\TenderEnum;
use common\models\Contract;
use common\models\WorkdayCalendar;
use Yii;
use common\models\Tender;
use common\models\TenderSearch;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * TenderController implements the CRUD actions for Tender model.
 */
class TenderController extends BackendController
{
    public function actionChart()
    {
        $year = Yii::$app->request->get('year') ? Yii::$app->request->get('year') : date("Y");
        $month = Yii::$app->request->get('month') ? Yii::$app->request->get('month') : date("m");

        $contract = Contract::find()->select([
            'DATE(created_at) as created_at',
            'SUM(price) as price'
        ])->andWhere(['is not', 'tender_id', null]);
        if ($year) {
            $contract->andWhere(['extract(year from created_at)' => $year]);
        }
        if ($month) {
            $contract->andWhere(['extract(month from created_at)' => $month]);
        }
        $data = $contract->groupBy(['DATE(created_at)'])->all();

        $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);

        $startDate = $year . '-' . $month . '-' . '01';
        $endDate = $year . '-' . $month . '-' . $daysInMonth;
        $allDates = [];
        $currentDate = strtotime($startDate);
        $endDateTimestamp = strtotime($endDate);

        while ($currentDate <= $endDateTimestamp) {
            $allDates[date('Y-m-d', $currentDate)] = 0; // 0 qiymat bilan boshlaymiz
            $currentDate = strtotime('+1 day', $currentDate);
        }

        foreach ($data as $d) {
            $allDates[$d->created_at] = $d->price;
        }

        return $this->render('chart', [
            'year' => $year,
            'month' => $month,
            'data' => $allDates,
        ]);
    }

    /**
     * Lists all Tender models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new TenderSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionModerator($status = null)
    {
        $searchModel = new TenderSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, $status);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionModeratorRejected($status)
    {
        $searchModel = new TenderSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, $status);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Tender model.
     * @param int $id ID
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    public function actionModeratorAccept()
    {
        $model = $this->findModel(Yii::$app->request->post()['id']);
        $log = new TenderModeratorLogResource();
        if ($model->state == TenderEnum::STATE_NEW) {
            $log->tender_id = $model->id;
            $log->description = "Moderator tasdiqladi";
            $log->state = TenderEnum::STATE_ACCEPT_MODERATOR;
            $log->moderator_pinfl = \Yii::$app->user->identity->username;
            if ($log->save()) {
                $model->state = TenderEnum::STATE_ACCEPT_MODERATOR;
                $model->updated_at = date("Y-m-d H:i:s");
                if ($model->save()) {
                    \Yii::$app->session->setFlash("success", "Moderatsiyadan muvaffaqiyatli o'tdi");
                    return $this->redirect(['index'
                    ]);
                } else {
                    \Yii::$app->session->setFlash("danger", "Moderatsiyadan o'tmadi" . json_encode($model->errors));
                    return $this->render('view', [
                        'model' => $model,
                    ]);
                }
            }
            \Yii::$app->session->setFlash("danger", "Moderator log " . json_encode($log->errors));
            return $this->render('view', [
                'model' => $model,
            ]);
        } else {
            \Yii::$app->session->setFlash("danger", "Moderatsiya holatida emas");
            return $this->render('view', [
                'model' => $model,
            ]);
        }
    }

    public function actionModeratorReject()
    {
        $model = $this->findModel(Yii::$app->request->post()['id']);
        $log = new TenderModeratorLogResource();
        if ($model->status == TenderEnum::STATUS_NEW && $model->state == TenderEnum::STATE_NEW) {
            $reason = Yii::$app->request->post()['select'];
            if (isset(Yii::$app->request->post()['description']) && Yii::$app->request->post()['description'] != null) {
                $reason .= "<br>" . Yii::$app->request->post()['description'];
            }
            $log->tender_id = $model->id;
            $log->state = TenderEnum::STATE_REJECT_MODERATOR;
            $log->moderator_pinfl = \Yii::$app->user->identity->username;
            $log->description = $reason;
            if ($log->save()) {
                $model->state = TenderEnum::STATE_REJECT_MODERATOR;
                $model->cancel_reason = $reason;
                if ($model->save()) {
                    \Yii::$app->session->setFlash("success", "Moderatsiyadan rad etildi");
                    return $this->render('view', [
                        'model' => $model,
                    ]);
                } else {
                    \Yii::$app->session->setFlash("danger", "Moderatsiyadan o'tmadi" . json_encode($model->errors));
                    return $this->render('view', [
                        'model' => $model,
                    ]);
                }
            }
            \Yii::$app->session->setFlash("danger", "Moderatsiya log" . json_encode($log->errors));
            return $this->render('view', [
                'model' => $model,
            ]);
        } else {
            \Yii::$app->session->setFlash("danger", "Tender moderatsiya holatida emas");
            return $this->render('view', [
                'model' => $model,
            ]);

        }

    }


    public function actionModeratorDmbat()
    {
        $model = $this->findModel(Yii::$app->request->get()['id']);
        $log = new TenderModeratorLogResource();
        if ($model->status == TenderEnum::STATUS_ACTIVE && $model->state == TenderEnum::STATE_DMBAT) {
            $log->tender_id = $model->id;
            $log->state = TenderEnum::STATE_READY;
            $log->moderator_pinfl = \Yii::$app->user->identity->username;
            $log->description = 'dmbat tasdiqladi';
            if ($log->save()) {
                $holidays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::HOLIDAY]), 'local_date', 'local_date');
                $workDays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::WORKDAY]), 'local_date', 'local_date');

                $model->state = TenderEnum::STATE_READY;
                $this->model->publish_days = $this->model->placement_period;
                $endDate = addDaysExcludingWeekends(date("Y-m-d H:i:s"), $this->model->placement_period, $workDays, $holidays);
                $this->model->end_date = $endDate;
                if ($model->save()) {
                    \Yii::$app->session->setFlash("success", "Moderatsiyadan dmbatdan o'tkazdi, E'lon qilindi");
                    return $this->redirect(['index']);
                } else {
                    \Yii::$app->session->setFlash("danger", "Moderatsiyadan o'tmadi" . json_encode($model->errors));
                    return $this->render('view', [
                        'model' => $model,
                    ]);
                }
            }
            \Yii::$app->session->setFlash("danger", "Moderatsiya log" . json_encode($log->errors));
            return $this->redirect(['index']);
        } else {
            \Yii::$app->session->setFlash("danger", "Tender moderatsiya holatida emas");
            return $this->redirect(['index']);

        }

    }

    public function actionModeratorDmbatReject()
    {
        $model = $this->findModel(Yii::$app->request->post()['id']);
        $log = new TenderModeratorLogResource();
        if ($model->status == TenderEnum::STATUS_ACTIVE && $model->state == TenderEnum::STATE_DMBAT) {
            $log->tender_id = $model->id;
            $log->state = TenderEnum::STATE_DMBAT_REJECT;
            $log->moderator_pinfl = \Yii::$app->user->identity->username;
            $log->description = Yii::$app->request->post()['description'];
            if ($log->save()) {
                $model->state = TenderEnum::STATE_DMBAT_REJECT;
                if ($model->save()) {
                    \Yii::$app->session->setFlash("success", "Moderatsiyadan dmbatdan o'tkazmadi");
                    return $this->render('view', [
                        'model' => $model,
                    ]);
                } else {
                    \Yii::$app->session->setFlash("danger", "Moderatsiyadan o'tmadi" . json_encode($model->errors));
                    return $this->render('view', [
                        'model' => $model,
                    ]);
                }
            }
            \Yii::$app->session->setFlash("danger", "Moderatsiya log" . json_encode($log->errors));
            return $this->render('view', [
                'model' => $model,
            ]);
        } else {
            \Yii::$app->session->setFlash("danger", "Tender moderatsiya holatida emas");
            return $this->render('view', [
                'model' => $model,
            ]);

        }

    }

    /**
     * Updates an existing Tender model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param int $id ID
     * @return mixed
     */
//    public function actionUpdate($id)
//    {
//        $model = $this->findModel($id);
//
//        if ($model->load(Yii::$app->request->post()) && $model->save()) {
//            return $this->redirect(['view', 'id' => $model->id]);
//        }
//        return $this->render('update', [
//            'model' => $model,
//        ]);
//    }


    /**
     * Finds the Tender model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id ID
     * @return Tender the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Tender::findOne($id)) !== null) {
            return $model;
        }
        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
