<?php

namespace api\modules\common\filters;

use api\components\BaseRequest;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\TenderEnum;
use Yii;

class PlanScheduleBudgetFilter extends BaseRequest
{

    public $search;

    public function rules()
    {
        return [
            ['search', 'safe']
        ];
    }

    public function getResult()
    {
        $user = Yii::$app->user->identity;
        if (!$user->isBudget) {
            $this->addError("error", t("Ruxsat mavjud emas"));
            return false;
        }
        $query = PlanScheduleResource::find()
            ->where(['company_id' => $user->company_id, 'organ' => $user->organ]);

        if ($this->search) {
            if (is_numeric($this->search)) {
                $query->andWhere(['or',
                    ['year' => $this->search],
                    ['quarter' => $this->search]
                ]);
            } else {
                $query->andWhere(['like', 'title', $this->search]);
            }
        }
        $query->orderBy('id desc');
        return paginate($query);
    }
}
