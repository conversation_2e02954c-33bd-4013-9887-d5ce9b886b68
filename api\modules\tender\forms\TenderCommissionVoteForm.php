<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderCommissionVoteResource;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;
use Yii;
use yii\web\ForbiddenHttpException;

class TenderCommissionVoteForm extends BaseRequest
{

    public TenderResource $model;
    public TenderCommissionVoteResource $tenderVoting;

    public $vote;
    public $description;

    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;
        $this->tenderVoting = new TenderCommissionVoteResource();

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            ['vote', 'required', 'message' => t('{attribute} yuborish majburiy')],
            ['vote', 'integer'],
            ['vote', 'checkVote'],
            ['description', 'required', 'when' => function ($model) {
                return $model->vote === TenderEnum::VOTE_NO || $model->vote === "0";
            }],
            ['description', 'string', 'max' => 255]
        ];
    }

    public function checkVote()
    {
        if (in_array($this->vote, [TenderEnum::VOTE_NO, TenderEnum::VOTE_YES, TenderEnum::VOTE_NEUTRAL])) {
            return true;
        } else {
            $this->addError("vote", t("Ovoz berish qiymatlari 1 yoki 0 bo'lishi kerak"));
            return false;
        }
    }

    public function getResult()
    {

        if ($this->model->state == TenderEnum::STATE_ACCEPT_MODERATOR || $this->model->state == TenderEnum::STATE_READY_FOR_CHAIRMAN) {

            $commissionId = \Yii::$app->user->identity->commissionMemberId;

            $tenderCom = $this->model->getCommissionMember($commissionId);
            if (!in_array($tenderCom->role, [TenderEnum::ROLE_COMMISSION, TenderEnum::ROLE_CHAIRMAN])) {
                $this->addError("error", t("Komisiya yoki rais roli uchun ruxsat mavjud"));
                return false;
            }

            if ($tenderCom->role == TenderEnum::ROLE_COMMISSION && $this->model->getCommissionVote($commissionId)) {
                $this->addError('vote', t("Avval bu tender uchun ovoz bergansiz"));
                return false;
            }


            $transaction = \Yii::$app->db->beginTransaction();

            $this->tenderVoting->tender_id = $this->model->id;
            $this->tenderVoting->status = TenderEnum::STATUS_ACTIVE;
            $this->tenderVoting->commission_member_id = $commissionId;
            $this->tenderVoting->role = $tenderCom->role;
            $this->tenderVoting->vote = $this->vote;
            $this->tenderVoting->description = $this->description;

            if ($this->tenderVoting->save()) {

                $memberCount = $this->model->getTenderCommissionMembersCount();
                $countVotes = $this->model->getCommissionVoteCount();

                if ((ceil(($memberCount - 1) * 2 / 3)) <= $countVotes) {
                    $voteNoCount = $this->model->getCommissionVoteNoCount();
                    $voteCountYes = $this->model->getCommissionVoteYesCount();
                    if ($voteCountYes == $voteNoCount) {
                        $chairmanVote = $this->model->getCommissionVoteChairmanCount();
                        if($chairmanVote){
                            if ($chairmanVote == TenderEnum::VOTE_YES) {
                                $this->model->state = TenderEnum::STATE_READY_FOR_CHAIRMAN;
                            } else {
                                $this->model->state = TenderEnum::STATE_REJECT_COMMISSIONS;
                            }
                            if (!$this->model->save()) {
                                $transaction->rollBack();
                                $this->addErrors($this->model->errors);
                                return false;
                            }
                        }

                    } else if ($voteCountYes > $voteNoCount) {
                        if($this->model->state != TenderEnum::STATE_READY_FOR_CHAIRMAN){
                            $this->model->state = TenderEnum::STATE_READY_FOR_CHAIRMAN;
                            if (!$this->model->save()) {
                                $transaction->rollBack();
                                $this->addErrors($this->model->errors);
                                return false;
                            }
                        }
                    } else {
                        $this->model->state = TenderEnum::STATE_REJECT_COMMISSIONS;
                        if (!$this->model->save()) {
                            $transaction->rollBack();
                            $this->addErrors($this->model->errors);
                            return false;
                        }
                    }

                }
                $transaction->commit();
                return true;
            } else {
                $this->addErrors($this->tenderVoting->errors);
                return false;
            }
        } else {
            $this->addError("description", t("Tender kelishish jarayonida emas"));
            return false;
        }
    }
}