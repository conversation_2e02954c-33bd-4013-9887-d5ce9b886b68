<?php

namespace api\modules\auth\controllers;

use api\components\ApiController;
use api\modules\auth\forms\ChallengeForm;
use api\modules\auth\forms\FrontendTimestampForm;
use api\modules\auth\forms\LoginEdsForm;
use api\modules\auth\forms\LoginForm;
use api\modules\auth\forms\LogoutForm;
use api\modules\auth\forms\SetActiveProfileForm;
use api\modules\auth\forms\SignupForm;
use api\modules\auth\forms\SignupNoResidentForm;
use api\modules\auth\forms\UpdateForm;
use api\modules\auth\resources\UserResource;
use app\modules\auth\forms\UserTypeForm;
use Yii;
use yii\web\UnauthorizedHttpException;

class UserController extends ApiController
{

    public function actionLogin()
    {
        return $this->sendResponse(
            new LoginForm(),
            Yii::$app->request->bodyParams
        );
    }

    /**
     * @throws UnauthorizedHttpException
     */
    public function actionMe()
    {
        if(Yii::$app->user->isGuest) {
            throw new UnauthorizedHttpException("Sessiya vaqti tugadi");
        }
        $user = UserResource::findOne(Yii::$app->user->id);
        return $this->sendModel($user);
    }

    public function actionSetActiveProfile()
    {
        //user/set-active-profile
        return $this->sendResponse(
            new SetActiveProfileForm(),
            Yii::$app->request->bodyParams
        );
    }

    public function actionUpdate()
    {
        return $this->sendResponse(
            new UpdateForm(),
            Yii::$app->request->bodyParams
        );
    }

    public function actionLogout()
    {
        return $this->sendResponse(new LogoutForm());
    }

    public function actionSignup()
    {
        return $this->sendResponse(
            new SignupForm(),
            Yii::$app->request->bodyParams
        );
    }

    public function actionSignupNoResident()
    {
        return $this->sendResponse(
            new SignupNoResidentForm(),
            Yii::$app->request->bodyParams
        );
    }

//    public function actionLoginPks7Ping(){
//        return checkPkcsPing();
//    }
    public function actionChallenge()
    {
        return $this->sendResponse(
            new ChallengeForm(),
            Yii::$app->request->bodyParams,
        );
    }

    public function actionLoginPkcs7()
    {
        return $this->sendResponse(
            new LoginEdsForm(),
            Yii::$app->request->bodyParams
        );
    }

    public function actionChangeUserType()
    {
        return $this->sendResponse(
            new UserTypeForm(),
            Yii::$app->request->bodyParams
        );
    }

    public function actionFrontendTimestamp()
    {
        return $this->sendResponse(
            new FrontendTimestampForm(),
            Yii::$app->request->bodyParams
        );
    }
}
