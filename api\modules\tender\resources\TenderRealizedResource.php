<?php


namespace api\modules\tender\resources;


use api\modules\auth\resources\UserResource;
use api\modules\backend\resources\TenderModeratorLogResource;
use api\modules\client\resources\TenderRequestResource;
use api\modules\client\resources\TenderRequestWinnerResource;
use api\modules\client\resources\TenderRequirementsAnswerResource;
use api\modules\common\resources\DistrictResource;
use api\modules\common\resources\FileResource;
use api\modules\common\resources\PlanScheduleResource;
use api\modules\common\resources\RegionResource;
use common\enums\TenderEnum;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\Tender;
use yii\web\NotFoundHttpException;

class TenderRealizedResource extends Tender
{
    public function fields()
    {
        return [
            'id',
            'lot',
            'type',
            'state',
            'created_at',
            'tenderTotalPrice' => function($model){
                return $model->getTenderTotalPriceBase();
            },
            'tenderClassifiers',
            'tenderRequirements',
            'title',
            'purchase_currency',
            'description',
            'amount_deposit',
            'accept_guarantee_bank',
            'method_payment',
            'advance_payment_percentage',
            'advance_payment_percentage',
            'advance_payment_period',
            'unblocking_type',
            'contact_position',
            'contact_phone',
            'contact_fio',
            'address',
            'delivery_phone',
            'publish_days',
            'end_date',
            'criteria_evaluation_proposals',
            'technical_part',
            'price_part',
            'language',
            'updated_at',
            'preference_local_producer',
            'contract'
        ];
    }

    public function extraFields()
    {
        return [
            'contactFile',
            'technicalDocumentFile',
            'technicalTaskFile',
            'planSchedule',
            'region',
            'district',
            'tenderCommissionMembers',
            'commissionMemberRole',
            'tenderRequirementsAnswer',
            'tenderRequest',
            'qualificationSelection',
            'tenderModeratorLog',
            'tenderRequestRating',
            'tenderRequestCount',
            'commissionVote',
            'company',
            'deleted_at',
            'deletedBy' => function ($model) {
                return $model->updatedBy ? $model->updatedBy->username : $model->updated_by;
            },
            'tenderRequestWinner'
        ];
    }

    public function getCompany()
    {
        return $this->hasOne(CompanyResource::class, ['id' => 'company_id']);
    }

    public function getCommissionMemberRole()
    {
        $model = TenderCommissionMemberResource::find()->notDeleted()
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_id' => $this->id])
            ->andWhere(['commission_member_id' => \Yii::$app->user->identity->commissionMemberId])
            ->one();
        return $model ? $model->role : "yoq";
    }

    public function getContactFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'contact_file_id']);
    }


    public function getDistrict()
    {
        return $this->hasOne(DistrictResource::class, ['id' => 'district_id']);
    }

    public function getContract()
    {
        return ContractResource::find()->where(['tender_id' => $this->id])->orderBy(['created_at' => SORT_DESC])->one();
    }


    public function getPlanSchedule()
    {
        return $this->hasOne(PlanScheduleResource::class, ['id' => 'plan_schedule_id']);
    }


    public function getRegion()
    {
        return $this->hasOne(RegionResource::class, ['id' => 'region_id']);
    }


    /**
     * Gets query for [[TenderClassifiers]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTenderClassifiers()
    {
        return $this->hasMany(TenderClassifierResource::class, ['tender_id' => 'id'])->andWhere([TenderClassifierResource::tableName() . '.status' => TenderEnum::STATUS_ACTIVE]);
    }

    public function getTenderRequirements()
    {
        return $this->hasMany(TenderRequirementsResource::class, ['tender_id' => 'id']);
    }

    public function getTenderRequirementsAnswer()
    {
        return $this->hasMany(TenderRequirementsAnswerResource::class, ['tender_id' => 'id']);
    }

    public function getTenderRequestRating()
    {
        return $this->hasMany(TenderRequestRatingResource::class, ['tender_id' => 'id']);
    }


    public function getTenderRequest()
    {
        return TenderRequestResource::find()
            ->notDeleted()
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_id' => $this->id])
            ->all();
        //return $this->hasMany(TenderRequestResource::class, ['tender_id' => 'id']);
    }

    public function getTenderRequestWinner()
    {
        return TenderRequestWinnerResource::find()
            ->notDeleted()
            ->andWhere(['is_winner' => TenderEnum::IS_WINNER_YES])
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_id' => $this->id])
            ->one();
    }

    public function getTenderRequestCount()
    {
        return count($this->tenderRequest);
    }

    public function getQualificationSelection()
    {
        return $this->hasMany(TenderQualificationSelectionResource::class, ['tender_id' => 'id']);
    }

    public function getTenderCommissionMembers()
    {
        return TenderCommissionMemberResource::find()->notDeletedAndFromCompany()->andWhere(['tender_id' => $this->id, 'status' => TenderEnum::STATUS_ACTIVE])->all();
//        return $this->hasMany(TenderCommissionMemberResource::class, ['tender_id' => 'id']);
    }

    public function getTenderCommissionMembersCount()
    {
        return TenderCommissionMemberResource::find()->notDeletedAndFromCompany()->where(['tender_id' => $this->id, 'status' => TenderEnum::STATUS_ACTIVE])->count();
    }


    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

//    public function getTenderTotalPrice()
//    {
//        $price = 0;
//        foreach ($this->tenderClassifiers as $classifier) {
//            $price += $classifier->price;
//        }
//        return $price;
//    }

//    public function getCommissionMember(int $commissionId)
//    {
//        $model = TenderCommissionMemberResource::find()->notDeletedAndFromCompany()
//            ->where(['tender_commission_member.tender_id' => $this->id, 'tender_commission_member.commission_member_id' => $commissionId, 'tender_commission_member.status' => TenderEnum::STATUS_ACTIVE])
//            ->andWhere(['tender_commission_member.status' => TenderEnum::STATUS_ACTIVE])
//            ->one();
//        if (!$model) new NotFoundHttpException("Ma'lumot topilmadi");
//        return $model;
//    }

    public function getCommissionVote(int $commissionId = null)
    {
        if ($commissionId === null) {
            $commissionId = \Yii::$app->user->identity->commissionMemberId;
        }
        return TenderCommissionVoteResource::find()
            ->where(['tender_id' => $this->id, 'commission_member_id' => $commissionId])
            ->exists();
    }

    public function getCommissionVoteCount()
    {
        return TenderCommissionVoteResource::find()
            ->where(['tender_id' => $this->id, 'status' => TenderEnum::STATUS_ACTIVE])
            ->count();
    }

    public function getCommissionVoteYesCount()
    {
        return TenderCommissionVoteResource::find()
            ->where(['tender_id' => $this->id, 'vote' => TenderEnum::VOTE_YES, 'status' => TenderEnum::STATUS_ACTIVE])
            ->count();
    }

    public function getCommissionVoteNoCount()
    {
        return TenderCommissionVoteResource::find()
            ->where(['tender_id' => $this->id, 'vote' => TenderEnum::VOTE_NO, 'status' => TenderEnum::STATUS_ACTIVE])
            ->count();
    }

    public function getTenderModeratorLog()
    {
        return TenderModeratorLogResource::find()->where(['tender_id' => $this->id])->orderBy(['id' => SORT_DESC])->one();
    }

    public function getUpdatedBy()
    {
        return $this->hasOne(UserResource::class, ['id' => 'updated_by']);
    }

}