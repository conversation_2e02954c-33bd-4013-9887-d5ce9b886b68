<?php

namespace common\models\shop;

use common\models\Region;
use common\traits\NotDeleted;
use common\traits\SoftDelete;
use Yii;

/**
 * This is the model class for table "product_draft_region".
 *
 * @property int $id
 * @property int|null $product_id
 * @property int|null $region_id
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 *
 * @property ProductDraft $product
 * @property Region $region
 */
class ProductDraftRegion extends \yii\db\ActiveRecord
{
    use SoftDelete;
    use NotDeleted;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'product_draft_region';
    }

    /**
     * {@inheritdoc}
     */
//    public function rules()
//    {
//        return [
//            [['product_id', 'region_id', 'created_at', 'updated_at', 'deleted_at', 'created_by', 'updated_by'], 'default', 'value' => null],
//            [['product_id', 'region_id', 'created_by', 'updated_by'], 'integer'],
//            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
//            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => ProductDraft::class, 'targetAttribute' => ['product_id' => 'id']],
//            [['region_id'], 'exist', 'skipOnError' => true, 'targetClass' => Region::class, 'targetAttribute' => ['region_id' => 'id']],
//        ];
//    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'product_id' => 'Product ID',
            'region_id' => 'Region ID',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'deleted_at' => 'Deleted At',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
        ];
    }

    /**
     * Gets query for [[Product]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        return $this->hasOne(ProductDraft::class, ['id' => 'product_id']);
    }

    /**
     * Gets query for [[Region]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getRegion()
    {
        return $this->hasOne(Region::class, ['id' => 'region_id']);
    }

}
