<?php


namespace api\modules\common\forms;


use api\components\BaseRequest;
use api\modules\common\resources\AffiliatesResource;
use api\modules\common\resources\CompanyResource;
use Yii;

class AffiliatesCreateForm extends BaseRequest
{
    public $full_name;
    public $pinfl;

    public AffiliatesResource $model;


    public function rules()
    {
        return [
            [['full_name','pinfl'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            ['full_name', 'string', 'max' => 255],
            ['pinfl', 'string', 'min' => 14, 'max' => 14, 'message' => t('PINFL 14 ta raqamdan iborat bo\'lishi kerak')],
            ['pinfl', 'validateUniquePinfl']
        ];
    }

    public function validateUniquePinfl($attribute, $params)
    {
        if (AffiliatesResource  ::find()->where(['pinfl' => $this->$attribute])->exists()) {
            $this->addError($attribute, t('Bu PINFL allaqachon mavjud'));
        }
    }


    public function __construct(AffiliatesResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }
    
    public function getResult()
    {
        $company_id = Yii::$app->user->identity->company_id;
        $company = CompanyResource::findOne($company_id);

        if (!$company) {
            $this->addError('company_id', t('Foydalanuvchi kompaniyasi topilmadi'));
            return false;
        }

        $this->model->full_name = $this->full_name;
        $this->model->pinfl = $this->pinfl;
        $this->model->company_tin = $company->tin;
        $this->model->company_id = $company->id;

        if (!$this->model->save()){
            $this->addErrors($company->getErrors());
        }

        return true;
    }
}