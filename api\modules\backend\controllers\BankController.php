<?php


namespace api\modules\backend\controllers;


//use common\components\services\BankComponent;
use common\enums\BankServiceEnum;
use common\enums\CompanyTransactionEnum;
use common\models\bank\BankTransactionIn;
use common\models\Company;
use common\models\CompanyBankAccount;
use console\models\CompanyTransaction;
use yii\httpclient\Client;
use yii\rest\Controller;

class BankController extends Controller
{

    public function actionKirim($fromDate = null)
    {
        $lastOne = BankTransactionIn::find()->orderBy(['bank_id' => SORT_DESC])->one();
        $lastId = 0;
        if ($lastOne != null) {
            $lastId = $lastOne->bank_id;
        }

        if ($fromDate != null) {
            $fromDate = date('Y-m-d', strtotime($fromDate));
        } else {
            $fromDate = date('Y-m-d');
        }

        $requestData = [
            "type" => "1", // 0-barchasi, 1-krim, 2-chiqim
            "lastId" => $lastId,
            "fromDate" => $fromDate,
            "toDate" => date('Y-m-d'),
            "serviceId" => BankServiceEnum::SERVICE_ID,
        ];

        $inPayments = $this->getAccountPayments($requestData);
        if (!empty($inPayments)) {
            if ($inPayments['status'] == "success") {
                foreach ($inPayments['data'] as $account) {

                    $check = BankTransactionIn::find()->where(['bank_id' => $account['id']])->one();
                    if ($check == null) {
                        $tran = $this->saveTransactionIn($account);
                        $res = $this->setBalance($tran, true);
                        if ($res) {
                            $tran->transaction_state = BankTransactionIn::STATE_ATTACHED;
                        } else {
                            $tran->transaction_state = BankTransactionIn::STATE_COMPANY_NOT_FOUND;
                        }
                        $tran->save();
                    }
                }
            }

        }
    }

    /*
     * birikmagan pullarni biriktiradi
     * */
    public function actionBiriktirish()
    {
        $bankTransactionIns = BankTransactionIn::find()
            ->where(['transaction_state' => BankTransactionIn::STATE_COMPANY_NOT_FOUND])
            ->andWhere(['!=', 'inn_payer', BankServiceEnum::BANK_YATT_PAYER_INN])
            ->all();

        echo "++++++ Korxonasi yo'qlar soni=" . count($bankTransactionIns) . "\n";

        if (!empty($bankTransactionIns)) {
            foreach ($bankTransactionIns as $bankTransactionIn) {
                $res = $this->setBalance($bankTransactionIn, false);
                if ($res) {
                    BankTransactionIn::updateAll(['transaction_state' => BankTransactionIn::STATE_ATTACHED], 'id=' . $bankTransactionIn->id);
                }
            }
        }
    }

    /**
     * @var $bankTransactionIn BankTransactionIn
     */
    private function saveTransactionIn($transactionDto)
    {
        $bankTransactionIn = new BankTransactionIn();
        $bankTransactionIn->bank_id = $transactionDto['id'];
        $bankTransactionIn->doc_number = $transactionDto['docNumber'];
        $bankTransactionIn->doc_date = $transactionDto['docDate'];
        $bankTransactionIn->payment_date = $transactionDto['paymentDate'];
        $bankTransactionIn->type = $transactionDto['type'];
        $bankTransactionIn->mfo_payer = $transactionDto['mfoPayer'];
        $bankTransactionIn->account_payer = $transactionDto['accountPayer'];
        $bankTransactionIn->name_payer = $transactionDto['namePayer'];
        $bankTransactionIn->inn_payer = $transactionDto['innPayer'];
        $bankTransactionIn->mfo_receiver = $transactionDto['mfoReceiver'];
        $bankTransactionIn->account_receiver = $transactionDto['accountReceiver'];
        $bankTransactionIn->name_receiver = $transactionDto['nameReceiver'];
        $bankTransactionIn->inn_receiver = $transactionDto['innReceiver'];
        $bankTransactionIn->amount = $transactionDto['amount'];
        $bankTransactionIn->purpose = $transactionDto['purpose'];
        $bankTransactionIn->status = $transactionDto['status'];
        $bankTransactionIn->created_at = date("Y-m-d H:i:s");
        $bankTransactionIn->save();
        return $bankTransactionIn;
    }

    private function checkCompanyAccount($transactionIn, $company)
    {
        $companyAccount = CompanyBankAccount::findOne([
            'company_id' => $company->id,
            'account' => $transactionIn->account_payer,
        ]);

        if ($companyAccount === null) {
//            $banks = Bank::findOne(['mfo' => $transactionIn->mfoPayer]);
            $isMainCompanyAccount = CompanyBankAccount::find()
                ->where(['company_id' => $company->id, 'is_main' => true])
                ->one();

            $companyAccount = new CompanyBankAccount();
//            $companyAccount->bank = $banks ? $banks->name : '-';
            $companyAccount->account = $transactionIn->account_payer;
            $companyAccount->mfo = $transactionIn->mfo_payer;
            $companyAccount->company_id = $company->id;
            if ($isMainCompanyAccount === null) {
                $companyAccount->is_main = true;
            }
            $companyAccount->save();
        }
    }

    private function setBalance($dataIn, $isNew)
    {
        if ($dataIn->inn_payer !== BankServiceEnum::BANK_YATT_PAYER_INN) {
            $company = Company::findOne(['tin' => $dataIn->inn_payer]);
            if ($company !== null) {
                // TransactionCreateDto obyektini yaratish va TransactionService orqali saqlash
//                $transaction = new BankTransactions();

                $cc = new CompanyTransaction([
                    'company_id' => $company->id,
                    'amount' => $dataIn->amount,
                    'type' => CompanyTransactionEnum::TYPE_REFILL,
                    'description' => "Bankdan hisob to'ldirildi",
                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                    'transaction_date' => date("Y-m-d H:i:s"),
                ]);
                if (!$cc->save()) {
                    return false;
                }

                // Kompaniya hisobini tekshirish
                $this->checkCompanyAccount($dataIn, $company);
                return true;
            }
        }

        return false;
    }

    private function getAccountPayments($requestData)
    {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://rpay.aloqabank.uz:4041/api/v2/account/payments");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));

        $headers = [
            'Authorization: Basic ' . base64_encode('dtxarid:2hhnBJ6P8lsg'),
            'Content-Type: application/json',
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if ($httpCode == 200) {
            $responseData = json_decode($response, true);
            return $responseData;
        } else {
            return [];
        }

    }


    /**
     * @var $lastOne BankTransactionIn
     */
    public function actionKirimList($fromDate = null)
    {
        $lastOne = BankTransactionIn::find()->orderBy(['bank_id' => SORT_DESC])->one();
        $lastId = 0;
        if ($lastOne != null) {
            $lastId = $lastOne->bank_id;
        }

        if ($fromDate != null) {
            $fromDate = date('Y-m-d', strtotime($fromDate));
        } else {
            $fromDate = date('Y-m-d');
        }

        $requestData = [
            "type" => "0", // 0-barchasi, 1-krim, 2-chiqim
            "lastId" => $lastId,
            "fromDate" => $fromDate,
            "toDate" => date('Y-m-d'),
            "serviceId" => BankServiceEnum::SERVICE_ID,
        ];

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://rpay.aloqabank.uz:4041/api/v2/account/payments");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));

        $headers = [
            'Authorization: Basic ' . base64_encode('dtxarid:2hhnBJ6P8lsg'),
            'Content-Type: application/json',
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if ($httpCode == 200) {
            $responseData = json_decode($response, true);
            return $responseData;
        } else {
            var_dump($response);
            die;
        }
    }

    public function actionService()
    {

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://rpay.aloqabank.uz:4041/api/v2/services");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        $headers = [
            'Authorization: Basic ' . base64_encode('dtxarid:2hhnBJ6P8lsg'),
            'Content-Type: application/json',
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if ($httpCode == 200) {
            $responseData = json_decode($response, true);
            return $responseData;
        } else {
            var_dump($response);
            die;
        }
    }

    public function actionServiceBalance()
    {

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://rpay.aloqabank.uz:4041/api/v2/account/33/balance");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        $headers = [
            'Authorization: Basic ' . base64_encode('dtxarid:2hhnBJ6P8lsg'),
            'Content-Type: application/json',
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if ($httpCode == 200) {
            $responseData = json_decode($response, true);
            return $responseData;
        } else {
            var_dump($response);
            die;
        }
    }


}