<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;

class TenderDmbatFilter extends BaseRequest
{
    public $lot;

    public function rules()
    {
        return [
            [['lot'], 'safe'],
        ];
    }


    public function getResult()
    {
        $model = TenderResource::find()->notDeletedAndFromCompany();
        $model->andWhere(['in', 'state', [TenderEnum::STATE_DMBAT, TenderEnum::STATE_DMBAT_REJECT]]);
        if ($this->lot) {
            $model->andWhere(['like', 'lot', $this->lot]);
        }

        $model->orderBy(['updated_at' => SORT_ASC]);
        return paginate($model);


    }
}