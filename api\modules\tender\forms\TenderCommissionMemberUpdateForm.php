<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\tender\resources\CommissionGroupMemberResource;
use api\modules\tender\resources\CommissionGroupResource;
use api\modules\tender\resources\CommissionMemberResource;
use api\modules\tender\resources\TenderClassifierResource;
use api\modules\tender\resources\TenderCommissionMemberResource;
use api\modules\tender\resources\TenderCommissionVoteResource;
use api\modules\tender\resources\TenderRequestRatingCommissionResource;
use api\modules\tender\resources\TenderRequirementsResource;
use api\modules\tender\resources\TenderResource;
use common\enums\StatusEnum;
use common\enums\TenderEnum;
use Yii;

class TenderCommissionMemberUpdateForm extends BaseRequest
{

    public TenderResource $model;
    public TenderCommissionMemberResource $modelNew;

    public $commission_member_old_id;
    public $commission_member_new_id;

    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;
        $this->modelNew = new TenderCommissionMemberResource();


        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [['commission_member_old_id', 'commission_member_new_id'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['commission_member_old_id', 'commission_member_new_id'], 'integer'],
            [['commission_member_new_id'], 'exist', 'skipOnError' => true, 'targetClass' => CommissionMemberResource::class, 'targetAttribute' => ['commission_member_new_id' => 'id']],
            [['commission_member_old_id'], 'exist', 'skipOnError' => true, 'targetClass' => CommissionMemberResource::class, 'targetAttribute' => ['commission_member_old_id' => 'id']],
        ];
    }

    public function getResult()
    {

        if(!CommissionMemberResource::find()->notDeletedAndFromCompany()->andWhere(['id' => $this->commission_member_old_id])->exists()){
            $this->addError('commission_member_old_id',t("Komissiya azosi topilmadi"));

            return false;
        }

        /**
         * @var $tenderCommissionMemberOld TenderCommissionMemberResource
        */
        $tenderCommissionMemberOld = TenderCommissionMemberResource::find()->notDeletedAndFromCompany()
            ->andWhere(['commission_member_id' => $this->commission_member_old_id, 'tender_id' => $this->model->id])->one();
        if(!$tenderCommissionMemberOld){
            $this->addError('commission_member_old_id',t("Almashtirilayotgan komissiya a'zosi tenderga a'zo qilinmagan"));

            return false;
        }

        if(!CommissionMemberResource::find()->notDeletedAndFromCompany()->andWhere(['id' => $this->commission_member_new_id])->exists()){
            $this->addError('commission_member_new_id',t("Komissiya azosi topilmadi"));

            return false;
        }

        if(TenderCommissionMemberResource::find()->notDeletedAndFromCompany()->andWhere(['commission_member_id' => $this->commission_member_new_id, 'tender_id' => $this->model->id])->exists()){
            $this->addError('commission_member_new_id',t("Yangi komissiya azosi tenderga avval a'zo qilingan"));

            return false;
        }

        /**
         * @var $tender TenderResource
        */
        $tender = $this->model;
        if(in_array($tender->state, [TenderEnum::STATE_READY]) || $tender->status == TenderEnum::STATUS_DELETED){
            $this->addError('commission_member_new_id',t("Tahrirlash mumkin emas."));

            return false;
        }

        if(TenderRequestRatingCommissionResource::find()->where(['tender_id' => $this->model->id, 'commission_member_id' => $this->commission_member_old_id])->exists()){
            $this->addError('commission_member_old_id',t("Takliflarga baholashda ishtirok etilgan. Tahrirlash mumkin emas"));

            return false;
        }


        $transaction = \Yii::$app->db->beginTransaction();

        $group = CommissionGroupMemberResource::find()->notDeleted()
            ->andWhere(['commission_member_id' => $this->commission_member_new_id])
            ->orderBy(['created_at' => SORT_DESC])->one();

        $this->modelNew->company_id = $this->model->company_id;
        $this->modelNew->status = TenderEnum::STATUS_ACTIVE;
        $this->modelNew->tender_id = $this->model->id;
        $this->modelNew->role = $tenderCommissionMemberOld->role;
        $this->modelNew->commission_group_member_id = $group ? $group->id : $tenderCommissionMemberOld->commission_group_member_id;
        $this->modelNew->commission_member_id = $this->commission_member_new_id;
        if($this->modelNew->save()){
            $tenderCommissionMemberOld->status = TenderEnum::STATUS_CHANGED;
            if(!$tenderCommissionMemberOld->save()){
                $transaction->rollBack();
                $this->addErrors($this->model->errors);
                return false;
            }
        } else {
            $transaction->rollBack();
            $this->addErrors($this->modelNew->errors);
            return false;
        }
        $transaction->commit();
        return true;
    }
}