<?php

namespace common\models;

use api\modules\common\resources\CompanyResource;
use api\modules\common\resources\CompanyTransactionResource;
use Yii;

/**
 * This is the model class for table "company_transaction_refunds".
 *
 * @property int $id
 * @property int $bank_account_id
 * @property string|null $account_number
 * @property int|null $company_id
 * @property float|null $sum
 * @property int|null $transaction_id
 * @property int|null $status
 * @property string|null $comment
 * @property string|null $created_at
 * @property int|null $created_by
 */
class CompanyTransactionRefunds extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'company_transaction_refunds';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['company_id', 'transaction_id', 'status', 'created_by', 'bank_account_id'], 'integer'],
            [['sum'], 'number'],
            [['comment'], 'string'],
            [['created_at'], 'safe'],
            [['account_number'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'bank_account_id' => 'Bank Account ID',
            'account_number' => 'Account Number',
            'company_id' => 'Company ID',
            'sum' => 'Sum',
            'transaction_id' => 'Transaction ID',
            'status' => 'Status',
            'comment' => 'Comment',
            'created_at' => 'Created At',
            'created_by' => 'Created By',
        ];
    }
    public function getCompany()
    {
        return $this->hasOne(CompanyResource::class,['id'=>'company_id']);
    }
    public function getTransaction()
    {
        return $this->hasOne(CompanyTransactionResource::class,['transaction_id'=>'id']);
    }
}
