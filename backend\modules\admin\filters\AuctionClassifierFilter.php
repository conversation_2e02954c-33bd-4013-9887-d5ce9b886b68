<?php


namespace backend\modules\admin\filters;


use api\components\BaseRequest;
use backend\modules\admin\resources\AuctionClassifierResource;

class AuctionClassifierFilter extends BaseRequest
{
//    public $id;
//
//    public function rules()
//    {
//        return [
//            ['id', 'required', 'message' => t('{attribute} yuborish majburiy')],
//            ['id', 'safe'],
//        ];
//    }

    public function getResult()
    {
        $query = AuctionClassifierResource::find()->all();
        return $query;
    }
}