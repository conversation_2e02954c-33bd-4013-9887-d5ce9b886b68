<?php

namespace app\modules\client\filters;

use api\components\BaseRequest;
use app\modules\client\resources\SoleSupplierResource;

class SoleSupplierFilter extends BaseRequest
{
    public $searchFilter;

    public function rules (){
        return [
            [['searchFilter'], 'safe']
        ];
    }


    public function getResult(): array
    {
       $query = SoleSupplierResource::find()->where('1=1');

       if(!isEmpty($this->searchFilter)){
           if(isTin($this->searchFilter)){
                $query->andWhere(['tin' => $this->searchFilter]);
           } else {
               $query->joinWith('classifiers');
               $query->andWhere(['like', 'classifier.code', $this->searchFilter]);
           }
       }

       return paginate($query);
    }
}