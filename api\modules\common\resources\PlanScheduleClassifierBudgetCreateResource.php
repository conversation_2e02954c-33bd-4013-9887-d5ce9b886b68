<?php


namespace api\modules\common\resources;


use common\models\Classifier;
use common\models\PlanSchedule;
use common\models\PlanScheduleClassifier;

class PlanScheduleClassifierBudgetCreateResource extends PlanScheduleClassifier
{
  public function rules()
  {
    return [
      [['plan_schedule_id', 'classifier_id', 'status', 'unit_id', 'year', 'month', 'count', 'count_live', 'count_used',
      'kls', 'company_bank_account_id', 'tovar', 'tovarname', 'expense', 'count', 'tovarprice', 'summa', 'address', 'srok'], 'required', 'message' => t('{attribute} yuborish majburiy')],
      [['plan_schedule_id', 'classifier_id', 'status', 'enabled', 'created_by', 'updated_by', 'unit_id', 'year', 'month', 'count', 'count_live', 'count_used'], 'integer'],
      [['created_at', 'updated_at', 'deleted_at'], 'safe'],
      [['description'], 'string', 'max' => 255],
      [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => Classifier::class, 'targetAttribute' => ['classifier_id' => 'id']],
      [['plan_schedule_id'], 'exist', 'skipOnError' => true, 'targetClass' => PlanSchedule::class, 'targetAttribute' => ['plan_schedule_id' => 'id']],
    ];
  }
}
