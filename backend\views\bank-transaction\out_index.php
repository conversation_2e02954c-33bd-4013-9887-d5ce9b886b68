<?php

use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\Url;

/**
 * @var yii\web\View $this
 * @var common\models\bank\search\BankTransactionOutSearch $searchModel
 * @var yii\data\ActiveDataProvider $dataProvider
 */

$this->title = t("Bank transaction OUT");
$this->params['breadcrumbs'][] = $this->title;


?>


<div class="black-list-index">
    <div class="card">

        <div class="card-body p-0">
            <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

            <?php echo GridView::widget([
                'layout' => "{items}\n{pager}",
                'options' => [
                    'class' => ['gridview', 'table-responsive'],
                ],
                'tableOptions' => [
                    'class' => ['table', 'text-nowrap', 'table-striped', 'table-bordered', 'mb-0'],
                ],
                'dataProvider' => $dataProvider,
                'filterModel' => $searchModel,
                'rowOptions' => function (
                    $model,
                    $key,
                    $index,
                    $grid
                ) {
                    return [
                        'id' => $key,
                        'ondblclick' => 'location.href="'
                            . Url::to(['out-view'])
                            . '?id="+(this.id);',
                    ];
                },
                'columns' => [
                    ['class' => 'yii\grid\SerialColumn'],
                    [
                        'label' => t("Company Name"),
                        'attribute' => 'company_title',
                        'value' => function ($model) {
                            return Html::a($model->company ? $model->company->title :"",['bank-transaction/out-view','id'=>$model->id]);

                        },
                        'filter' => true,
                        'format' => 'raw',
                    ],

                    'company_tin',
                    [
                        'attribute' => 'amount',
                        'value' => function($model){
                            return number_format($model->amount / 100);
                        }
                    ],
                    'created_at',
                    'state',
                    'type',
                    'payment_status',

                ],
            ]); ?>

        </div>
        <div class="card-footer">
            <?php echo getDataProviderSummary($dataProvider) ?>
        </div>
    </div>

</div>
