<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderActionRequestCommissionVoteResource;
use api\modules\tender\resources\TenderActionRequestResource;
use common\enums\TenderEnum;
use Yii;

class TenderActionRequestVoteForm extends BaseRequest
{

    public TenderActionRequestResource $model;

    public $vote;
    public $description;

    public function __construct(TenderActionRequestResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            ['vote', 'required', 'message' => t('{attribute} yuborish majburiy')],
            ['vote', 'integer'],
            ['vote', 'checkVote'],
            ['description', 'required', 'when' => function ($model) {
                return $model->vote === TenderEnum::VOTE_NO || $model->vote === "0";
            }],
            ['description', 'string', 'max' => 255]
        ];
    }

    public function checkVote()
    {
        if (in_array($this->vote, [TenderEnum::VOTE_NO, TenderEnum::VOTE_YES, TenderEnum::VOTE_NEUTRAL])) {
            return true;
        } else {
            $this->addError("vote", t("Ovoz berish qiymatlari 1 yoki 0 bo'lishi kerak"));
            return false;
        }
    }

    public function getResult()
    {

        if ($this->model->status == TenderEnum::TENDER_ACTION_STATUS_NEW || $this->model->status == TenderEnum::TENDER_ACTION_STATUS_READY_FOR_PROTOCOL) {

            $commissionId = \Yii::$app->user->identity->commissionMemberId;
            $tender = $this->model->tender;
            $tenderCom = $tender->getCommissionMember($commissionId);
            if (!in_array($tenderCom->role, [TenderEnum::ROLE_COMMISSION, TenderEnum::ROLE_CHAIRMAN])) {
                $this->addError("error", t("Komisiya yoki rais roli uchun ruxsat mavjud"));
                return false;
            }


            if ($this->model->getCommissionVote($commissionId)) {
                $this->addError('vote', t("Avval bu tender uchun ovoz bergansiz"));
                return false;
            }

            $transaction = \Yii::$app->db->beginTransaction();
            $tenderVoting = new TenderActionRequestCommissionVoteResource();

            $tenderVoting->tender_id = $tender->id;
            $tenderVoting->commission_member_id = $commissionId;
            $tenderVoting->tender_action_request_id = $this->model->id;
            $tenderVoting->role = $tenderCom->role;
            $tenderVoting->vote = $this->vote;
            $tenderVoting->description = $this->description;

            if ($tenderVoting->save()) {

                $memberCount = $tender->getTenderCommissionMembersCount();
                $countVotes = $this->model->getCommissionVoteCount();

                if ((ceil(($memberCount - 1) * 2 / 3)) <= $countVotes) {
                    $this->model->status = TenderEnum::TENDER_ACTION_STATUS_READY_FOR_PROTOCOL;
                    if (!$this->model->save()) {
                        $transaction->rollBack();
                        $this->addErrors($this->model->errors);
                        return false;
                    }
                }
                $transaction->commit();
                return true;
            } else {
                $this->addErrors($tenderVoting->errors);
                return false;
            }
        } else {
            $this->addError("error", t("Ovoz berish tugadi"));
            return false;
        }
    }
}