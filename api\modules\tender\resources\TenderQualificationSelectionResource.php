<?php


namespace api\modules\tender\resources;


use api\modules\common\resources\FileResource;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderQualificationSelection;

class TenderQualificationSelectionResource extends TenderQualificationSelection
{

    public function fields()
    {
        return [
            'id',
            'tender_request_id',
            'tender_id',
            'company_id',
            'file',
            'title',
            'status',
            'state',
        ];
    }

    public function getFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'file_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

}