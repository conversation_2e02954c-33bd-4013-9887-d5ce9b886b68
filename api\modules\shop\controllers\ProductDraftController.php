<?php

namespace api\modules\shop\controllers;

use api\components\ApiController;
use api\modules\shop\filters\ProductDraftDetailFilter;
use api\modules\shop\filters\ProductDraftListFilter;
use api\modules\shop\forms\ProductDraftCancelForm;
use api\modules\shop\forms\ProductDraftDeleteForm;
use api\modules\shop\forms\ProductDraftForm;
use api\modules\shop\forms\ProductDraftUpdateForm;
use api\modules\shop\resources\ProductDraftResource;
use common\behaviors\RoleAccessBehavior;
use Yii;

class ProductDraftController extends ApiController
{
    public function behaviors()
    {
        $parent = parent::behaviors();
        $parent['accessByRole'] = [
            'class' => RoleAccessBehavior::class,
            'rules' => [
                'index' => ['user'],
                'product-draft-detail' => ['user'],
                'product-draft-view' => ['user'],
                'create' => ['user'],
                'update' => ['user'],
                'delete' => ['user'],
                'cancel' => ['user'],
            ],
        ];
        return $parent;
    }

    public function actionIndex()
    {
        return $this->sendResponse(
            new ProductDraftListFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionProductDraftDetail($id)
    {
        return $this->sendResponse(
            new ProductDraftDetailFilter($this->findOne($id)),
            Yii::$app->request->queryParams
        );
    }

    public function actionProductDraftView($id)
    {
        return $this->sendModel($this->findOne($id));
    }

    private function findOne($id)
    {
        $model = ProductDraftResource::findOrFail($id);
        return $model;
    }

    public function actionCreate()
    {
        return $this->sendResponse(
            new ProductDraftForm(new ProductDraftResource()),
            Yii::$app->request->bodyParams
        );
    }

    public function actionUpdate($id)
    {
        return $this->sendResponse(
            new ProductDraftUpdateForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionDelete($id)
    {
        return $this->sendResponse(
            new ProductDraftDeleteForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionCancel($id)
    {
        return $this->sendResponse(
            new ProductDraftCancelForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }
}