<?php


namespace api\modules\shop\controllers;


use api\components\ApiController;
use api\modules\shop\forms\ProductDraftForm;
use api\modules\shop\resources\ProductDraftResource;
use Yii;

class ProductDraftController extends ApiController
{
    public function actionCreate()
    {
        return $this->sendResponse(
            new ProductDraftForm(new ProductDraftResource()),
            Yii::$app->request->bodyParams
        );
    }

    public function actionView($id)
    {
        return $this->sendModel($this->findOne($id));
    }

    private function findOne($id)
    {
        $model = ProductDraftResource::findOne(['id' => $id , 'deleted_at' => null]);

        return $model;
    }
}