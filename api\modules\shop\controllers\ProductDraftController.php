<?php


namespace api\modules\shop\controllers;


use api\components\ApiController;
use api\modules\shop\forms\ProductDraftForm;
use api\modules\shop\forms\ProductDraftUpdateForm;
use api\modules\shop\forms\SendToModerationForm;
use api\modules\shop\resources\ProductDraftResource;
use Yii;
use yii\web\NotFoundHttpException;

class ProductDraftController extends ApiController
{
    public function actionCreate()
    {
        return $this->sendResponse(
            new ProductDraftForm(new ProductDraftResource()),
            Yii::$app->request->bodyParams
        );
    }

    public function actionUpdate($id)
    {
        return $this->sendResponse(
            new ProductDraftUpdateForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionView($id,$status = null)
    {
        return $this->sendModel($this->findOne($id, $status));
    }

    public function actionSendToModeration($id)
    {
        return $this->sendResponse(
            new SendToModerationForm($this->findOne($id)),
            Yii::$app->request->queryParams
        );
    }

    private function findOne($id)
    {
        $model = ProductDraftResource::findOrFail($id);

        return $model;
    }
}