<?php


namespace api\modules\shop\controllers;


use api\components\ApiController;
use api\modules\shop\forms\ProductDraftForm;
use api\modules\shop\forms\SendToModerationForm;
use api\modules\shop\resources\ProductDraftResource;
use Yii;
use yii\web\NotFoundHttpException;

class ProductDraftController extends ApiController
{
    public function actionCreate()
    {
        return $this->sendResponse(
            new ProductDraftForm(new ProductDraftResource()),
            Yii::$app->request->bodyParams
        );
    }

    public function actionView($id)
    {
        return $this->sendModel($this->findOne($id));
    }

    public function actionSendToModeration($id)
    {
        return $this->sendResponse(
            new SendToModerationForm($this->findOne($id)),
            Yii::$app->request->queryParams
        );
    }

    private function findOne($id)
    {
        $model = ProductDraftResource::findOne(['id' => $id , 'deleted_at' => null]);

        if (!$model)
            throw new NotFoundHttpException("Product not found");

        return $model;
    }


}