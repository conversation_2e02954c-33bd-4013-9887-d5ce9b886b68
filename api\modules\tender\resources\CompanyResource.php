<?php


namespace api\modules\tender\resources;


use common\models\Company;
use common\models\query\NotDeletedFromCompanyQuery;

class CompanyResource extends Company
{
    public function fields()
    {
        return [
            'id',
            'tin',
            'pinfl',
            'title',
            'address'
        ];
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

}