<?php


namespace backend\modules\admin\filters;


use api\components\BaseRequest;
use backend\modules\admin\resources\BankAccountResource;
use common\enums\OperationTypeEnum;

class BankAccountFilter extends BaseRequest
{
    public function getResult()
    {
        $model = BankAccountResource::find()->where(['parent_id' => null , 'prefix_account' => OperationTypeEnum::P_K_30901]);
        $model->orderBy(['id' => SORT_DESC]);
        return paginate($model);
    }
}