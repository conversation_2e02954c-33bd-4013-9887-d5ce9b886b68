<?php

namespace backend\controllers;

use common\enums\AuctionEnum;
use common\enums\ShopEnum;
use common\enums\TenderEnum;
use common\models\auction\Auction;
use common\models\shop\Order;
use common\models\Tender;
use common\models\User;
use Yii;

/**
 * Site controller
 */
class SiteController extends BackendController
{
    /**
     * Displays the index (home) page.
     * Use it in case your home page contains static content.
     *
     * @return string
     */
    public function actionIndex()
    {
        $users = User::find()->count();
        $orders = Order::find()->andWhere(['status'=>ShopEnum::STATUS_ACTIVE])->count();
        $auctions = Auction::find()->andWhere(['status'=>AuctionEnum::STATUS_ACTIVE])->count();
        $tenders = Tender::find()->andWhere(['status'=>TenderEnum::STATUS_ACTIVE])->count();

        return $this->render('index',[
          'user'=>$users,
          'orders'=>$orders,
          'auctions'=>$auctions,
          'tenders'=>$tenders,
        ]);
    }
//    public function beforeAction($action)
//    {
//        $this->layout = Yii::$app->user->isGuest || !Yii::$app->user->can('loginToBackend') ? 'base' : 'common';
//
//        return parent::beforeAction($action);
//    }

    public function actionError()
    {
        $this->layout = 'blank';
        $exception = Yii::$app->errorHandler->exception;

        if ($exception !== null) {
            return $this->render('error', [
                'name' => $exception->getName(),
                'message' => $exception->getMessage(),
                'exception' => $exception,
            ]);
        }

        // fallback: agar exception null bo‘lsa
        return $this->render('error', [
            'name' => Yii::t('yii', 'Error'),
            'message' => Yii::t('yii', 'An internal server error occurred.'),
            'exception' => new \Exception('Unknown error'),
        ]);
    }
}
