<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\shop\resources\FavoriteResource;
use common\models\shop\Product;

class FavoriteForm extends BaseRequest
{

    public FavoriteResource $model;

    public $product_id;


    public function __construct(FavoriteResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [ ['product_id'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],

        ];
    }


    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();

        $companyId = \Yii::$app->user->identity->company_id;

        $oldFavourite = FavoriteResource::find()->andWhere(['company_id'=>$companyId])->andWhere(['product_id'=>$this->product_id])->exists();

        if ($oldFavourite){
            return true;
        }
        $this->model->company_id = $companyId;
        $this->model->user_id = \Yii::$app->user->id;
        $this->model->created_at = date("Y-m-d H:i:s");
        $att = $this->attributes;
        $this->model->setAttributes($att,false);
        if($this->model->attributes && $this->model->validate() && $this->model->save()){
            $transaction->commit();
            return true;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}