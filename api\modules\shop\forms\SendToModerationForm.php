<?php

namespace api\modules\shop\forms;

use api\components\BaseRequest;
use api\modules\shop\resources\ProductDraftResource;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;
use common\models\shop\ProductFile;
use common\models\shop\ProductRegion;
use Yii;

class SendToModerationForm extends BaseRequest
{
    public ProductDraftResource $model;

    public function __construct(ProductDraftResource $model, $params = [])
    {
        $this->model = $model;
        parent::__construct($params);
    }

    public function getResult()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $product = new ProductResource();

            $product->classifier_category_id = $this->model->classifier_category_id;
            $product->classifier_id = $this->model->classifier_id;
            $product->company_id = $this->model->company_id;
            $product->title = $this->model->title;
            $product->brand_title = $this->model->brand_title;
            $product->description = $this->model->description;
            $product->year = $this->model->year;
            $product->quantity = $this->model->quantity;
            $product->unit_id = $this->model->unit_id;
            $product->price = $this->model->price;
            $product->min_order = $this->model->min_order;
            $product->max_order = $this->model->max_order;
            $product->type = $this->model->type;
            $product->country_id = $this->model->country_id;
            $product->unit_price = $this->model->unit_price;
            $product->made_in = $this->model->made_in;
            $product->platform_display = $this->model->platform_display;
            $product->delivery_period = $this->model->delivery_period;
            $product->delivery_period_type = $this->model->delivery_period_type;
            $product->warranty_period = $this->model->warranty_period;
            $product->warranty_period_type = $this->model->warranty_period_type;
            $product->expiry_period = $this->model->expiry_period;
            $product->expiry_period_type = $this->model->expiry_period_type;
            $product->account_number = $this->model->account_number;

            $product->state = ProductEnum::SHOP_STATE_NEW;
            $product->status = ProductEnum::STATUS_NEW;
            $product->created_by = Yii::$app->user->id;
            $product->updated_by = Yii::$app->user->id;

            if ($product->save()) {
                $this->copyFilesToProduct($product->id);
                $this->copyRegionsToProduct($product->id);

                $this->model->deleted_at = date('Y-m-d H:i:s');
                $this->model->updated_by = Yii::$app->user->id;
                $this->model->save(false);

                $transaction->commit();
                return ['product_id' => $product->id];
            } else {
                $transaction->rollBack();
                $this->addErrors($product->errors);
                return false;
            }
        } catch (\Exception $e) {
            $transaction->rollBack();
            $this->addError('general', $e->getMessage());
            return false;
        }
    }

    private function copyFilesToProduct($productId)
    {
        $draftFiles = $this->model->productFiles; // ProductDraft files

        foreach ($draftFiles as $draftFile) {
            $productFile = new ProductFile();
            $productFile->product_id = $productId;
            $productFile->file_id = $draftFile->file_id;
            $productFile->type = $draftFile->type;
            $productFile->created_by = Yii::$app->user->id;
            $productFile->save();
        }

        $draftImages = $this->model->productImages; // ProductDraft images

        foreach ($draftImages as $draftImage) {
            $productFile = new ProductFile();
            $productFile->product_id = $productId;
            $productFile->file_id = $draftImage->file_id;
            $productFile->type = $draftImage->type;
            $productFile->created_by = Yii::$app->user->id;
            $productFile->save();
        }
    }

    private function copyRegionsToProduct($productId)
    {
        $draftRegions = $this->model->productRegions; // ProductDraft regions

        foreach ($draftRegions as $draftRegion) {
            $productRegion = new ProductRegion();
            $productRegion->product_id = $productId;
            $productRegion->region_id = $draftRegion->region_id;
            $productRegion->created_by = Yii::$app->user->id;
            $productRegion->save();
        }
    }
}