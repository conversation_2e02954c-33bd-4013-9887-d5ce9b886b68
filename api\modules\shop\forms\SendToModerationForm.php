<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\shop\resources\ProductDraftResource;
use api\modules\shop\resources\ProductResource;

class SendToModerationForm extends BaseRequest
{
    public $id;

    public ProductDraftResource $model;

    public function rules()
    {
        return [
            [
                ['id'], 'required', 'message' => t('{attribute} yuborish majburiy')
            ],
        ];
    }

    public function __construct(ProductDraftResource $model , $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function getResult()
    {
        $product = new ProductResource();
        $product->load($this->model);
        dd(var_dump($product->load($this->model->attributes)));
    }
}