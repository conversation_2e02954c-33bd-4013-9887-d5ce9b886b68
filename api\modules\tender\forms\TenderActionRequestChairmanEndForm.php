<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\common\resources\FileResource;
use api\modules\tender\resources\TenderActionRequestResource;
use api\modules\tender\resources\TenderResource;
use common\enums\StatusEnum;
use common\enums\TenderEnum;
use kartik\mpdf\Pdf;

class TenderActionRequestChairmanEndForm extends BaseRequest
{

    public TenderActionRequestResource $model;

    public function __construct(TenderActionRequestResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function getResult(): bool
    {

        if ($this->model->status == TenderEnum::TENDER_ACTION_STATUS_READY_FOR_PROTOCOL) {

            $commissionId = \Yii::$app->user->identity->commissionMemberId;
            $tender = $this->model->tender;
            /**
             * @var $tender TenderResource
             */
            $tenderCom = $tender->getCommissionMember($commissionId);
            if ($tenderCom->role != TenderEnum::ROLE_CHAIRMAN) {
                $this->addError("error", t("Faqat rais roli uchun ruxsat mavjud"));
                return false;
            }

            $voteNoCount = $this->model->getCommissionVoteByVoteCount(TenderEnum::VOTE_NO);
            $voteCountYes = $this->model->getCommissionVoteByVoteCount(TenderEnum::VOTE_YES);
            $transaction = \Yii::$app->db->beginTransaction();
            if ($voteCountYes == $voteNoCount) {

                $chairmanVote = $this->model->getCommissionVoteChairmanCount();
                if ($chairmanVote) {
                    if ($chairmanVote == TenderEnum::VOTE_YES) {
                        $this->model->status = TenderEnum::TENDER_ACTION_STATUS_READY;
                    } else {
                        $this->model->status = TenderEnum::TENDER_ACTION_STATUS_CANCEL;
                    }
                } else {
                    $transaction->rollBack();
                    $this->addError("error", t("Rais ovoz bermagan"));
                    return false;
                }

            } else if ($voteCountYes > $voteNoCount) {
                $this->model->status = TenderEnum::TENDER_ACTION_STATUS_READY;
            } else {
                $this->model->status = TenderEnum::TENDER_ACTION_STATUS_CANCEL;
            }

            if (!$this->model->save()) {
                $transaction->rollBack();
                $this->addErrors($this->model->errors);
                return false;
            }
            // TODO must be changed.
//            $fileSave = $this->makeProtocol($this->model);

//            if ($fileSave == true) {
                $transaction->commit();
                return true;
//            } else {
//                $transaction->rollBack();
//                $this->addError("error", $fileSave);
//                return false;
//            }

        } else {
            $this->addError("error", t("Protokol shakllantirish xolatida emas, ovoz bermaganlar mavjud"));
            return false;
        }
    }

    //TODO TenderActionRequestResult xal qilish kerak, savol
    private function makeProtocol(TenderActionRequestResource $model)
    {
        $path_to_email_template = '@api/modules/shop/files/new_shop_contract2.php';
        $content = \Yii::$app->view->renderFile($path_to_email_template, ['model' => $model]);

        $pdf = new Pdf(['mode' => Pdf::MODE_UTF8,
            'format' => Pdf::FORMAT_A4,
            'orientation' => Pdf::ORIENT_PORTRAIT,
            'destination' => Pdf::DEST_BROWSER,
            'content' => $content,
            'cssFile' => '@api/modules/shop/files/css/pdf.css',
        ]);

        $currentDate = date('Y-m-d');

        $folderPath = \Yii::getAlias('@storage') . '/web/source/' . $currentDate;
        if (!is_dir($folderPath)) {
            mkdir($folderPath, 0777, true);
        }

        $fayl_filename = '/' . str_replace('.pdf', '', 'tender_protocol_file') . '_' . (int)microtime(true) . '.pdf';

        $pdf->output($content, $folderPath . $fayl_filename, 'F');

        $file = new FileResource();

        $file->path = $fayl_filename;
        $file->title = 'tender_protocol_file_' . $model->id;
        $file->size = 1234;
        $file->type = 'pdf';
        $file->day = $currentDate;
        $file->status = StatusEnum::STATUS_ACTIVE;

        if (!$file->save()) {
            unlink($folderPath . $fayl_filename);
            return $file->errors;
        }
        $model->updateAttributes(['file_id' => $file->id]);
        return true;
    }
}