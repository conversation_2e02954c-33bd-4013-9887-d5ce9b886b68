<?php


namespace api\modules\auction\forms;


use api\components\BaseRequest;
use api\modules\auction\resources\AuctionDetailResource;
use common\enums\AuctionEnum;
use common\enums\CompanyTransactionEnum;
use common\models\auction\AuctionHistory;
use common\models\Company;
use common\models\CompanyTransaction;
use common\models\WorkdayCalendar;
use Yii;
use yii\base\Exception;
use yii\helpers\ArrayHelper;

class AuctionReloadForm extends BaseRequest
{
    public AuctionDetailResource $model;
    public $pkcs7;

    public function __construct(AuctionDetailResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function getResult()
    {
        /**
         * @var $company Company
         */
        if ($this->model->status != AuctionEnum::STATUS_NOT_HELD) {
            $this->addError("id", t("Auksion qayta e'lon qilish uchun muqobil xolatda emas"));
            return false;
        }
        $model = $this->model;
        $user = Yii::$app->user->identity;

        $holidays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::HOLIDAY]), 'local_date', 'local_date');
        $workDays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::WORKDAY]), 'local_date', 'local_date');

        $endDate = $model->auction_end;
        $endDate = addDaysExcludingWeekends($endDate, 3, $workDays, $holidays);
        $endTime = strtotime($endDate);
        $time = time();
        if ($time > $endTime) {
            $this->addError("id", t("3 ish kunigacha qayta e'lon qilish mumkin. Vaqt o'tib ketdi"));
            return false;
        }
        if($model->age > 1){
            $this->addError("error", t("Auksion 1 marta qayta o'tkaziladi"));
            return false;
        }

        $transaction = \Yii::$app->db->beginTransaction();

        $zalog_sum = 0;
        if (!$user->isBudget) {
            $zalog_sum = $model->total_sum * env('ZALOG_PERCENT', 0.03);

        }

        $commission_sum = $model->total_sum * env('COMMISSION_PERCENT', 0.0015);
        $commission_sum = $commission_sum > 1000000 ? 1000000 : $commission_sum;
        $total_block_sum = $zalog_sum + $commission_sum;
        $company = $model->company;
        /**
         * @var $company Company
         */


        if ($company->availableBalance < $total_block_sum) {
            $this->addError('error', t("Balansda yetarli mablag' mavjud emas"));
        }

        if ($zalog_sum > 0) {
            $company_transaction_zalog = new CompanyTransaction([
                'company_id' => $company->id,
                'auction_id' => $model->id,
                'amount' => $zalog_sum,
                'type' => CompanyTransactionEnum::TYPE_ZALOG,
                'description' => Yii::t("main", "Auksion uchun garov bandlandi"),
                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                'transaction_date' => date("Y-m-d H:i:s"),
            ]);

            if (!$company_transaction_zalog->save()) {
                $this->addError('error', t("Zalog summa bandlashda xatolik"));
            }
        }


        $company_transaction_commission = new CompanyTransaction([
            'company_id' => $company->id,
            'auction_id' => $model->id,
            'amount' => $commission_sum,
            'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION,
            'description' => Yii::t("main", "Auksion uchun kommisiya"),
            'status' => CompanyTransactionEnum::STATUS_SUCCESS,
            'transaction_date' => date("Y-m-d H:i:s"),
        ]);

        if (!$company_transaction_commission->save()) {
            throw new Exception(t("Komisiya summa bandlashda xatolik"));
        }

        $model->status = AuctionEnum::STATUS_ACTIVE;
        $model->auction_end = date("Y-m-d H:i:s", strtotime("+15 minutes"));


        if ($model->save()) {

            $history = new AuctionHistory();
            $history->auction_id = $model->id;
            $history->user_id = Yii::$app->user->id;
            $history->status = $model->status;
            $history->comment = t("Amalga oshmagan lotni qayta savdoga chiqarildi");
            $history->created_at = date("Y-m-d H:i:s");
            if (!$history->save()) {
                $transaction->rollBack();
                throw new Exception(t("Auksion tarixini saqlashda xatolik"));
            }

            $transaction->commit();
            return true;
        } else {
            $this->addErrors($model->errors);
        }

        $transaction->rollBack();
        return false;
    }

}