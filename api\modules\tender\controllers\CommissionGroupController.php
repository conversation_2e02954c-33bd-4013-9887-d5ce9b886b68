<?php

namespace api\modules\tender\controllers;

use api\components\ApiController;
use api\modules\tender\filters\CommissionGroupFilter;
use api\modules\tender\forms\CommissionGroupDeleteForm;
use api\modules\tender\forms\CommissionGroupForm;
use api\modules\tender\forms\CommissionGroupMemberDeleteForm;
use api\modules\tender\forms\CommissionGroupMemberForm;
use api\modules\tender\forms\CommissionGroupUpdateForm;
use api\modules\tender\resources\CommissionGroupResource;
use api\modules\tender\resources\CommissionGroupMemberResource;
use Yii;
use yii\web\NotFoundHttpException;

/**
 * Group controller for the `tender` module
 */
class CommissionGroupController extends ApiController
{
    public function actionIndex()
    {
        return $this->sendResponse(
            new CommissionGroupFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionView($id)
    {
        return $this->sendModel($this->findOne($id));
    }

    public function actionCreate(){
        return $this->sendResponse(
            new CommissionGroupForm(new CommissionGroupResource()),
            Yii::$app->request->bodyParams
        );
    }

    public function actionUpdate($id){
        return $this->sendResponse(
            new CommissionGroupUpdateForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }

    public function actionAddMember(){

        $model = new CommissionGroupMemberForm(new CommissionGroupMemberResource());
        return $this->sendResponse(
            $model,
            Yii::$app->request->bodyParams
        );
    }

    public function actionRemoveMember(){
        return $this->sendResponse(
            new CommissionGroupMemberDeleteForm(),
            Yii::$app->request->bodyParams
        );
    }

    public function actionDelete($id){
        return $this->sendResponse(
            new CommissionGroupDeleteForm($this->findOne($id)),
            Yii::$app->request->queryParams
        );
    }


    private function findOne($id) {
        $model = CommissionGroupResource::find()->where(['id' => $id])->one();

        if (!$model) throw new NotFoundHttpException("Group not found");

        return $model;
    }
}
