<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\UnitResource;
use common\models\Unit;

class UnitFilter extends BaseRequest
{
    public $title;

    public function rules()
    {
        return [
            ['title', 'safe'],
        ];
    }

    public function getResult()
    {
        $query = UnitResource::find()->where(['type' => Unit::TYPE_DEFAULT]);

        if ($this->title) {
            $query->andWhere(['like', 'title', $this->title]);
        }

        return $query->all();
    }
}
