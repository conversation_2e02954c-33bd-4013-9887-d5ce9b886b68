<?php


/**
 * @var array $answers
 */
?>
<?php if (is_array($answers) && !empty($answers)): ?>
    <?php foreach ($answers as $key => $answer): ?>
        <?php if (!empty($answer['requirement'])): ?>
            <p class="MsoNormal" style='margin-left:-.25pt'>
                По критерию <?= '"' . $answer['requirement'] . '"' ?>, следующие участники получили следующие баллы:
            </p>
        <?php endif; ?>

        <?php if (is_array($answer['requests']) && !empty($answer['requests'])): ?>
            <table style="border-collapse: collapse;border: none;width: 400pt;">
                <thead>
                <tr>
                    <th style="width: 200pt;border: none;padding: 0;height: 20.95pt;vertical-align: middle; text-align: center">
                        <p style='margin-top:5.7pt;margin-right:0;margin-bottom:.0001pt;margin-left:18.8pt;text-align:left;font-family:"Trebuchet MS","sans-serif";'>
                             Участник
                        </p>
                    </th>
                    <th style="width: 80pt;border: none;padding: 0;height: 20.95pt;vertical-align: middle; text-align: center">
                        <p style='margin-top:5.7pt;margin-right:0;margin-bottom:.0001pt;margin-left:.5pt;text-align:center;font-family:"Trebuchet MS","sans-serif";'>
                            Балл
                        </p>
                    </th>
                    <th style="width: 80pt;border: none;padding: 0;height: 20.95pt;vertical-align: middle; text-align: center">
                        <p style='margin-top:5.7pt;margin-right:0;margin-bottom:.0001pt;margin-left:.5pt;text-align:center;font-family:"Trebuchet MS","sans-serif";'>
                            Оценка
                        </p>
                    </th>
                    <th style="width: 80pt;border: none;padding: 0;height: 20.95pt;vertical-align: middle; text-align: center">
                        <p style='margin-top:5.7pt;margin-right:0;margin-bottom:.0001pt;margin-left:.5pt;text-align:center;font-family:"Trebuchet MS","sans-serif";'>
                            Комментарий
                        </p>
                    </th>
                </tr>
                </thead>
                <tbody>
                <?php foreach ($answer['requests'] as $idx => $request): ?>
                    <tr>
                        <td style="width: 200pt;border: none;padding: 0;height: 20.95pt;vertical-align: middle; text-align: center">
                            <p style='margin-top:5.7pt;margin-right:0;margin-bottom:.0001pt;margin-left:18.8pt;text-align:left;font-family:"Trebuchet MS","sans-serif";'>
                                <?= $request['company'] ?>
                            </p>
                        </td>
                        <td style="width: 80pt;border: none;padding: 1mm;height: 20.95pt;vertical-align: middle; text-align: center">
                            <p style='margin-top:5.7pt;margin-right:0;margin-bottom:.0001pt;margin-left:.5pt;text-align:center;font-family:"Trebuchet MS","sans-serif";'>
                                <?= $request['score'] ?>
                            </p>
                        </td>
                        <td style="width: 80pt;border: none;padding: 1mm;height: 20.95pt;vertical-align: middle; text-align: center">
                            <p style='margin-top:5.7pt;margin-right:0;margin-bottom:.0001pt;margin-left:.5pt;text-align:center;font-family:"Trebuchet MS","sans-serif";'>
                                <?= $request['grade'] ?>
                            </p>
                        </td>
                        <td style="width: 80pt;border: none;padding: 0;height: 20.95pt;vertical-align: middle; text-align: center">
                            <p style='margin-top:5.7pt;margin-right:0;margin-bottom:.0001pt;margin-left:.5pt;text-align:center;font-family:"Trebuchet MS","sans-serif";'>
                                <?= $request['description'] ?>
                            </p>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    <?php endforeach; ?>
<?php endif; ?>


