<?php

namespace backend\controllers;

use common\models\File;
use Yii;
use common\models\News;
use common\models\NewsSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\web\UploadedFile;

/**
 * NewsController implements the CRUD actions for News model.
 */
class NewsController extends BackendController
{
    /**
     * Lists all News models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new NewsSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single News model.
     * @param int $id ID
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new News model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new News();

        if ($model->load(Yii::$app->request->post()) ) {
            $photo = UploadedFile::getInstanceByName( 'News[photo]');

           if ($photo){
               $fayl_filename = '/source/' . str_replace('.' . $photo->extension, '', $photo->name) . '_' . (int)microtime(true) . '.' . $photo->extension;
               $photo->saveAs(\Yii::getAlias('@storage') . '/web' . $fayl_filename);
               $file = new File();
               $file->path = $fayl_filename;
               $file->title = $photo->name;
               $file->size = $photo->size;
               $file->type = $photo->extension;
             if ($file->save()){
                 $model->file_id = $file->id;
             }
           }
             $model->save();
            return $this->redirect(['view', 'id' => $model->id]);
        }
        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing News model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param int $id ID
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) ) {
            $photo = UploadedFile::getInstanceByName( 'News[photo]');

            if ($photo){
                $fayl_filename = '/source/' . str_replace('.' . $photo->extension, '', $photo->name) . '_' . (int)microtime(true) . '.' . $photo->extension;
                $photo->saveAs(\Yii::getAlias('@storage') . '/web' . $fayl_filename);
                $file = new File();
                $file->path = $fayl_filename;
                $file->title = $photo->name;
                $file->size = $photo->size;
                $file->type = $photo->extension;
                if ($file->save()){
                    $model->file_id = $file->id;
                }
            }
            $model->save();
            return $this->redirect(['view', 'id' => $model->id]);
        }
        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing News model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param int $id ID
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the News model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id ID
     * @return News the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = News::findOne($id)) !== null) {
            return $model;
        }
        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
