<?php

namespace api\modules\tender\resources;

use common\models\TenderActionRequest;
use common\models\TenderActionRequestResult;
use Yii;

class TenderActionRequestResource extends TenderActionRequest
{
    public function fields()
    {
        return [
            'id',
            'file',
            'tender_id',
            'status',
            'type',
            'created_at',
            'lot' => function ($model) {
                return $model->tender ? $model->tender->lot : null;
            },
            'commission_votes' => 'tenderActionRequestCommissionVotes',
            'i_vote' => function ($model) {
                $commissionId = Yii::$app->user->identity->commissionMemberId;
                $check = TenderActionRequestCommissionVoteResource::find()
                    ->where(['tender_action_request_id' => $model->id, 'commission_member_id' => $commissionId])
                    ->andWhere(['deleted_at' => null])
                    ->one();
                return (bool)$check;
            },
            'commission_member_role' => function ($model) {
                $tender = $model->tender;
                return $tender ? $tender->commissionMemberRole : null;
            }
        ];
    }


    /**
     * Gets query for [[TenderActionRequestCommissionVotes]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTenderActionRequestCommissionVotes()
    {
        return $this->hasMany(TenderActionRequestCommissionVoteResource::class, ['tender_action_request_id' => 'id']);
    }

    /**
     * Gets query for [[TenderActionRequestResults]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTenderActionRequestResults()
    {
        return $this->hasMany(TenderActionRequestResult::class, ['tender_action_request_id' => 'id']);
    }

    public function getFile()
    {
        return $this->hasOne(\api\modules\common\resources\FileResource::class, ['id' => 'file_id']);
    }

    public function getTender()
    {
        return $this->hasOne(TenderResource::class, ['id' => 'tender_id']);
    }

}
