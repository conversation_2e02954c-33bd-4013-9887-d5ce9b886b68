<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;

class TenderReloadForm extends BaseRequest
{

    public TenderResource $model;


    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }


    public function getResult()
    {

        //$companyId = \Yii::$app->user->identity->company_id;
        // o'chirilgan bo'lmasa
        if($this->model->status != TenderEnum::STATUS_DELETED){
            $this->addError('id', "Tender avval o'chirilgan bo'lishi kerak");
            return false;
        }

        $this->model->state = TenderEnum::STATE_NEW;
        $this->model->status= TenderEnum::STATUS_NEW;
        $this->model->deleted_at = null;

        if($this->model->save()){
            return true;
        }

        $this->addErrors($this->model->errors);
        return false;
    }
}