<?php

namespace app\modules\auction\controllers;

use common\behaviors\RoleAccessBehavior;
use yii\web\Controller;

/**
 * Default controller for the `auction` module
 */
class DefaultController extends Controller
{
    public function behaviors()
    {
        $parent = parent::behaviors();
        $parent['accessByRole'] = [
            'class' => RoleAccessBehavior::class,
            'rules' => [
                'index' => ['user'],
            ],
        ];
        return $parent;
    }
  /**
   * Returns a string
   * @return string
   */
  public function actionIndex()
  {
    return "Auction module";
  }
}
