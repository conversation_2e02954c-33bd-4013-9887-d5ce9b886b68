<?php


namespace api\modules\tender\resources;


use api\modules\backend\resources\TenderModeratorLogResource;
use api\modules\client\resources\TenderRequestResource;
use api\modules\client\resources\TenderRequirementsAnswerResource;
use api\modules\common\resources\DistrictResource;
use api\modules\common\resources\RegionResource;
use common\enums\TenderEnum;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\Tender;
use yii\web\NotFoundHttpException;

class TenderActiveLotsResource extends Tender
{
    public function fields()
    {
        return [
            'id',
            'lot',
            'title',
            'tenderTotalPrice' => function($model){
                return $model->getTenderTotalPriceBase();
            },
            'end_date',
            'region',
            'district',
        ];
    }


    public function getTenderClassifiers()
    {
        return TenderClassifierResource::find()->where('deleted_at is null')->andWhere(['tender_id' => $this->id])->andWhere('status='.TenderEnum::STATUS_ACTIVE )->all();

        //return $this->hasMany(TenderClassifierResource::class, ['tender_id' => 'id']);
    }


    public function getDistrict()
    {
        return $this->hasOne(DistrictResource::class, ['id' => 'district_id']);
    }


    public function getRegion()
    {
        return $this->hasOne(RegionResource::class, ['id' => 'region_id']);
    }
}