<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\auction\resources\AuctionResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\shop\resources\ContractCancelRequestResource;
use api\modules\shop\resources\ContractResource;
use common\enums\CompanyTransactionEnum;
use common\enums\ContractEnum;
use common\enums\StatusEnum;
use common\models\CompanyTransaction;
use common\models\shop\ContractCancelRequest;
use Yii;
use yii\httpclient\Exception;
use yii\web\HttpException;
use yii\web\NotFoundHttpException;

class CancelContractAuctionForm extends BaseRequest
{
    public $customer_id;
    public $producer_id;
    public ContractResource $model;

    public $id;

    public function rules()
    {
        return [
            [['id'],'required'],
        ];
    }

    /**
     * @throws Exception
     * @throws NotFoundHttpException
     * @throws HttpException
     * @throws \yii\db\Exception
     */
    public function getResult()
    {
        $this->model = ContractResource::findOne(['id' => $this->id, 'deleted_at' => null]);
        if (!$this->model) throw new NotFoundHttpException("Contract not found");

        $company_id = Yii::$app->user->identity->company_id;

        if ($this->model->customer_id == $company_id) {
            $this->customer_id = $company_id;
        } else if ($this->model->producer_id == $company_id) {
            $this->producer_id = $company_id;
        } else {
            throw new \yii\web\HttpException(
                403,
                t("Shartnoma sizga tegishli emas")
            );
        }

        if ($this->model->status != ContractEnum::STATUS_CANCEL_PROCESS) {
            throw new HttpException(403, t("Shartnoma bir tomonlama bekor qilish xolatida emas"));
        }


        $transaction = \Yii::$app->db->beginTransaction();

        $this->model->status = ContractEnum::STATUS_CANCEL;
        $this->model->customer_cancel_date = $this->customer_id ? date("Y-m-d H:i:s") : $this->model->customer_cancel_date;
        $this->model->producer_cancel_date = $this->producer_id ? date("Y-m-d H:i:s") : $this->model->producer_cancel_date;

        if ($this->model->attributes && $this->model->validate() && $this->model->save()) {


            $contractCancelRequest = ContractCancelRequestResource::find()
                ->where('status=' . ContractEnum::STATUS_REQUEST_NEW)
                ->andWhere(['contract_id' => $this->model->id])
                ->orderBy(['id' => SORT_DESC])->one();
            if (!$contractCancelRequest) {
                $transaction->rollBack();
                throw new Exception(t("Shartnoma bir tomonlama bekor qilish so'rovi topilmadi"));
            }

            $contractCancelRequest->status = ContractEnum::STATUS_REQUEST_DONE;
            if (!$contractCancelRequest->save()) {
                $transaction->rollBack();
                $this->addErrors($contractCancelRequest->errors);
                return false;
            }
            /**
             * @var $auction AuctionResource
             */
            $date = date("Y-m-d H:i:s");
            $auction = AuctionResource::find()->where(['id' => $this->model->auction_id])->one();
            if (!$auction) {
                throw new Exception(t("Auksion topilmadi"));
            }


            if ($contractCancelRequest && $contractCancelRequest->type != ContractCancelRequest::TYPE_WITHOUT_PENALTY) {
                //TODO jarimalarni berish

                if ($contractCancelRequest->type == ContractCancelRequest::TYPE_PRODUCER_PENALTY) {
                    $company_out_id = $this->model->producer_id;
                    $company_in_id = $this->model->customer_id;

                    $transactionOut = CompanyTransaction::find()->where(['auction_id' => $auction->id, 'type' => CompanyTransactionEnum::TYPE_ZALOG, 'status' => CompanyTransactionEnum::STATUS_SUCCESS, 'reverted_id' => null])
                        ->andWhere(['in', 'company_id', [$company_out_id]])->one();

                    $transactionOutReverted = CompanyTransaction::find()->where(['auction_id' => $auction->id, 'type' => CompanyTransactionEnum::TYPE_ZALOG, 'status' => CompanyTransactionEnum::STATUS_SUCCESS, 'reverted_id' => null])
                        ->andWhere(['in', 'company_id', [$company_out_id, $company_in_id]])->all();

                    foreach ($transactionOutReverted as $company_transaction1) {
                        $revert = new CompanyTransaction([
                            'company_id' => $company_transaction1->company_id,
                            'contract_id' => $this->model->id,
                            'auction_id' => $company_transaction1->auction_id,
                            'amount' => $company_transaction1->amount,
                            'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
                            'description' => Yii::t("main", "Garov bandlashdan chiqarildi."),
                            'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                            'transaction_date' => $date,
                        ]);
                        if ($revert->save()) {
                            $company_transaction1->reverted_id = $revert->id;
                            $company_transaction1->save(false);
                        } else {
                            $transaction->rollBack();
                            $this->addErrors($revert->errors);
                            return false;
                        }
                    }


                    $company_transaction_penalty_out = new CompanyTransaction([
                        'company_id' => $company_out_id,
                        'contract_id' => $this->model->id,
                        'auction_id' => $this->model->auction_id,
                        'amount' => $transactionOut->amount,
                        'type' => CompanyTransactionEnum::TYPE_PENALTY_OUT,
                        'description' => \Yii::t("main", "Shartnoma qabul qilmagani uchun garov summada jarima berdi"),
                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                        'transaction_date' => $date,
                    ]);

                    if (!$company_transaction_penalty_out->save()) {
                        $this->addErrors($company_transaction_penalty_out->errors);
                        $transaction->rollBack();
                        throw new \yii\web\HttpException(
                            400,
                            t("Garov uchun tranzaksiya saqlanmadi: ") . $company_transaction_penalty_out->errors
                        );
                    }

                    $company_transaction_penalty_in = new CompanyTransaction([
                        'company_id' => $company_in_id,
                        'contract_id' => $this->model->id,
                        'auction_id' => $this->model->auction_id,
                        'amount' => $company_transaction_penalty_out->amount,
                        'type' => CompanyTransactionEnum::TYPE_PENALTY_IN,
                        'description' => \Yii::t("main", "Shartnoma qabul qilinmagani uchun garov summada(jarima) qabul qilib oldi"),
                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                        'transaction_date' => $date,
                    ]);

                    if (!$company_transaction_penalty_in->save()) {
                        $this->addErrors($company_transaction_penalty_in->errors);
                        $transaction->rollBack();
                        throw new \yii\web\HttpException(
                            400,
                            t("Garov uchun tranzaksiya saqlanmadi")
                        );
                    }

                } else {
                    $company_out_id = $this->model->customer_id;
                    $company_in_id = $this->model->producer_id;

                    $transactionOut = CompanyTransaction::find()->where(['auction_id' => $auction->id, 'type' => CompanyTransactionEnum::TYPE_ZALOG, 'status' => CompanyTransactionEnum::STATUS_SUCCESS, 'reverted_id' => null])->andWhere(['in', 'company_id', [$company_out_id]])->one();

                    $transactionOutReverted = CompanyTransaction::find()->where(['auction_id' => $auction->id, 'type' => CompanyTransactionEnum::TYPE_ZALOG, 'status' => CompanyTransactionEnum::STATUS_SUCCESS, 'reverted_id' => null])
                        ->andWhere(['in', 'company_id', [$company_out_id, $company_in_id]])->all();

                    foreach ($transactionOutReverted as $company_transaction1) {
                        $revert = new CompanyTransaction([
                            'company_id' => $company_transaction1->company_id,
                            'contract_id' => $this->model->id,
                            'auction_id' => $company_transaction1->auction_id,
                            'amount' => $company_transaction1->amount,
                            'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
                            'description' => Yii::t("main", "Garov bandlashdan chiqarildi."),
                            'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                            'transaction_date' => $date,
                        ]);
                        if ($revert->save()) {
                            $company_transaction1->reverted_id = $revert->id;
                            $company_transaction1->save(false);
                        } else {
                            $transaction->rollBack();
                            $this->addErrors($revert->errors);
                            return false;
                        }
                    }

                    $company_transaction_penalty_out = new CompanyTransaction([
                        'company_id' => $company_out_id,
                        'contract_id' => $this->model->id,
                        'auction_id' => $this->model->auction_id,
                        'amount' => $transactionOut->amount,
                        'type' => CompanyTransactionEnum::TYPE_PENALTY_OUT,
                        'description' => \Yii::t("main", "Shartnoma qabul qilmagani uchun garov summada jarima berdi"),
                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                        'transaction_date' => $date,
                    ]);

                    if (!$company_transaction_penalty_out->save()) {
                        $this->addErrors($company_transaction_penalty_out->errors);
                        $transaction->rollBack();
                        throw new \yii\web\HttpException(
                            400,
                            t("Garov uchun tranzaksiya saqlanmadi: ")
                        );
                    }

                    $company_transaction_penalty_in = new CompanyTransaction([
                        'company_id' => $company_in_id,
                        'contract_id' => $this->model->id,
                        'auction_id' => $this->model->auction_id,
                        'amount' => $company_transaction_penalty_out->amount,
                        'type' => CompanyTransactionEnum::TYPE_PENALTY_IN,
                        'description' => \Yii::t("main", "Shartnoma qabul qilinmagani uchun garov summada(jarima) qabul qilib oldi"),
                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                        'transaction_date' => $date,
                    ]);

                    if (!$company_transaction_penalty_in->save()) {
                        $this->addErrors($company_transaction_penalty_in->errors);
                        $transaction->rollBack();
                        throw new \yii\web\HttpException(
                            400,
                            t("Garov uchun tranzaksiya saqlanmadi: ") . $company_transaction_penalty_in->errors
                        );
                    }
                }

            } else {
                $zalogs = CompanyTransaction::find()->where(['auction_id' => $auction->id, 'type' => CompanyTransactionEnum::TYPE_ZALOG])->andWhere(['in', 'company_id', [$this->model->customer_id, $this->model->producer_id]])->all();

                foreach ($zalogs as $company_transaction) {
                    $revert = new CompanyTransaction([
                        'company_id' => $company_transaction->company_id,
                        'contract_id' => $this->model->id,
                        'auction_id' => $company_transaction->auction_id,
                        'amount' => $company_transaction->amount,
                        'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
                        'description' => Yii::t("main", "Shartnoma rad etilgani uchun(jarimasiz) garov summa bandlashdan chiqarildi"),
                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                        'transaction_date' => date("Y-m-d H:i:s"),
                    ]);
                    if ($revert->save()) {
                        $company_transaction->reverted_id = $revert->id;
                        $company_transaction->save(false);
                    } else {
                        $transaction->rollBack();
                        $this->addErrors($revert->errors);
                        return false;
                    }
                }
            }

            //Shartnoma to'lovi customerni o'ziga qaytarildi
            $transactionContractPayment = CompanyTransaction::find()
                ->where([
                    'contract_id' => $this->model->id,
                    'type' => CompanyTransactionEnum::TYPE_BLOCK_PAYMENT_FOR_CONTRACT,
                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                    'reverted_id' => null
                ])
                ->andWhere(['company_id' => $this->model->customer_id])->all();

            foreach ($transactionContractPayment as $company_transaction1) {
                $revert = new CompanyTransaction([
                    'company_id' => $company_transaction1->company_id,
                    'contract_id' => $company_transaction1->contract_id,
                    'auction_id' => $company_transaction1->auction_id,
                    'amount' => $company_transaction1->amount,
                    'type' => CompanyTransactionEnum::TYPE_REVERT_PAYMENT_FOR_CONTRACT,
                    'description' => Yii::t("main", "Shartnoma to'lovi bandlashdan chiqarildi."),
                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                    'transaction_date' => $date,
                ]);
                if ($revert->save()) {
                    $company_transaction1->reverted_id = $revert->id;
                    if (!$company_transaction1->save()) {
                        $transaction->rollBack();
                        $this->addErrors($company_transaction1->errors);
                        return false;
                    }
                } else {
                    $transaction->rollBack();
                    $this->addErrors($revert->errors);
                    return false;
                }
            }

            // komisiya xarajatga o'tkazildi
            $comissions = CompanyTransaction::find()->where(['auction_id' => $auction->id, 'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION, 'reverted_id' => null])
                ->andWhere(['in', 'company_id', [$this->model->customer_id, $this->model->producer_id]])->all();


            foreach ($comissions as $company_transaction1) {
                $revert = new CompanyTransaction([
                    'company_id' => $company_transaction1->company_id,
                    'contract_id' => $this->model->id,
                    'auction_id' => $auction->id,
                    'amount' => $company_transaction1->amount,
                    'type' => CompanyTransactionEnum::TYPE_REVERT_BLOCK_COMMISION,
                    'description' => Yii::t("main", "Shartnoma rad etildi. Komissiya summasi blokdan chiqarildi"),
                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                    'transaction_date' => $date,
                ]);
                if ($revert->save()) {
                    $company_transaction1->reverted_id = $revert->id;
                    $company_transaction1->save(false);
                } else {
                    $transaction->rollBack();
                    $this->addErrors($revert->errors);
                    return false;
                }
            }


            foreach ($comissions as $company_transaction) {
                $cc = new CompanyTransaction([
                    'company_id' => $company_transaction->company_id,
                    'contract_id' => $this->model->id,
                    'auction_id' => $company_transaction->auction_id,
                    'amount' => $company_transaction->amount,
                    'type' => CompanyTransactionEnum::TYPE_COMMISSION,
                    'description' => Yii::t("main", "Shartnoma rad etildi. Komissiya summasi xarajatga o'tkazildi"),
                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                    'transaction_date' => $date,
                ]);
                if (!$cc->save()) {
                    $transaction->rollBack();
                    $this->addErrors($cc->errors);
                    return false;
                }
            }

            //reja grafikga qaytarish

            $auctionClassifier = $auction->auctionClassifiers;
            foreach ($auctionClassifier as $item) {
                if ($item->status != StatusEnum::STATUS_ACTIVE) {
                    continue;
                }

                $plan_schedule = PlanScheduleClassifierResource::find()
                    ->andWhere(['plan_schedule_id' => $auction->plan_schedule_id])
                    ->andWhere(['classifier_id' => $item->classifier_id])
                    ->one();
                if (!$plan_schedule) {
                    $transaction->rollBack();
                    throw new Exception(t("Reja grafik topilmadi"));
                }

                $plan_schedule->updateAttributes([
                    'count_used' => $plan_schedule->count_used - $item->quantity,
                    'count_live' => $plan_schedule->count_live + $item->quantity,
                ]);

            }

            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }

}