<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\UnitResource;
use common\models\News;

class NewsFilter extends BaseRequest
{
    public $title;

    public function rules()
    {
        return [
            ['title', 'safe'],
        ];
    }

    public function getResult()
    {
        $query = News::find()->andWhere(['status'=>1]);

        if ($this->title) {
            $query->where(['like', 'title', $this->title]);
        }

        return $query->all();
    }
}
