<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\UnitResource;
use common\models\Unit;

class UnitTenderConditionFilter extends BaseRequest
{
    public $title;

    public function rules()
    {
        return [
            ['title', 'safe'],
        ];
    }

    public function getResult()
    {
        $query = UnitResource::find()->where(['type' => Unit::TYPE_TENDER]);

        if ($this->title) {
            $query->andWhere(['like', 'title', $this->title]);
        }

        return $query->all();
    }
}
