<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%affiliates}}`.
 */
class m250716_122528_create_affiliates_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%affiliates}}', [
            'id' => $this->primaryKey(),
            'company_id' => $this->integer(),
            'company_tin' => $this->string(9),
            'pinfl' => $this->string(14),
            'full_name' => $this->string(255),
        ]);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%affiliates}}');
    }
}
