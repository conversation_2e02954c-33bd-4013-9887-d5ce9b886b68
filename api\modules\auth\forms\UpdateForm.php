<?php

namespace api\modules\auth\forms;

use api\components\BaseRequest;
use api\modules\auth\resources\UserResource;
use common\enums\CompanyEnum;
use common\enums\UserEnum;
use common\models\Company;
use common\models\CompanyBankAccount;
use common\models\EconomicActivitiesType;
use common\models\OrganizationLegalForm;
use common\models\Region;
use common\models\User;
use Exception;
use Yii;

class UpdateForm extends BaseRequest
{
    public $email;
    public $director_fullname;
    public $phone;
    public $password;
    public $organization_legal_form_id;
    public $economic_activities_type_id;
    public $confirmPassword;
    public $districtId;
    public $address;

    public function rules()
    {
        return [
            [
                [
                    'email',
                    'director_fullname',
                    'organization_legal_form_id',
                    'economic_activities_type_id',
                    'phone',
                    'districtId',
                    'address'
                ],
                'required',
            ],
            [
                [
                    'email',
                    'director_fullname',
                    'phone',
                    'password',
                    'confirmPassword',
                    'address'
                ],
                'trim',
            ],
            [
                [
                    'email',
                    'director_fullname',
                    'phone',
                    'password',
                    'confirmPassword',
                    'address',
                ],
                'string',
            ],
            [
                ['email'],
                'email',
            ],
            [
                ['password'],
                'string',
                'min' => 6,
            ],
            [
                ['password'],
                'compare',
                'compareAttribute' => 'confirmPassword',
            ],
            [
                ['phone'],
                'string',
                'min' => 9,
                'max' => 13,
            ],
            [['organization_legal_form_id'], 'exist', 'skipOnError' => true, 'targetClass' => OrganizationLegalForm::class, 'targetAttribute' => ['organization_legal_form_id' => 'id']],
            [['economic_activities_type_id'], 'exist', 'skipOnError' => true, 'targetClass' => EconomicActivitiesType::class, 'targetAttribute' => ['economic_activities_type_id' => 'id']],
        ];
    }

    public function getResult()
    {
        try {
            $user = Yii::$app->user->identity;
            $company = $user->company;

            if ($company) {


                if($company->district_id != $this->districtId){
                    $district = Region::findOne($this->districtId);
                    if($company->region_id != $district->parent_id){
                        $this->addError("error", t("Yuridik manzilni (viloyatni) o'zgartirish mumkin emas"));
                        return false;
                    }
                }

                $transaction = Yii::$app->db->beginTransaction();


                $company->phone = $this->phone;
                $company->director = $this->director_fullname;
                $company->organization_legal_form_id = $this->organization_legal_form_id;
                $company->economic_activities_type_id = $this->economic_activities_type_id;
                $company->district_id = $this->districtId;
                $company->address = $this->address;

                if (!$company->save(false)) {
                    $transaction->rollBack();
                    throw new Exception('Company save error');
                }

                $user->email = $this->email;
                if ($this->password != null) {
                    $user->setPassword($this->password);
                }

                if (!$user->save(false)) {
                    $transaction->rollBack();
                    throw new Exception('User save error');
                }
                $transaction->commit();
                return true;
            } else {
                throw new Exception(t("Korxona topilmadi"));
            }

        } catch (\Throwable $th) {
            throw $th;
        }
    }

    protected function getAccessToken()
    {
        $access_token = Yii::$app->security->generateRandomString(40);
        $this->user->access_token = $access_token;

        if (!$this->user->save(false)) {
            throw new Exception('User access token save error');
        }

        return $access_token;
    }
}
