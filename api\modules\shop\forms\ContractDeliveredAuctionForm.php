<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\shop\resources\ContractResource;
use common\enums\ContractEnum;
use common\enums\OperationTypeEnum;
use common\models\VirtualTransaction;
use yii\db\Exception;
use yii\web\NotFoundHttpException;

class ContractDeliveredAuctionForm extends BaseRequest
{

    public ?ContractResource $model;
    public $factureFile;
    public $id;


    public function rules()
    {
        return  [
            [['id'],'required'],
            [['id'],'number'],
            [['factureFile'], 'safe'],
        ];
    }


    /**
     * @throws Exception
     * @throws NotFoundHttpException
     * @throws \yii\base\Exception
     * @throws \Throwable
     */
    public function getResult()
    {
        $this->model = ContractResource::findOne(['id' => $this->id, 'deleted_at' => null]);
        if (!$this->model) throw new NotFoundHttpException("Contract not found");

        $company_id = \Yii::$app->user->identity->company_id;
        if ($this->model->customer_id != $company_id) {
            $this->addError("error", t("Shartnoma sizga tegishli emas"));
            return false;
        }
        if ($this->model->status != ContractEnum::STATUS_PAYMENT_END) {
            $this->addError("error", t("Shartnoma yakunlash uchun muqobil statusda emas"));
            return false;
        }

        $transaction = \Yii::$app->db->beginTransaction();
        $date = date("Y-m-d H:i:s");

        $this->model->status = ContractEnum::STATUS_DONE;
        $this->model->customer_mark_delivered_date = $date;
        $this->model->facture_file_id = $this->factureFile;
//        $att = $this->attributes;
//        $this->model->setAttributes($att,false); //$this->model->attributes && $this->model->validate() &&
        if ($this->model->save()) {

            //TODO zaloglarni qaytarish
//            $zalogs = CompanyTransaction::find()->where(['auction_id' => $this->model->auction_id, 'type' => CompanyTransactionEnum::TYPE_ZALOG, 'reverted_id' => null])->andWhere(['in', 'company_id', [$this->model->customer_id, $this->model->producer_id]])->all();
//
//            foreach ($zalogs as $company_transaction) {
//                $revert = new CompanyTransaction([
//                    'company_id' => $company_transaction->company_id,
//                    'contract_id' => $this->model->id,
//                    'auction_id' => $company_transaction->auction_id,
//                    'amount' => $company_transaction->amount,
//                    'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
//                    'description' => Yii::t("main", "Shartnoma to'liq tuzilganligi uchun zalog qaytarildi"),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => $date,
//                ]);
//                if ($revert->save()) {
//                    $company_transaction->reverted_id = $revert->id;
//                    $company_transaction->save(false);
//                } else {
//                    $transaction->rollBack();
//                    $this->addErrors($revert->errors);
//                    return false;
//                }
//            }
            $zalogs = VirtualTransaction::find()
                ->where([
                    'auction_id' => $this->model->auction_id,
                    'contract_id' => $this->model->id,
                    'operation_type' => OperationTypeEnum::BLOCK_TRANSACTION_DEPOSIT_AGAIN,
                    'parent_id' => null])
                ->andWhere(['in', 'credit_company_id', [$this->model->customer_id, $this->model->producer_id]])
                ->andWhere(['>','credit',0])
                ->all();
            foreach ($zalogs as $company_transaction) {
                try {
                    /** @var VirtualTransaction  $company_transaction */
                    $revertID = VirtualTransaction::saveTransaction(
                        $company_transaction->creditCompany,
                        $company_transaction->debitCompany,
                        $company_transaction->creditAccount->prefix_account,
                        $company_transaction->debitAccount->prefix_account,
                        $company_transaction->credit,
                        "Shartnoma to'liq tuzilganligi uchun zalog qaytarildi",
                        OperationTypeEnum::PRODUCT_NAME_AUCTION,
                        $this->model->auction_id,
                        $this->model->id,
                        OperationTypeEnum::CANCEL_BLOCK_TRANSACTION_DEPOSIT
                    );
                    $company_transaction->parent_id = $revertID;
                    if (!$company_transaction->save()) {
                        $transaction->rollBack();
                        $this->addErrors($company_transaction->errors);
                        return false;
                    }
                } catch (\Throwable $e) {
                    $transaction->rollBack();
                    $this->addError('error', $e->getMessage());
                    return false;
                }
            }

            // komisiya xarajatga o'tkazildi
//            $comissions = CompanyTransaction::find()->where(['auction_id' => $this->model->auction_id, 'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION, 'reverted_id' => null])
//                ->andWhere(['in', 'company_id', [$this->model->customer_id, $this->model->producer_id]])->all();
//
//
//            foreach ($comissions as $company_transaction1) {
//                $revert = new CompanyTransaction([
//                    'company_id' => $company_transaction1->company_id,
//                    'contract_id' => $this->model->id,
//                    'auction_id' => $company_transaction1->auction_id,
//                    'amount' => $company_transaction1->amount,
//                    'type' => CompanyTransactionEnum::TYPE_REVERT_BLOCK_COMMISION,
//                    'description' => Yii::t("main", "Shartnoma to'liq tuzilganligi uchun komissiya summasi blokdan chiqarildi"),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => $date,
//                ]);
//                if ($revert->save()) {
//                    $company_transaction1->reverted_id = $revert->id;
//                    $company_transaction1->save(false);
//                } else {
//                    $transaction->rollBack();
//                    $this->addErrors($revert->errors);
//                    return false;
//                }
//            }
//
//
//            foreach ($comissions as $company_transaction) {
//                $cc = new CompanyTransaction([
//                    'company_id' => $company_transaction->company_id,
//                    'contract_id' => $this->model->id,
//                    'auction_id' => $company_transaction->auction_id,
//                    'amount' => $company_transaction->amount,
//                    'type' => CompanyTransactionEnum::TYPE_COMMISSION,
//                    'description' => Yii::t("main", "Shartnoma to'liq tuzilganligi uchun komissiya xarajatga o'tdi"),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => $date,
//                ]);
//                if (!$cc->save()) {
//                    $transaction->rollBack();
//                    $this->addErrors($cc->errors);
//                    return false;
//                }
//            }

            $commission = VirtualTransaction::find()
                ->where([
                    'auction_id' => $this->model->auction_id,
                    'contract_id' => $this->model->id,
                    'parent_id' => null,])
                ->andWhere(['in', 'credit_company_id', [$this->model->customer_id, $this->model->producer_id]])
                ->andWhere(['>','credit',0])
                ->all();
            foreach ($commission as $company_transaction) {
                try {
                    /** @var VirtualTransaction  $company_transaction */
                    $debitCompany = $company_transaction->creditCompany;
                    $transferID = VirtualTransaction::saveTransaction(
                        $debitCompany,
                        _company(),
                        $debitCompany->isBudget() ? OperationTypeEnum::P_B_31501 : OperationTypeEnum::P_K_30501,
                        $debitCompany->isBudget() ? OperationTypeEnum::A_50113 : OperationTypeEnum::A_50111,
                        $company_transaction->credit,
                        "Shartnoma to'liq tuzilganligi uchun komissiya xarajatga o'tdi",
                        OperationTypeEnum::PRODUCT_NAME_AUCTION,
                        $this->model->auction_id,
                        $this->model->id,
                        OperationTypeEnum::PAY_TRANSACTION_COMMISSION
                    );
                    $company_transaction->parent_id = $transferID;
                    if (!$company_transaction->save()) {
                        $transaction->rollBack();
                        $this->addErrors($company_transaction->errors);
                        return false;
                    }
                } catch (\Throwable $e) {
                    $transaction->rollBack();
                    $this->addError('error', $e->getMessage());
                    return false;
                }
            }
            // TODO tolov qilganni otkazish

//            $rkp = CompanyTransaction::findOne([
//                'company_id' => $company_id,
//                'contract_id' => $this->model->id,
//                'type' => CompanyTransactionEnum::TYPE_BLOCK_PAYMENT_FOR_CONTRACT,
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS
//            ]);
//
//            if ($rkp) {
//                $rkp_back = new CompanyTransaction([
//                    'company_id' => $this->model->customer_id,
//                    'amount' => $rkp->amount,
//                    'auction_id' => $rkp->auction_id,
//                    'contract_id' => $this->model->id,
//                    'type' => CompanyTransactionEnum::TYPE_REVERT_PAYMENT_FOR_CONTRACT,
//                    'description' => Yii::t("main", "Yetkazib berilganligi uchun shartnoma summasi blokdan chiqarildi"),
//                    'transaction_date' => $date,
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS
//                ]);
//                if ($rkp_back->save()) {
//                    $rkp->reverted_id = $rkp_back->id;
//                    $rkp->save(false);
//
//                    $outgoingToBank = new CompanyTransaction([
//                        'company_id' => $rkp->company_id,
//                        'contract_id' => $this->model->id,
//                        'auction_id' => $rkp->auction_id,
//                        'amount' => $rkp->amount,
//                        'type' => CompanyTransactionEnum::TYPE_PAY_TO_CONTRACT,
//                        'description' => Yii::t("main", "Shartnoma summasi bank xisob raqamga chiqib ketdi"),
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'transaction_date' => $date,
//                    ]);
//
//                    if (!$outgoingToBank->save()) {
//                        $this->addErrors($outgoingToBank->errors);
//                        $transaction->rollBack();
//                        return false;
//                    }
//
//                }
//            }

            /** @var VirtualTransaction  $rkp */
            $rkp = VirtualTransaction::find()
                ->where([
                    'credit_company_id' => $company_id,
                    'contract_id' => $this->model->id,
                    'auction_id' => $this->model->auction_id,
                    'operation_type' => OperationTypeEnum::BLOCK_TRANSACTION_FULL_PAYMENT])
                ->andwhere(['>','credit',0])
                ->one();
            if (!$rkp)
            {
                $transaction->rollBack();
                throw new \Exception("Shartnoma summasi bo'yicha blokirovka qilingan mablag' topilmadi");
            }
            try {
                $transferID = VirtualTransaction::saveTransaction(
                    $rkp->creditCompany,
                    $this->model->producer,
                    OperationTypeEnum::P_K_30301,
                    OperationTypeEnum::P_K_30101,
                    $rkp->credit,
                    "Shartnoma bo'yicha to'lov to'landi.",
                    OperationTypeEnum::PRODUCT_NAME_AUCTION,
                    $this->model->auction_id,
                    $this->model->id,
                    OperationTypeEnum::PAY_FOR_DELIVERED_GOODS,
                );
                $rkp->parent_id = $transferID;
                if (!$rkp->save()) {
                    $transaction->rollBack();
                    $this->addErrors($rkp->errors);
                    return false;
                }
            } catch (\Throwable $e) {
                $transaction->rollBack();
                $this->addError('error', $e->getMessage());
                return false;
            }
            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}