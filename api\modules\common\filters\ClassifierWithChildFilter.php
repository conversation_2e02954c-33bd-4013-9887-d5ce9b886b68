<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\DistrictResource;
use api\modules\common\resources\RegionResource;
use common\models\Classifier;
use common\models\ClassifierCategory;
use yii\data\ArrayDataProvider;

class ClassifierWithChildFilter extends BaseRequest
{
    public $title;

    public function rules()
    {
        return [
            ['title', 'safe'],
        ];
    }

    public function getResult()
    {
        $data = [];
        $categories = ClassifierCategory::find();
        if ($this->title){
            $categories->leftJoin('classifier c', 'c.classifier_category_id'.'='. 'classifier_category.id');
            $categories->andWhere(['or',
                ['like', ClassifierCategory::tableName().'.title_uz', $this->title],
                ['like', ClassifierCategory::tableName().'.title_ru', $this->title],
                ['like', ClassifierCategory::tableName().'.title_en', $this->title],
                ['like', ClassifierCategory::tableName().'.title_uzk', $this->title],
                ['like', 'c.title_uz', $this->title],
                ['like',  'c.title_ru', $this->title],
                ['like',  'c.title_en', $this->title],
                ['like',  'c.title_uzk', $this->title],
            ]);
        }
        $r = 0;
        foreach ($categories->all() as $category) {
            $data[$r]['id'] = $category->id;
            $data[$r]['name'] = $category->title_uz;
            $classifier = Classifier::find()->andWhere(['classifier_category_id' => $category->id]);
            if (!$classifier->exists()) {
                continue;
            }
            $classifiers = $classifier->all();
            $b = 0;
            foreach ($classifiers as $classifier) {
                $data[$r]['options'][$b] = [
                    "value" => $classifier->id,
                    "label" =>  $classifier->title_uz
                ];
                $b++;
            }
            $r++;
        }
        return new ArrayDataProvider(['allModels' => $data]);
    }
}
