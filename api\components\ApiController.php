<?php

namespace api\components;

use api\modules\common\forms\Pkcs7Form;
use common\models\Pkcs7Log;
use Yii;
use common\behaviors\RequestLogBehavior;
use yii\base\Model;
use yii\rest\Controller;
use yii\rest\OptionsAction;

abstract class ApiController extends Controller
{

    public function behaviors()
    {
        return parent::behaviors() + [
                'corsFilter' => [
                    'class' => \yii\filters\Cors::class,
                    'cors' => [
                        // restrict access to
                        'Origin' => ['http://localhost:3000', 'http://xarid-storage.ebirja.uz', 'http://dxp.uz', 'https://dxp.uz', 'https://xarid.ebirja.uz', 'http://xarid.ebirja.uz/', 'xarid.ebirja.uz', 'ebirja.uz', 'http://dxp.uz/', 'https://dxp.uz/', 'http://tezkorxarid.uz', 'https://tezkorxarid.uz', 'http://dxp.uz/common/file/create', 'http://ebirja.uz', 'http://ebirja.uz/'],
                        // Allow only POST and PUT methods
                        'Access-Control-Request-Method' => ['GET', 'HEAD', 'POST', 'PUT'],
                        // Allow only headers 'X-Wsse'
                        'Access-Control-Request-Headers' => ['Origin', 'Content-Type', 'X-Auth-Token', 'Authorization', 'Accept', 'Referer', 'User-Agent', 'Headers'],
                        // Allow credentials (cookies, authorization headers, etc.) to be exposed to the browser
                        'Access-Control-Allow-Credentials' => true,
                        // Allow OPTIONS caching
                        'Access-Control-Max-Age' => 3600,
                        // Allow the X-Pagination-Current-Page header to be exposed to the browser.
                        // 'Access-Control-Expose-Headers' => ['Origin', 'Content-Type', 'X-Auth-Token', 'Authorization'],
                    ],
                ],
                [
                    'class' => \yii\filters\ContentNegotiator::class,
                    'languages' => ['uzk','uz', 'ru',],
                    'formats' => [
                        'application/json' => \yii\web\Response::FORMAT_JSON,
                    ],
                ],


                'bearerAuth' => [
                    'class' => \yii\filters\auth\HttpBearerAuth::class,


                    'except' => [
                        'options',
                        'login',
                        'challenge',
                        'login-pkcs7',
                        'login-pks7-ping',
                        'signup',
                        'signup-no-resident',
                        'region-list',
                        'district-list',
                        'active',
                        'active-lots',
                        'contract-pdf',
                        'active-lots-selection',
                        'economic-activities-type',
                        'organization-legal-form',
                        'frontend-timestamp',

                    ],


                    'optional' => [
                        'active-lots-view',
                        'offers',
                        'download-file',
                        'mfo-list',
                        'auction-view',
                        'announce-list',
                        'product-view',
                        'product-detail',
                        'black-list',
                        'statistics',
                        'classifier-category-list',
                        'classifier-list',
                        'country-list',
                        'list-district',
                        'get-protocol',
                        'get-protocol-not-realized',
                        'get-discussion-protocol',
                        'classifier-category-index',
                        'news-list',
                        'one-new-view',
                        'similar-news',
                        'contract-file',
                    ]
                ],
                [
                    'class' => RequestLogBehavior::class,
                ]
                //      'access' => [
                //        'class' => AccessControl::class,
                //        'rules' => [
                //           /*commission*/
                //            [
                //                'controllers' => [
                //                    'tender',
                //                ],
                //                'actions' => [
                //                    'commission-index'
                //                ],
                //
                //                'allow' => true,
                //                'roles' => ['commission'],
                //            ],
                //
                //
                //        ], // rules
                //
                //      ], // access
            ];
    }

    public $enableCsrfValidation = false;


    public function actionOptions()
    {
        return true;
    }

    public function actions()
    {
        return [
            'options' => [
                'class' => OptionsAction::class
            ]
        ];
    }

    protected function sendResponse(Model $model, $params = [])
    {
        $model->load($params, '');

        if ($model->validate()) {
            $result = $model->getResult();

            if ($result == false && !is_array($result)) {
                Yii::$app->response->statusCode = 422;
            }

            return [
                'result' => $result,
                'errors' => $model->errors
            ];
        } else {

            Yii::$app->response->statusCode = 422;

            return [
                'result' => null,
                'errors' => $model->errors,
            ];
        }
    }

    protected function sendResponsePost(Model $model, $params = [], $pksc7 = null, $pkcs_type = null)
    {
        $model->load($params, '');

        if ($model->validate()) {
            $result = $model->getResult();


            if ($result == false && !is_array($result)) {
                Yii::$app->response->statusCode = 422;
            } else {
                Pkcs7Log::create($result,$pksc7, $params,$pkcs_type);
            }

            return [
                'result' => $result,
                'errors' => $model->errors
            ];
        } else {

            Yii::$app->response->statusCode = 422;

            return [
                'result' => null,
                'errors' => $model->errors,
            ];
        }
    }

    protected function sendModel($model)
    {
        return [
            'result' => $model,
            'errors' => null
        ];
    }

    protected function verifyPkcs7($params = [])
    {
        $model = new Pkcs7Form();
        $model->load($params, '');

        if ($model->validate()) {
            return $model->getResult();
        } else {

            Yii::$app->response->statusCode = 422;

            return [
                'result' => null,
                'errors' => $model->errors,
            ];
        }
    }


    protected function sendFile(Model $model, $params = [])
    {
        $model->load($params, '');

        if ($model->validate()) {
            return $model->generateFile();
        } else {

            Yii::$app->response->statusCode = 422;

            return [
                'result' => null,
                'errors' => $model->errors,
            ];
        }
    }
}
