<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;
use common\models\CompanyBalance;
use common\models\TenderModeratorLog;
use Yii;
use yii\base\Exception;
use function foo\func;

class ModeratorAcceptForm extends BaseRequest
{

    public ProductResource $model;

    public $state;
    public $description;
    public $moderator_pinfl;


    public function __construct(ProductResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [ ['description'
            ], 'safe'],

        ];
    }



    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();

        throw new Exception("Tasdiqlash moderator adminkasi orqali");

        // Balance tekshiriladi pul yetarli bolsa savdoga chiqariladi

        $company_id = $this->model->company_id;
        $totalSum = $this->model->max_order*$this->model->unit_price*env('SHOP_ZALOG_PERSENT', 0.003);

        $companyBalance =  CompanyBalance::find()->andWhere(['company_id'=>$company_id])->one();

        if ($companyBalance && ($companyBalance->available >= $totalSum)) {
            $this->model->state = ProductEnum::SHOP_STATE_ACTIVE;
            $this->model->active_date = date('Y-m-d H:i:s');
//            $this->model->inactive_date =  addDaysExcludingWeekends(date("Y-m-d H:i:s"), 15);
            $this->model->inactive_date = date("Y-m-d H:i:s", strtotime("+15 minutes"));;
        }
        else{
            $this->model->state = ProductEnum::SHOP_STATE_NO_MONEY;
        }

        if ($this->model->save()){
            $moderatorLog = new  TenderModeratorLog();
            $moderatorLog->product_id = $this->model->id;
            $moderatorLog->description = $this->description;
            $moderatorLog->moderator_pinfl = $this->moderator_pinfl;
            $moderatorLog->state = $this->state;
            if(!($moderatorLog->validate() && $moderatorLog->save())){
                $transaction->rollBack();
                $this->addError('product_id', $moderatorLog->errors);
                return false;
            }
            $transaction->commit();
            return true;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}