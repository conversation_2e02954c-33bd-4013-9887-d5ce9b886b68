<?php

namespace api\modules\shop\resources;

use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\CompanyShortResource;
use common\models\auction\Auction;
use common\models\auction\AuctionOffer;
use yii\helpers\ArrayHelper;

class AuctionMyLotsResource extends Auction
{
    public function fields()
    {
        return [
            'id',
            'lot',
            'status',
            'total_sum',
            'auction_end',
            'requestDate',
            'classifierCategory',
            'delivery_period',
            'payment_period',
        ];
    }

    public function extraFields()
    {
        return [
            'companyShort'
        ];
    }

    public function getCompanyShort()
    {
        return $this->hasOne(CompanyShortResource::class, ['id' => 'company_id']);
    }

    public function getRequestDate()
    {
        $model = AuctionOffer::find()->where(['auction_id' => $this->id, 'company_id' => \Yii::$app->user->identity->company_id])->orderBy(['created_at' => SORT_DESC])->one();
        return $model ? $model->created_at : null;
    }

    public function getClassifierCategory()
    {
        return $this->hasOne(ClassifierCategoryResource::class, ['id' => 'classifier_category_id']);
    }


}
