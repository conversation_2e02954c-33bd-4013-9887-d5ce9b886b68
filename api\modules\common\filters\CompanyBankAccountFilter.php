<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\CompanyBankAccountResource;

class CompanyBankAccountFilter extends BaseRequest
{

    public function getResult()
    {

        $companyId = \Yii::$app->user->identity->company_id;
//        return $companyId;

        $company_balance = CompanyBankAccountResource::find()->notDeleted()->andWhere(['company_id' => $companyId, 'organ' => null]);
        return paginate($company_balance);
    }
}