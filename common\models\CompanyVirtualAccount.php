<?php

namespace common\models;

use common\components\ActiveRecordMeta;
use common\enums\OperationTypeEnum;
use common\enums\StatusEnum;
use Exception;

/**
 * This is the model class for table "company_virtual_account".
 *
 * @property int $id
 * @property int|null $company_id
 * @property string|null $organ
 * @property string|null $prefix_account
 * @property int|null $organization_type
 * @property float|null $price
 * @property string|null $account
 * @property int|null $order
 * @property int|null $status
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 *
 * @property Company $company
 * @property VirtualTransaction[] $virtualTransactions
 * @property VirtualTransaction[] $virtualTransactions0
 */
class CompanyVirtualAccount extends ActiveRecordMeta
{
    const SCENARIO_GENERATE_ACCOUNT = 'generate_account';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'company_virtual_account';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['company_id', 'prefix_account', 'organization_type', 'price', 'account', 'order', 'status', 'created_at', 'updated_at', 'deleted_at', 'created_by', 'updated_by'], 'default', 'value' => null],
            [['company_id', 'organization_type', 'order', 'status', 'created_by', 'updated_by'], 'integer'],
            [['price'], 'number'],
            ['account', 'checkAccount', 'on' => self::SCENARIO_GENERATE_ACCOUNT],
            [['created_at', 'updated_at', 'deleted_at','prefix_account'], 'safe'],
            [['account'], 'string', 'max' => 27],
            [['organ'], 'string', 'length' => 11],
            [['company_id'], 'exist', 'skipOnError' => true, 'targetClass' => Company::class, 'targetAttribute' => ['company_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'company_id' => 'Company ID',
            'prefix_account' => 'Prefix',
            'organization_type' => 'Organization Type',
            'price' => 'Price',
            'account' => 'Account',
            'order' => 'Order',
            'status' => 'Status',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'deleted_at' => 'Deleted At',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
        ];
    }

    /**
     * Gets query for [[Company]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCompany()
    {
        return $this->hasOne(Company::class, ['id' => 'company_id']);
    }

    /**
     * Gets query for [[VirtualTransactions]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getVirtualTransactions()
    {
        return $this->hasMany(VirtualTransaction::class, ['credit_account_id' => 'id']);
    }

    /**
     * Gets query for [[VirtualTransactions0]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getVirtualTransactions0()
    {
        return $this->hasMany(VirtualTransaction::class, ['debit_account_id' => 'id']);
    }


    public function checkAccount($attribute, $param)
    {
        if ($this->organization_type == OperationTypeEnum::ORGANIZATION_TYPE_CORPORATIVE && strlen($this->{$attribute}) != 22) {
            $this->addError($attribute, t("Account nomer talablarga javob bermadi."));
        } else if ($this->organization_type == OperationTypeEnum::ORGANIZATION_TYPE_BUDGET && strlen($this->{$attribute}) != 25) {
            $this->addError($attribute, t("Account nomer talablarga javob bermadi."));
        }
    }

    /**
     *  Virtual account create
     * @param Company $company
     * @param $account
     * @param int $type
     * @return bool
     * @throws Exception
     */
    public static function saveVirtualAccount(Company $company, $account, $organ = null): bool
    {
        $model = new self(['scenario' => self::SCENARIO_GENERATE_ACCOUNT]);
        $model->company_id = $company->id;
        $model->organ = $organ;
        $model->prefix_account = $account;
        $model->status = StatusEnum::STATUS_ACTIVE;
        $model->organization_type = $company->organization_type;
        $model->account = self::generateCorporativeAccountNumber($account, $company->tin, $company->pinfl);
        if (!$model->save())
        {
            throw new Exception($model->getFirstErrors());
        }
        return true;
    }

    /**
     * @param mixed $account
     * @param mixed $tin
     * @param mixed $pinfl
     * @return string
     */
    private static function generateCorporativeAccountNumber($account,$tin = null, $pinfl = null): string
    {
        if (isTin($tin)) {
            $identification = "00000" . $tin;
        } else {
            $identification = $pinfl;
        }
        return $account . OperationTypeEnum::VIRTUAL_ACCOUNT_CURRENCY_SUM . $identification; // account - bank hisob raqam, currency - valyuta, 00000 - filial raqami, inn - corporative yuridik shaxslar uchun
    }
    public static function findOneByPrefixAndCompany($prefix, $id): ?CompanyVirtualAccount
    {
        return CompanyVirtualAccount::findOne(['prefix_account' => $prefix, 'company_id' => $id, 'status' => StatusEnum::STATUS_ACTIVE]);
    }

    /**
     * @throws Exception
     */
    public static function calculate(Company $company, $prefix_account)
    {
        $account = self::findOneByPrefixAndCompany($prefix_account, $company->id);
        if ($account !== null) {
            $debit = VirtualTransaction::getSumDebitByCompanyTinAndPrefix($company, $prefix_account);
            $credit = VirtualTransaction::getSumCreditByCompanyTinAndPrefix($company, $prefix_account);
            $sum = $credit - $debit;
            $account->price = $sum; // tiyin
            if (!$account->save())
                throw new Exception("Hisoblash jaroyonida xatolik yuz berdi.");
        }
    }
}
