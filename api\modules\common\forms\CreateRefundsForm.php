<?php

namespace api\modules\common\forms;

use api\components\BaseRequest;
use api\modules\common\resources\PlanScheduleClassifierCreateResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\PlanScheduleResource;
use api\modules\common\resources\TransactionRefundsResource;
use common\enums\CompanyTransactionEnum;
use common\enums\StatusEnum;
use common\enums\TenderEnum;
use common\models\CompanyBalance;
use common\models\CompanyTransaction;
use Yii;
use yii\base\Exception;

class CreateRefundsForm extends BaseRequest
{

    public $sum;
    public $account_number;
    public $comment;

    public TransactionRefundsResource $model;

    public function __construct(TransactionRefundsResource $model, $params = [])
    {
        $this->model = $model;
        parent::__construct($params);
    }


    public function rules()
    {
        return [
            [['sum', 'account_number', 'comment'], 'required', 'message' => t('{attribute} yuborish majburiy')],
        ];
    }

    public function getResult()
    {


        $companyId = \Yii::$app->user->identity->company_id;

        $check = TransactionRefundsResource::find()->where(['company_id' => $companyId, 'status' => CompanyTransactionEnum::REFUNDS_STATUS_NEW])->one();
        if ($check) {
            $this->addError('sum', t('Avvalgi tasdiqlanmagan ariza mavjud'));
            return false;
        }

        $sum = $this->sum * 100;

        $transaction = \Yii::$app->db->beginTransaction();

        $this->model->status = CompanyTransactionEnum::REFUNDS_STATUS_NEW;
        $this->model->created_at = date("Y-m-d H:i:s");
        $this->model->created_by = Yii::$app->user->id;
        $this->model->company_id = $companyId;
        $att = $this->attributes;
        $this->model->setAttributes($att, false);
        $this->model->sum = $sum;

        if ($this->model->attributes && $this->model->validate() && $this->model->save()) {
            
            $companyBalance = CompanyBalance::find()->andWhere(['company_id' => $companyId])->one();
            if (!$companyBalance) {
                throw new Exception(t("Company balance yo'q"));
            }

            if ($companyBalance->available < $sum) {
                throw new Exception(t("Balansda yetarli mablag' mavjud emas"));
            }

            $company_transaction = new CompanyTransaction([
                'company_id' => $companyId,
                'amount' => $sum,
                'type' => CompanyTransactionEnum::TYPE_WITHDRAW,
                'description' => $this->comment,
                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                'transaction_date' => date("Y-m-d H:i:s"),
            ]);
            if (!$company_transaction->save()) {
                $this->addErrors($company_transaction->errors);
                $transaction->rollBack();
                return false;
            }
            $this->model->updateAttributes(['transaction_id' => $company_transaction->id]);
            $transaction->commit();
            return true;

        }
        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;

    }
}
