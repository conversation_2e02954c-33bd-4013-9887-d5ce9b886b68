<?php


namespace api\modules\common\resources;


use common\models\CompanyBankAccount;
use common\models\PlanScheduleClassifier;
use common\models\PlanScheduleClassifierUnit;
use common\models\query\NotDeletedFromCompanyQuery;
use function foo\func;

class PlanScheduleClassifierShortResource extends PlanScheduleClassifier
{
    public function fields()
    {
        return [
            'id',
            'month',
            'count_live',
            'summa' => function ($model) {
                return $model->summa / 100;
            },
            'unit',
            'classifier',
        ];
    }


    public function getClassifier()
    {
        return $this->hasOne(ClassifierResource::class, ['id' => 'classifier_id']);
    }

    public function getUnit()
    {
        return $this->hasMany(PlanScheduleClassifierUnitResource::class, ['plan_schedule_classifier_id' => 'id']);
    }
}