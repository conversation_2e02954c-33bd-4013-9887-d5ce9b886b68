<?php

use common\models\Contract;

/**
 * @var $model Contract
 */
function tab()
{
    echo "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
}
$orderList = $model->order->orderList;
$price = $model->order ? (isset($model->order->winner) && $model->order->winner != NULL ? $model->order->winner->price : 0) : 0
?>
<p style="text-align: center">ДОГОВОР № <?= $model->id ?> <?= $model->order->product->platform_display == \common\enums\ProductEnum::PLATFORM_DISPLAY_E_SHOP ? "(Электронный магазин)" :"(Национальный магазин)" ?></p>
<p style="text-align: center;">Идентификатор лота <?= $model->order->lot_number ?><br>на поставку товаров (работ, услуг)
    по результатам проведения электронных государственных закупок на специальном<br>информационном портале
    от <?= date("d.m.Y", strtotime($model->created_at)) ?> г. <?= date("H:i", strtotime($model->created_at)) ?> мин.
    (лот № <?= $model->id ?>)
</p>
<table>
    <tr>
        <td>Алмазарский район
            <br>
            (Место заключения договора)
        </td>
        <td><?= date("Y", strtotime($model->created_at)) ?> г. «<?= date("d", strtotime($model->created_at)) ?>
            » <?= \api\helpers\DateHelper::getMonth(date("m", strtotime($model->created_at))) ?>
            <br>
            (Дата заключения договора)
        </td>
    </tr>

</table>

<p><?=$model->customer->title?> , являющийся корпоративным заказчиком,
    именуемый в дальнейшем «Заказчик», в лице <?=$model->customer ? $model->customer->directorShortName :""?>, действующий на основании, с одной стороны и <?=$model->producer ? $model->producer->directorFullName:""?>, именуемый дальнейшем «Исполнитель», в лице, <?=$model->producer->directorFullName?>, действующий на основании, с другой
    стороны, совместно именуемые «Стороны» по результатам проведения электронных государственных закупок на
    специальном информационном портале, заключили настоящий договор о нижеследующем.

</p>

<h3 class="section-title">I. ПРЕДМЕТ ДОГОВОРА</h3>
<p>
    <?= tab() ?> 1. Заказчик производит полную оплату товара (работы, услуги) и получает товар (работу, услугу),
    Исполнитель
    доставляет товар (работу, услугу) на следующих условиях:

</p>

<table>
    <thead>
    <tr>
        <th>№</th>
        <th>Наименование товара (работы, услуги)</th>
        <th>Единица измерения</th>
        <th>Количество</th>
        <th>Стартовая цена (за единицу, сум)</th>
        <th>Итоговая цена (за единицу, сум)</th>
        <th>Итоговая цена (всего, сум)</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td>1</td>
        <td><?=$orderList->product->title?></td>
        <td>компл.</td>
        <td><?=$orderList->quantity?></td>
        <td><?= showPrice($orderList->price/100)?></td>
        <td><?=showPrice(($model->order->winner->price)/100) ?> сум</td>
        <td><?=showPrice(($price *  $orderList->quantity)/100) ?>  сум</td>
    </tr>
    </tbody>
</table>

<p>Общая сумма договора: <?=showPrice(($price *  $orderList->quantity)/100) ?>  сум</p>
<p>семь миллионов триста сорок девять тысяч восемьсот девяносто пять сумов 00 тийин.
</p>
<h3 class="section-title">ТЕХНИЧЕСКИЕ ПАРАМЕТРЫ</h3>

<table class="no-inner-border">
    <tr>
        <td><?=$orderList->product->title?></td>
        <td>
            <?=$orderList->product->description?>
        </td>
    </tr>
</table>
<h3 class="section-title">ДАННЫЕ О ТОВАРЕ (РАБОТЕ, УСЛУГЕ)</h3>
<table class="no-inner-border">
    <tr>
        <td rowspan="9"><?=$orderList->product->title?></td>
        <td>
            Марка товара (работы,
            услуги)
        </td>
        <td>
            <?=$orderList->product->classifier->title_ru?>
        </td>
    </tr>
    <tr>
        <td>
            Производитель товара
            (работы, услуги)
        </td>
        <td>
            CASTER SIT

        </td>
    </tr>
    <tr>
        <td>
            Страна производитель

        </td>
        <td>
            Узбекистан

        </td>
    </tr>
    <tr>
        <td>Гарантийный период
        </td>
        <td>
            <?=$orderList->product->warranty?>
        </td>
    </tr>
    <tr>
        <td>Срок годности
        </td>
        <td>
            25.12.2025
        </td>
    </tr>
    <tr>
        <td>Год производства
        </td>
        <td>
            <?=$orderList->product->year?>
        </td>
    </tr>
    <tr>
        <td>Лицензия или сертификат (в
            случаях,
            предусмотренных
            законодательством)
        </td>
        <td>
            <?=$orderList->product->is_have_license ? "Да" :"Нет" ?>
        </td>
    </tr>
    <tr>
        <td>Регион доставки товара
            (работы, услуги)
        </td>
        <td>
            <?=$model->order->region->title_ru?>
        </td>
    </tr>
    <tr>
        <td>Комментарий победителя по
            лицензии

        </td>
        <td>
            -
        </td>
    </tr>
</table>

<h3 class="section-title">СВОЙСТВО ТОВАРА (РАБОТЫ, УСЛУГИ)</h3>

<p>
    <?= tab() ?> 2.1. Заказчик обязуется обеспечить наличие на лицевом счете в расчетно-клиринговой палате Оператора
    (далее –
    РКП) 100% суммы договора, в течение 10 рабочих дней. При этом задаток засчитывается в счет суммы договора. <br>
    <?= tab() ?> 2.2. Исполнитель обязуется осуществить поставку товара в течение 1 день (рабочих) с момента получения
    уведомления от РКП об оплате. <br>
    <?= tab() ?> 2.3. Заказчик обязан проверить комплектность, качество и соответствие другим требованиям,
    предусмотренным в
    объявлении (заявке) или оферте о проведении электронных государственных закупок получаемого товара (работы,
    услуги) в присутствии Исполнителя при принятии товара (работы, услуги). <br>
    <?= tab() ?> 2.4. Все расходы по транспортировке товара (работы, услуги) несет Исполнитель, если иное не установлено
    условиями настоящего договора. <br>
    <?= tab() ?> 2.5. Факт поставки товара (работы, услуги) Исполнителем и его приёмки Заказчиком подтверждает
    оформленная
    Исполнителем счет-фактура, подписываемая Сторонами. <br>
    <?= tab() ?> 2.6. После принятия товара (работы, услуги) в течение 3 рабочих дней Заказчик обязан направить
    Оператору
    информацию, подтверждающую поставку товара (работы, услуги), через свой персональный кабинет, на основании
    которой в установленном порядке осуществляется оплата на расчетный счет Исполнителя. <br>

</p>
