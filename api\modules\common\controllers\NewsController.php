<?php

namespace api\modules\common\controllers;

use api\components\ApiController;
use api\modules\common\filters\NewsFilter;
use api\modules\common\filters\SimilarNewsFilter;
use api\modules\common\filters\UnitFilter;
use common\models\News;
use Yii;

class NewsController extends ApiController
{
    public function actionNewsList()
    {
        return $this->sendResponse(
            new  NewsFilter(),
            Yii::$app->request->queryParams,
        );
    }
    public function actionSimilarNews($id)
    {
        return $this->sendResponse(
            new  SimilarNewsFilter(),
            Yii::$app->request->queryParams,
        );
    }
    public function actionOneNewView($id)
    {
        return $this->sendModel($this->findOne($id));
    }
    private function findOne($id)
    {
        $model = News::findOrFail($id);
        return $model;
    }
}
