<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "affiliates".
 *
 * @property int $id
 * @property int|null $company_id
 * @property string|null $company_tin
 * @property string|null $pinfl
 * @property string|null $full_name
 */
class Affiliates extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'affiliates';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['company_id', 'company_tin', 'pinfl', 'full_name'], 'default', 'value' => null],
            [['company_id'], 'integer'],
            [['company_tin'], 'string', 'max' => 9],
            [['pinfl'], 'string', 'max' => 14],
            [['full_name'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => Yii::t('app', 'ID'),
            'company_id' => Yii::t('app', 'Company ID'),
            'company_tin' => Yii::t('app', 'Company Tin'),
            'pinfl' => Yii::t('app', 'Pinfl'),
            'full_name' => Yii::t('app', 'Full Name'),
        ];
    }

}
