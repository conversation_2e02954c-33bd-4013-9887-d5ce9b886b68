<?php


namespace api\modules\shop\forms;


use Yii;
use api\components\BaseRequest;
use api\modules\shop\resources\ContractResource;
use common\enums\ContractEnum;
use common\enums\OperationTypeEnum;
use common\models\auction\Auction;
use common\models\VirtualTransaction;
use console\models\AuctionHistory;
use console\models\Contract;
use yii\httpclient\Exception;
use yii\web\NotFoundHttpException;

class CancelContractAuctionProducerForm extends BaseRequest
{
    public $customer_id;
    public $producer_id;
    public ContractResource $model;

    public $pkcs7;
    public $id;

    public function rules()
    {
        return [
            ['id', 'required'],
        ];
    }

    /**
     * @throws Exception
     * @throws \yii\db\Exception
     * @throws NotFoundHttpException
     * @throws \Exception
     * @throws \Throwable
     */
    public function getResult()
    {
        $this->model = ContractResource::findOrFail($this->id);
        $user = Yii::$app->user->identity;
        $company_id = $user->company_id;

        if ($this->model->producer_id == $company_id) {
            $this->producer_id = $company_id;
        } else {
            $this->addError("error", t("Shartnomadan bosh tortish uchun yetkazib beruvchi bo'lishingiz kerak"));
            return false;
        }
        /**
         * @var $auction Auction
         */
        $auction = $this->model->auction;
        if (!$auction) {
            throw new Exception(t("Auksion topilmadi"));
        }

        if (!in_array($this->model->status, [ContractEnum::STATUS_SIGNED])) {
            $this->addError("error", t("Shartnomadan bosh tortish uchun muqobil holatda emas"));
            return false;
        }

        $date = date("Y-m-d H:i:s");
        $transaction = \Yii::$app->db->beginTransaction();

        $this->model->status = ContractEnum::STATUS_DISCARD;
        $this->model->producer_cancel_date = date("Y-m-d H:i:s");

        if ($this->model->attributes && $this->model->validate() && $this->model->save()) {


            $reserveOffer = $auction->reserveOffer;
            $currentOffer = $auction->currentOffer;
            if ($currentOffer->company_id == $this->producer_id) {


//                $company_out_id = $this->model->producer_id;
//                $company_in_id = $this->model->customer_id;
//
//                $transactionOut = CompanyTransaction::find()->where(['auction_id' => $auction->id, 'type' => CompanyTransactionEnum::TYPE_ZALOG, 'status' => CompanyTransactionEnum::STATUS_SUCCESS, 'reverted_id' => null])
//                    ->andWhere(['in', 'company_id', [$company_out_id]])->one();
//
//                $transactionOutReverted = CompanyTransaction::find()->where(['auction_id' => $auction->id, 'type' => CompanyTransactionEnum::TYPE_ZALOG, 'status' => CompanyTransactionEnum::STATUS_SUCCESS, 'reverted_id' => null])
//                    ->andWhere(['in', 'company_id', [$company_out_id, $company_in_id]])->all();
//
//                foreach ($transactionOutReverted as $company_transaction1) {
//                    $revert = new CompanyTransaction([
//                        'company_id' => $company_transaction1->company_id,
//                        'contract_id' => $this->model->id,
//                        'auction_id' => $company_transaction1->auction_id,
//                        'amount' => $company_transaction1->amount,
//                        'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
//                        'description' => Yii::t("main", "Garov bandlashdan chiqarildi."),
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'transaction_date' => $date,
//                    ]);
//                    if ($revert->save()) {
//                        $company_transaction1->reverted_id = $revert->id;
//                        if (!$company_transaction1->save()) {
//                            $transaction->rollBack();
//                            $this->addErrors($company_transaction1->errors);
//                            return false;
//                        }
//                    } else {
//                        $transaction->rollBack();
//                        $this->addErrors($revert->errors);
//                        return false;
//                    }
//                }
//
//                if ($transactionOut) {
//                    $company_transaction_penalty_out = new CompanyTransaction([
//                        'company_id' => $company_out_id,
//                        'contract_id' => $this->model->id,
//                        'auction_id' => $this->model->auction_id,
//                        'amount' => $transactionOut->amount,
//                        'type' => CompanyTransactionEnum::TYPE_PENALTY_OUT,
//                        'description' => \Yii::t("main", "Shartnomadan bosh tortilgani uchun jarima to'ladi"),
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'transaction_date' => $date,
//                    ]);
////
//                    if (!$company_transaction_penalty_out->save()) {
//                        $this->addErrors($company_transaction_penalty_out->errors);
//                        $transaction->rollBack();
//                        return false;
//                    }
//
//                    $company_transaction_penalty_in = new CompanyTransaction([
//                        'company_id' => $company_in_id,
//                        'contract_id' => $this->model->id,
//                        'auction_id' => $this->model->auction_id,
//                        'amount' => $company_transaction_penalty_out->amount,
//                        'type' => CompanyTransactionEnum::TYPE_PENALTY_IN,
//                        'description' => \Yii::t("main", "Shartnomadan bosh tortilgani uchun yetkazib beruvchining garov summa(jarima)sini qabul qilib oldi"),
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'transaction_date' => $date,
//                    ]);
////
//                    if (!$company_transaction_penalty_in->save()) {
//                        $this->addErrors($company_transaction_penalty_in->errors);
//                        $transaction->rollBack();
//                        return false;
//                    }
//                }

                // Buyurtmachini bitim uchun blokirovka qilingan depositi blokirovkadan chiqarildi.
                /** @var VirtualTransaction  $transactionOutReverted */
                $transactionOutReverted = VirtualTransaction::find()
                    ->where([
                        'auction_id' => $auction->id,
                        'operation_type' => OperationTypeEnum::BLOCK_TRANSACTION_DEPOSIT,
                        'parent_id' => null,
                        'contract_id' => $this->model->id,
                        'credit_company_id' => $this->model->customer_id,
                    ])->andWhere(['>','credit',0])->one();
                if (!$transactionOutReverted) {
                    $transaction->rollBack();
                    throw new Exception("Buyurtmachini bitim uchun blokirovka qilingan deposit mablag'i topilmadi.");
                }

                try {
                    $revertID = VirtualTransaction::saveTransaction(
                        $transactionOutReverted->creditCompany,
                        $transactionOutReverted->debitCompany,
                        $transactionOutReverted->creditAccount->prefix_account,
                        $transactionOutReverted->debitAccount->prefix_account,
                        $transactionOutReverted->credit,
                        "Shartnoma uchun blokirovka qilingan deposit mablag'i blokirovkadan chiqarildi.",
                        OperationTypeEnum::PRODUCT_NAME_AUCTION,
                        $auction->id,
                        $this->model->id,
                        OperationTypeEnum::CANCEL_BLOCK_TRANSACTION_DEPOSIT,
                    );
                    $transactionOutReverted->parent_id = $revertID;
                    if (!$transactionOutReverted->save()) {
                        $transaction->rollBack();
                        $this->addErrors($transactionOutReverted->errors);
                        return false;
                    }
                } catch (Exception $e) {
                    $transaction->rollBack();
                    throw new Exception($e->getMessage());
                }

                // Yetkazib beruvchini depositi buyurtmachiga o'tkazib berildi.
                /** @var VirtualTransaction  $transactionOut */
                $transactionOut = VirtualTransaction::find()
                    ->where([
                        'auction_id' => $auction->id,
                        'operation_type' => OperationTypeEnum::BLOCK_TRANSACTION_DEPOSIT,
                        'parent_id' => null,
                        'contract_id' => $this->model->id,
                        'credit_company_id' => $this->model->producer_id,
                    ])->andWhere(['>','credit',0])->one();
                if (!$transactionOut) {
                    $transaction->rollBack();
                    throw new Exception("Yetkazib beruvchini bitim uchun blokirovka qilingan deposit mablag'i topilmadi.");
                }
                try {
                    $transferID = VirtualTransaction::saveTransaction(
                        $this->model->producer,
                        $this->model->customer,
                        OperationTypeEnum::P_K_30301,
                        OperationTypeEnum::P_K_30101,
                        $transactionOut->credit,
                        "Shartnomadan bosh tortilgani uchun yetkazib beruvchi jarima to'ladi",
                        OperationTypeEnum::PRODUCT_NAME_AUCTION,
                        $auction->id,
                        $this->model->id,
                        OperationTypeEnum::PAY_DEPOSIT_AS_FINE_SELLER
                    );
                    $transactionOut->parent_id = $transferID;
                    if (!$transactionOut->save()) {
                        $transaction->rollBack();
                        $this->addErrors($transactionOut->errors);
                        return false;
                    }
                } catch (Exception $e) {
                    $transaction->rollBack();
                    throw new Exception($e->getMessage());
                }

                $history = new AuctionHistory();
                $history->auction_id = $auction->id;
                $history->user_id = $user->id;
                $history->status = $auction->status;
                $history->comment = "G'olib berok qildi, shartnoma zaxira g'olibga yuborildi";
                $history->created_at = $date;
                if (!$history->save()) {
                    $this->addErrors($history->errors);
                    $transaction->rollBack();
                    return false;
                }

                if ($reserveOffer) {
                    Contract::createContract($transaction, $auction, $reserveOffer->company_id, $this->model->price, ContractEnum::STATUS_WAITING_RESERVE, $this->model->number, 0);
                }

            } else {
                $history = new AuctionHistory();
                $history->auction_id = $auction->id;
                $history->user_id = $user->id;
                $history->status = $auction->status;
                $history->comment = "Zaxira g'olib berok qildi, shartnoma bekor qilindi";
                $history->created_at = $date;
                if (!$history->save()) {
                    $this->addErrors($history->errors);
                    $transaction->rollBack();
                    return false;
                }
            }

            //Shartnoma uchun to'lov qilib quygan bo'lsa, o'ziga qaytarib beriladi
//            $transactionContractPayment = CompanyTransaction::find()
//                ->where([
//                    'auction_id' => $auction->id,
//                    'contract_id' => $this->model->id,
//                    'type' => CompanyTransactionEnum::TYPE_BLOCK_PAYMENT_FOR_CONTRACT,
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'reverted_id' => null
//                ])
//                ->andWhere(['company_id' => $this->model->customer_id])->all();
//
//            foreach ($transactionContractPayment as $company_transaction1) {
//                $revert = new CompanyTransaction([
//                    'company_id' => $company_transaction1->company_id,
//                    'contract_id' => $company_transaction1->contract_id,
//                    'auction_id' => $company_transaction1->auction_id,
//                    'amount' => $company_transaction1->amount,
//                    'type' => CompanyTransactionEnum::TYPE_REVERT_PAYMENT_FOR_CONTRACT,
//                    'description' => Yii::t("main", "Shartnoma to'lovi bandlashdan chiqarildi."),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => $date,
//                ]);
//                if ($revert->save()) {
//                    $company_transaction1->reverted_id = $revert->id;
//                    if (!$company_transaction1->save()) {
//                        $transaction->rollBack();
//                        $this->addErrors($company_transaction1->errors);
//                        return false;
//                    }
//                } else {
//                    $transaction->rollBack();
//                    $this->addErrors($revert->errors);
//                    return false;
//                }
//            }

            try {
                /** @var VirtualTransaction  $transactionContractPayment */
                $transactionContractPayment = VirtualTransaction::find()
                    ->where([
                        'auction_id' => $auction->id,
                        'contract_id' => $this->model->id,
                        'operation_type' =>  OperationTypeEnum::BLOCK_TRANSACTION_FULL_PAYMENT,
                        'parent_id' => null
                    ])
                    ->andWhere(['>', 'credit', 0])
                    ->andWhere(['debit_company_id' => $this->model->customer_id])->one();
                if ($transactionContractPayment) {
                    $revertID = VirtualTransaction::saveTransaction(
                        $transactionContractPayment->creditCompany,
                        $transactionContractPayment->debitCompany,
                        $transactionContractPayment->creditAccount->prefix_account,
                        $transactionContractPayment->debitAccount->prefix_account,
                        $transactionContractPayment->credit,
                        "Shartnoma to'lovi bandlashdan chiqarildi.",
                        OperationTypeEnum::PRODUCT_NAME_AUCTION,
                        $auction->id,
                        $this->model->id,
                        OperationTypeEnum::BLOCK_TRANSACTION_FULL_PAYMENT,
                    );
                    $transactionContractPayment->parent_id = $revertID;
                    if (!$transactionContractPayment->save()) {
                        $transaction->rollBack();
                        $this->addErrors($transactionContractPayment->errors);
                        return false;
                    }
                }
            } catch (\Throwable $ex) {
                $transaction->rollBack();
                $this->addError('error', $ex->getMessage());
                return false;
            }
            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }

}