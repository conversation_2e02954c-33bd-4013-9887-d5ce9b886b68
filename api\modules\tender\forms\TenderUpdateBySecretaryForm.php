<?php


namespace api\modules\tender\forms;


use api\components\BaseModel;
use api\components\BaseRequest;
use api\modules\common\resources\DistrictResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\RegionResource;
use api\modules\tender\resources\CommissionGroupMemberResource;
use api\modules\tender\resources\CommissionGroupResource;
use api\modules\common\resources\FileResource;
use api\modules\tender\resources\TenderActionRequestResource;
use api\modules\tender\resources\TenderClassifierCreateResource;
use api\modules\tender\resources\TenderClassifierResource;
use api\modules\tender\resources\TenderCommissionMemberResource;
use api\modules\tender\resources\TenderCommissionVoteResource;
use api\modules\tender\resources\TenderQualificationFileListResource;
use api\modules\tender\resources\TenderRequirementsResource;
use api\modules\tender\resources\TenderResource;
use common\enums\CompanyEnum;
use common\enums\StatusEnum;
use common\enums\TenderEnum;
use common\models\Bmh;
use common\models\CommissionGroup;
use common\models\Tender;
use common\models\WorkdayCalendar;
use Yii;
use yii\db\Exception;
use yii\helpers\ArrayHelper;

class TenderUpdateBySecretaryForm extends BaseRequest
{

    public TenderResource $model;
    public $type;
    public $title;
    public $preference_local_producer;
    public $language;
    public $placement_period;
    public $funding_source;
    public $purchase_currency;
    public $description;
    public $amount_deposit;
    public $accept_guarantee_bank;
    public $method_payment;
    public $advance_payment_percentage;
    public $advance_payment_period;
    public $contact_file_id;
    public $unblocking_type;
    public $contact_position;
    public $contact_fio;
    public $contact_phone;
    public $plan_schedule_id;
    public $region_id;
    public $district_id;
    public $address;
    public $delivery_phone;
    public $technical_task_file_id;
    public $technical_document_file_id;
    public $criteria_evaluation_proposals;
    public $min_passing_ball;
    public $technical_part;
    public $price_part;

    public $products = [];
    public $requirements = [];
    public $qualifications = [];
    public $commission_group_id;

    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        $parent = parent::rules();
        $child = [
            [['type', 'preference_local_producer', 'language', 'placement_period',
                'purchase_currency', 'amount_deposit', 'accept_guarantee_bank', 'method_payment',
                'unblocking_type', 'plan_schedule_id', 'region_id', 'district_id',
                'title', 'funding_source', 'description',
                'contact_position', 'contact_fio', 'contact_phone', 'address', 'delivery_phone', 'products', 'requirements',
                'commission_group_id', 'criteria_evaluation_proposals',
                //'min_passing_ball', 'contact_file_id', 'technical_task_file_id', 'technical_document_file_id',
            ], 'required', 'message' => '{attribute} maydoni yuborish majburiy'],
            [['preference_local_producer', 'language',
                'contact_file_id', 'advance_payment_period',
                'plan_schedule_id', 'region_id', 'district_id',
                'technical_task_file_id', 'technical_document_file_id', 'commission_group_id'], 'integer'],
            [['accept_guarantee_bank', 'unblocking_type'], 'integer', 'min' => 0, 'max' => 1],
            [['title', 'funding_source', 'description',
                'contact_position', 'contact_fio', 'contact_phone', 'address', 'delivery_phone'], 'string', 'max' => 255],
            [['products', 'requirements', 'qualifications'], 'safe'],
            [['contact_file_id'], 'exist', 'skipOnError' => true, 'targetClass' => FileResource::class, 'targetAttribute' => ['contact_file_id' => 'id']],
            [['technical_document_file_id'], 'exist', 'skipOnError' => true, 'targetClass' => FileResource::class, 'targetAttribute' => ['technical_document_file_id' => 'id']],
            [['technical_task_file_id'], 'exist', 'skipOnError' => true, 'targetClass' => FileResource::class, 'targetAttribute' => ['technical_task_file_id' => 'id']],
            [['region_id'], 'exist', 'skipOnError' => true, 'targetClass' => RegionResource::class, 'targetAttribute' => ['region_id' => 'id']],
            [['district_id'], 'exist', 'skipOnError' => true, 'targetClass' => DistrictResource::class, 'targetAttribute' => ['district_id' => 'id']],
            [['commission_group_id'], 'exist', 'skipOnError' => true, 'targetClass' => CommissionGroupResource::class, 'targetAttribute' => ['commission_group_id' => 'id']],
            [['min_passing_ball', 'amount_deposit'], 'number'],
            [['advance_payment_percentage'], 'number', 'min' => 15, 'max' => 85],
            ['type', 'checkType'],
            ['purchase_currency', 'checkPurchaseCurrency'],
            ['amount_deposit', 'checkDeposit'],
            ['method_payment', 'checkMethodPayment'],
            ['criteria_evaluation_proposals', 'checkCriteria'],
            [['advance_payment_percentage', 'advance_payment_period'], 'required', 'when' => function ($model) {
                return $this->method_payment == TenderEnum::ADVANCE_PAYMENT;
            }],
            [['technical_part', 'price_part'], 'required', 'when' => function ($model) {
                return $this->criteria_evaluation_proposals == TenderEnum::CRITERIA_EVALUATION_PROPOSALS_BALLING;
            }],
            [['technical_part', 'price_part'], 'checkPassingBal'],
            [['placement_period'], 'checkPlacementPeriod'],

            ['placement_period', 'integer', 'min' => 5, 'max' => 30, 'message' => t("Joylashtirish muddati 5-30 kun oralig'ida bo'lishi kerak")],
        ];
        return array_merge($parent, $child);
    }


    public function checkType($attribute, $params)
    {
        if ($this->type == TenderEnum::TYPE_TENDER || $this->type == TenderEnum::TYPE_INVITE) {
            return true;
        } else {
            $this->addError('type', t("Joylashtirish turi qiymatlari noto'g'ri yuborildi"));

            return false;
        }
    }

    public function checkCriteria()
    {
        if ($this->criteria_evaluation_proposals == TenderEnum::CRITERIA_EVALUATION_PROPOSALS_MIN_PRICE || $this->criteria_evaluation_proposals == TenderEnum::CRITERIA_EVALUATION_PROPOSALS_BALLING) {
            return true;
        } else {
            $this->addError('criteria_evaluation_proposals', t("Takliflarni baholash me'zoni qiymatlari noto'g'ri yuborildi"));
            return false;
        }
    }

    public function checkPassingBal()
    {
        $min = $this->technical_part;
        $max = $this->price_part;
        if (($max + $min) == 100) {
            if ($min >= 30 && $min <= 70) {
                return true;
            } else {
                $this->addError('price_part', t("Texnik qismi ulushi qiymatlari 30%-70% orasi bo'lishi kerak"));
                return false;
            }

        } else {
            $this->addError('price_part', t("Narx qismi va Texnik qismi ulushlari birgalikda 100% bo'lishi kerak"));
            return false;
        }
    }

    public function checkPurchaseCurrency()
    {
        if (in_array($this->purchase_currency, TenderEnum::PURCHASE_CURRENCY_LIST)) {
            return true;
        } else {
            $this->addError("purchase_currency", t("Xarid qilish valyutasi maydoni qiymatlari noto'g'ri yuborildi"));
            return false;
        }
    }

    public function checkMethodPayment()
    {
        if (in_array($this->method_payment, TenderEnum::METHOD_PAYMENT_LIST)) {
            return true;
        } else {
            $this->addError("method_payment", t("To'lov tartibi maydoni qiymatlari noto'g'ri yuborildi"));
            return false;
        }
    }

    public function checkPlacementPeriod()
    {
        if ($this->type == TenderEnum::TYPE_TENDER) {

            if ($this->placement_period >= TenderEnum::PERIOD_TENDER_MIN && $this->placement_period <= TenderEnum::PERIOD_TENDER_MAX) {
                return true;
            } else {
                $this->addError("placement_period", t("Joylashtirish muddati 12-30 ish kuni oralig'ida bo'lishi kerak"));
            }

        } else {

            if ($this->placement_period >= TenderEnum::PERIOD_SELECTION_MIN && $this->placement_period <= TenderEnum::PERIOD_SELECTION_MAX) {
                return true;
            } else {
                $this->addError("placement_period", t("Joylashtirish muddati 5-12 ish kuni oralig'ida bo'lishi kerak"));
            }
        }
        return false;
    }

    public function checkDeposit()
    {
        if ($this->type == TenderEnum::TYPE_TENDER) {
            if ($this->amount_deposit >= 0 and $this->amount_deposit <= TenderEnum::DEPOSIT_TENDER_MAX) {
                return true;
            } else {
                $this->addError("amount_deposit", Yii::t("main", "Garov miqdori 0% - {val}% oraliqda bo'lishi kerak", ['val' => TenderEnum::DEPOSIT_TENDER_MAX]));
                return false;
            }
        } else {
            if ($this->amount_deposit >= 0 and $this->amount_deposit <= TenderEnum::DEPOSIT_INVITE_MAX) {
                return true;
            } else {
                $this->addError("amount_deposit", Yii::t("main", "Garov miqdori 0% - {val}% oraliqda bo'lishi kerak", [
                    'val' => TenderEnum::DEPOSIT_INVITE_MAX
                ]));
                return false;
            }
        }

    }

    public function getResult()
    {


        $companyId = \Yii::$app->user->identity->company_id;
        $company = \Yii::$app->user->identity->company;
        $tenderActionRequest = TenderActionRequestResource::find()->where(['tender_id' => $this->model->id, 'status' => TenderEnum::TENDER_ACTION_STATUS_READY, 'type' => TenderEnum::TENDER_ACTION_TYPE_UPDATE])->one();
        if(!$tenderActionRequest){
            $this->addError("error", t("Tahrirlash uchun tasdiqlangan so'rov topilmadi"));
            return false;
        }

        $role = $this->model->getCommissionMemberRole();
        if($role != TenderEnum::ROLE_SECRETARY){
            $this->addError("error", t("Ruxsat mavjud emas"));
            return false;
        }

        if($this->model->state != TenderEnum::STATE_READY){
            $this->addError("error", t("Tahrirlash mumkin bo'lmagan xolatda turibdi."));
            return false;
        }

        $transaction = \Yii::$app->db->beginTransaction();

        $tenderActionRequest->status = TenderEnum::TENDER_ACTION_STATUS_DONE;
        if(!$tenderActionRequest->save()){
            $transaction->rollBack();
            $this->addErrors($tenderActionRequest->errors);
            return false;
        }


        $att = $this->attributes;
        $this->model->setAttributes($att, false);

        if ($this->model->type == TenderEnum::TYPE_INVITE) {
            $date = date("Y-m-d H:i:s");
            $end = new \DateTime($this->model->end_date);
            $workDays = WorkdayCalendar::getWorkdaysAsArr(WorkdayCalendar::WORKDAY);
            $holidays = WorkdayCalendar::getWorkdaysAsArr(WorkdayCalendar::HOLIDAY);
            $pre_date = addDaysExcludingWeekends($end->modify('-1 day')->format('Y-m-d H:i:s'),1, $workDays, $holidays,false);
            if ($pre_date > $date) {
                throw new Exception(t("Xarid qilish yakunlashiga 1 ish kuni qolganida, o'zgartira olasiz !!!"));
            }
            $classifiers = ArrayHelper::getColumn($this->products,'classifier_id');
            $_classifiers = ArrayHelper::getColumn(TenderClassifierResource::findAll(["tender_id" => $this->model->id]),'classifier_id');
            if ($classifiers != $_classifiers) {
                throw new Exception("Mahsulotlar ro'yxatini o'zgartira olmaysiz !!!");
            }
            if ($this->plan_schedule_id != $this->model->plan_schedule_id) {
                throw new Exception("Xarid reja jadvalini o'zgartira olmaysiz !!!");
            }
            $this->model->end_date = addDaysExcludingWeekends($this->model->end_date, 3, $workDays, $holidays);
        }

        if ($this->model->attributes && $this->model->validate() && $this->model->save()) {
            $tenderId = $this->model->id;
            TenderCommissionMemberResource::updateAll(['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s")], ['tender_id' => $tenderId]);
            TenderClassifierResource::updateAll(['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s")], ['tender_id' => $tenderId]);
            TenderRequirementsResource::updateAll(['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s")], ['tender_id' => $tenderId]);
            TenderCommissionVoteResource::updateAll(['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s")], ['tender_id' => $tenderId]);
            TenderQualificationFileListResource::updateAll(['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s")], ['tender_id' => $tenderId]);
            //tenderga aloqador hammasi ochadi
            //#all_tender

            $group = CommissionGroup::findOne(['id' => $this->commission_group_id, 'company_id' => $companyId]);
            if (!$group) {
                $this->addError('error', t("Guruh topilmadi"));
                $transaction->rollBack();
                return false;
            }
            $commission_members = CommissionGroupMemberResource::find()->where(['commission_group_id' => $group->id, 'status' => TenderEnum::STATUS_ACTIVE])->all();
            foreach ($commission_members as $members) {
                $tenderCommissionMember = new TenderCommissionMemberResource();

                $tenderCommissionMember->commission_group_member_id = $members->id;
                $tenderCommissionMember->commission_member_id = $members->commission_member_id;
                $tenderCommissionMember->role = $members->role;
                $tenderCommissionMember->tender_id = $tenderId;
                $tenderCommissionMember->company_id = $companyId;
                $tenderCommissionMember->status = TenderEnum::STATUS_ACTIVE;
                if (!($tenderCommissionMember->validate() && $tenderCommissionMember->save())) {
                    $transaction->rollBack();

                    $this->addErrors($tenderCommissionMember->errors);
                    return false;
                }
            }


            foreach ($this->products as $product) {
                $tenderProduct = new TenderClassifierCreateResource();
                $tenderProduct->setAttributes($product, false);
                $tenderProduct->tender_id = $tenderId;
                $tenderProduct->price = $tenderProduct->price * 100;
                $tenderProduct->status = TenderEnum::STATUS_ACTIVE;
                if (!$tenderProduct->validate()) {
                    $transaction->rollBack();
                    $this->addError('products', $tenderProduct->errors);
                    return false;
                }

                $planSchedule = PlanScheduleClassifierResource::find()
                    ->notDeleted()
                    ->andWhere(['classifier_id' => $tenderProduct->classifier_id, 'id' => $tenderProduct->plan_schedule_classifier_id, 'status' => TenderEnum::STATUS_ACTIVE])
                    ->one();
                if ($planSchedule == null) {
                    $transaction->rollBack();

                    $this->addError("products", t("Maxsulot aktiv xolatda topilmadi"));
                    return false;
                }
//                $planSchedule->count_live = $planSchedule->count_live + $sum;
//                $planSchedule->count_used = $planSchedule->count_used - $sum;
//                $planSchedule->save();

                if ($tenderProduct->number_purchased > $planSchedule->count_live || $tenderProduct->number_purchased <= 0) {
                    $transaction->rollBack();

                    $this->addError("products", t("Xarid qilinayotgan tovar soni, xarid jadvalidagi qolgan maxsulot soniga mutanosib emas"));
                    return false;
                }

                if (!$tenderProduct->save()) {
                    $transaction->rollBack();
                    $this->addError('products', $tenderProduct->errors);
                    return false;
                }
            }

            $bmh = Bmh::getAmount();
            $price = $this->model->tenderTotalPrice;
            $isTender = $this->model->type == TenderEnum::TYPE_TENDER;
            $isNoByudjet = $company->organization_type == CompanyEnum::NO_BYUDJET;
            $limitMultiplier = $isNoByudjet ? 25000 : 6000;
            $limit = $bmh * $limitMultiplier;
            if ($isTender) {
                if ($price < $limit) {
                    $transaction->rollBack();
                    $this->addError("error", Yii::t("main",
                        "Tovarlarning (ishlarning, xizmatlarning) qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining {limit} baravaridan ortiq miqdorni tashkil etadi",
                        ['limit' => $limitMultiplier]
                    ));
                    return false;
                }
            } else {
                if ($price > $limit) {
                    $transaction->rollBack();
                    $this->addError("error", Yii::t("main",
                        "Tovarlarning (ishlarning, xizmatlarning) qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining {limit} baravarigacha bo‘lgan miqdorni tashkil etadi",
                        ['limit' => $limitMultiplier]
                    ));
                    return false;
                }
            }
            foreach ($this->requirements as $requirement) {
                $tenderRequirements = new TenderRequirementsResource();
                $tenderRequirements->setAttributes($requirement, false);
                $tenderRequirements->tender_id = $tenderId;
                $tenderRequirements->status = TenderEnum::STATUS_ACTIVE;

                if (!($tenderRequirements->validate() && $tenderRequirements->save())) {
                    $transaction->rollBack();
                    $this->addError('requirements', $tenderRequirements->errors);
                    return false;
                }

            }

            foreach ($this->qualifications as $title) {
                if ($title == null || $title == '') {
                    $transaction->rollBack();
                    $this->addError('qualifications', "Qiymat yuborilmagan {title}");
                    return false;
                }
                $tenderRequirements = new TenderQualificationFileListResource();
                $tenderRequirements->title = $title;
                $tenderRequirements->tender_id = $tenderId;
                $tenderRequirements->company_id = $companyId;
                $tenderRequirements->status = StatusEnum::STATUS_ACTIVE;

                if (!($tenderRequirements->validate() && $tenderRequirements->save())) {
                    $transaction->rollBack();

                    $this->addError('qualifications', $tenderRequirements->errors);
                    return false;
                }

            }


            $transaction->commit();
            return true;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}