<?php

namespace backend\controllers;

use api\modules\shop\resources\ProductResource;
use common\enums\OperationTypeEnum;
use common\enums\ProductEnum;
use common\models\Classifier;
use common\models\CompanyBalance;
use common\models\CompanyVirtualAccount;
use common\models\Contract;
use common\models\TenderModeratorLog;
use common\models\WorkdayCalendar;
use Yii;
use backend\models\Product;
use backend\models\ProductSearch;
use yii\base\Exception;
use yii\data\ActiveDataProvider;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * ProductController implements the CRUD actions for Product model.
 */
class ProductController extends BackendController
{
    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ProductSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        $classifier = ArrayHelper::map(Classifier::find()->all(), 'id', 'title_uz');
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'classifier' => $classifier,
        ]);
    }

    public function actionChart()
    {
        $year = Yii::$app->request->get('year') ? Yii::$app->request->get('year') : date("Y");
        $month = Yii::$app->request->get('month') ? Yii::$app->request->get('month') : date("m");

        $contract = Contract::find()->select([
            'DATE(created_at) as created_at',
            'SUM(price) as price'
        ])->andWhere(['is not', 'order_id', null]);
        if ($year) {
            $contract->andWhere(['extract(year from created_at)' => $year]);
        }
        if ($month) {
            $contract->andWhere(['extract(month from created_at)' => $month]);
        }
        $data = $contract->groupBy(['DATE(created_at)'])->all();

        $daysInMonth = cal_days_in_month(CAL_GREGORIAN, $month, $year);

        $startDate = $year . '-' . $month . '-' . '01';
        $endDate = $year . '-' . $month . '-' . $daysInMonth;
        $allDates = [];
        $currentDate = strtotime($startDate);
        $endDateTimestamp = strtotime($endDate);

        while ($currentDate <= $endDateTimestamp) {
            $allDates[date('Y-m-d', $currentDate)] = 0; // 0 qiymat bilan boshlaymiz
            $currentDate = strtotime('+1 day', $currentDate);
        }

        foreach ($data as $d) {
            $allDates[$d->created_at] = $d->price;
        }

        return $this->render('chart', [
            'year' => $year,
            'month' => $month,
            'data' => $allDates,
        ]);
    }

    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionModerator($state)
    {
        $searchModel = new ProductSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, $state);
        $classifier = ArrayHelper::map(Classifier::find()->all(), 'id', 'title_uz');
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'classifier' => $classifier,
        ]);
    }

    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionModeratorRejected($state)
    {
        $searchModel = new ProductSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, $state);
        $classifier = ArrayHelper::map(Classifier::find()->all(), 'id', 'title_uz');
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'classifier' => $classifier,
        ]);
    }

    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionNoMoney($state)
    {
        $searchModel = new ProductSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, $state);
        $classifier = ArrayHelper::map(Classifier::find()->all(), 'id', 'title_uz');
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'classifier' => $classifier,
        ]);
    }

    /**
     * Lists all Product models.
     * @return mixed
     * @throws Exception
     */
    public function actionModeratorAccept()
    {

        $model = $this->findModel(Yii::$app->request->post()['id']);

        $transaction = \Yii::$app->db->beginTransaction();


        // Balance tekshiriladi pul yetarli bolsa savdoga chiqariladi

        $company_id = $model->company_id;
        $totalSum = $model->max_order * $model->unit_price * env('SHOP_ZALOG_PERSENT', 0.03);

        if (hasMoney($model->company, $totalSum)) {
            $model->state = ProductEnum::SHOP_STATE_ACTIVE;
            $model->active_date = date('Y-m-d H:i:s');

            $holidays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::HOLIDAY]), 'local_date', 'local_date');
            $workDays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::WORKDAY]), 'local_date', 'local_date');


            $model->inactive_date = addDaysExcludingWeekends(date("Y-m-d H:i:s"), 2, $workDays, $holidays);
//            $model->inactive_date = date("Y-m-d H:i:s", strtotime("+15 minutes"));
        } else {
            $model->state = ProductEnum::SHOP_STATE_NO_MONEY;
        }

        if ($model->save()) {
            $moderatorLog = new TenderModeratorLog();
            $moderatorLog->product_id = $model->id;
            $moderatorLog->description = "Moderator tasdiqladi";
            $moderatorLog->moderator_pinfl = Yii::$app->user->identity->username;

            if (!($moderatorLog->validate() && $moderatorLog->save())) {
                $transaction->rollBack();
                return $this->render('view', [
                    'model' => $model,
                ]);
            }

            $transaction->commit();
            \Yii::$app->session->setFlash("success", "Успешно moderating");
            return $this->redirect([
                'index',
            ]);
        }

        $transaction->rollBack();
        return $this->render('view', [
            'model' => $model,
        ]);


    }

    public function actionModeratorReject()
    {

        $model = $this->findModel(Yii::$app->request->post()['id']);

        $transaction = \Yii::$app->db->beginTransaction();

        $model->state = ProductEnum::SHOP_STATE_RETURN_MODERATOR;

        if ($model->save()) {
            $moderatorLog = new  TenderModeratorLog();
            $moderatorLog->product_id = $model->id;
            $moderatorLog->description = Yii::$app->request->post()['select'] . " <br> " . Yii::$app->request->post()['description'];
            $moderatorLog->moderator_pinfl = Yii::$app->user->identity->username;

            if (!($moderatorLog->validate() && $moderatorLog->save())) {
                $transaction->rollBack();
                return $this->render('view', [
                    'model' => $model,
                ]);
            }

            $transaction->commit();
            \Yii::$app->session->setFlash("success", "Успешно no moderating");
            return $this->render('view', [
                'model' => $model,
            ]);
        }

        $transaction->rollBack();
        return $this->render('view', [
            'model' => $model,
        ]);


    }

    /**
     * Displays a single Product model.
     * @param int $id ID
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }
    /**
     * Finds the Product model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id ID
     * @return Product the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ProductResource::findOne($id)) !== null) {
            return $model;
        }
        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
