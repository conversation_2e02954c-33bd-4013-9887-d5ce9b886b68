<?php


namespace api\modules\client\resources;


use api\modules\common\resources\FileResource;
use common\models\TenderRequest;

class TenderRequestHistoryResource extends TenderRequest
{
    public function fields()
    {
        return [
            'id',
            'price' => function ($model) {
                return $model->price / 100;
            },
            'price_qqs' => function ($model) {
                return $model->price_qqs / 100;
            },
            'request_state' => function ($model) {
                return $model->state;
            },
            'description',
            'preference_local_producer',
            'preference_local_producer_file' => function ($model) {
                return $model->preferenceFile;
            },
            'created_at',
            'updated_at',
            'tender_id',
            'state' => function ($model) {
                return $model->tender ? $model->tender->state : null;
            }
        ];
    }

    public function extraFields()
    {
        return [
            'tenderRequestValues'
        ];
    }

    public function getTender()
    {
        return $this->hasOne(TenderResource::class, ['id' => 'tender_id']);
    }

    public function getTenderRequestValues()
    {
        return TenderRequestValuesResource::find()->notDeleted()->andWhere(['tender_request_id' => $this->id])->all();
    }

    public function getPreferenceFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'preference_local_producer_file_id']);
    }

}