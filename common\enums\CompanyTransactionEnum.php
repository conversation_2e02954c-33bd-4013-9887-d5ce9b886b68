<?php

namespace common\enums;

interface CompanyTransactionEnum
{
  const TYPE_PAY_TO_CONTRACT = 1; // dogovor bo'yicha to'lov
  const TYPE_PAYED_FROM_CONTRACT = 2; // dogovor bo'yicha qabul
  const TYPE_REVERT_FROM_PRODUCER = 4; // yetkazib beruvchidan pul qaytib qaytarildi
  const TYPE_WITHDRAW = 7; // balansdan schetiga yechib olindi
  const TYPE_REFILL = 8; // balansni to'ldirdi
  const TYPE_PENALTY_IN = 9; // shtraf qabul qildi
  const TYPE_PENALTY_OUT = 10; // shtraf to'ladi
  const TYPE_COMMISSION = 11; // komissiya to'ladi
  const TYPE_BLOCK_PAYMENT_FOR_CONTRACT = 12; // dogovor bo'yicha predoplata yechib olindi
  const TYPE_REVERT_PAYMENT_FOR_CONTRACT = 13; // dogovor bo'yicha predoplata qaytarib berildi
  const TYPE_ZALOG = 14; // zalog olindi
  const TYPE_REVERT_ZALOG = 15; // zalog qaytarildi
  const TYPE_BLOCK_COMMISION = 16; // komissiya olindi
  const TYPE_REVERT_BLOCK_COMMISION = 17; // kommisiya qaytarildi
  const TYPE_DEMPING = 18; // demping olindi
  const TYPE_REVERT_DEMPING = 19; // demping qaytarildi

  const STATUS_SUCCESS = 1;
  const STATUS_WAITING = 2;
  const STATUS_ERROR = 3;
  const STATUS_MODERATION_REQUIRED = 4;
  const STATUS_CANCALLED = 5;

  public const TYPE_LIST = [
    self::TYPE_PAY_TO_CONTRACT,
    self::TYPE_PAYED_FROM_CONTRACT,
    self::TYPE_REVERT_FROM_PRODUCER,
    self::TYPE_WITHDRAW,
    self::TYPE_REFILL,
    self::TYPE_PENALTY_IN,
    self::TYPE_PENALTY_OUT,
    self::TYPE_COMMISSION,
    self::TYPE_BLOCK_PAYMENT_FOR_CONTRACT,
    self::TYPE_REVERT_PAYMENT_FOR_CONTRACT,
    self::TYPE_ZALOG,
    self::TYPE_REVERT_ZALOG,
    self::TYPE_BLOCK_COMMISION,
    self::TYPE_REVERT_BLOCK_COMMISION,
  ];

  public const STATUS_LIST = [
    self::STATUS_SUCCESS,
    self::STATUS_WAITING,
    self::STATUS_ERROR,
    self::STATUS_MODERATION_REQUIRED,
    self::STATUS_CANCALLED,
  ];

  //TODO Pul mablag'larini qaytarish statuslari

   const REFUNDS_STATUS_NEW=1; //moderator tekshiruvida
   const REFUNDS_STATUS_ACCEPT=2; //moderator tasdiqlaganlari
   const REFUNDS_STATUS_REJECTED=3; //moderator rad etganlari

    const PROCEDURE_TYPE_TENDER = 1;
    const PROCEDURE_TYPE_SELECTION = 2;
    const PROCEDURE_TYPE_E_SHOP = 3;
    const PROCEDURE_TYPE_N_SHOP = 4;
    const PROCEDURE_TYPE_AUCTION = 5;

    const PROCEDURE_LIST = [
        CompanyTransactionEnum::PROCEDURE_TYPE_TENDER => 'tender_id',
        CompanyTransactionEnum::PROCEDURE_TYPE_SELECTION => 'tender_id',
        CompanyTransactionEnum::PROCEDURE_TYPE_E_SHOP => 'order_id',
        CompanyTransactionEnum::PROCEDURE_TYPE_N_SHOP => 'order_id',
        CompanyTransactionEnum::PROCEDURE_TYPE_AUCTION => 'auction_id',
    ];

    const PROCEDURE_LIST_LABEL = [
        CompanyTransactionEnum::PROCEDURE_TYPE_TENDER => [
            'uz' => 'Tender',
            'uzk' =>  'Тендер',
            'ru' => 'Тендер',
        ],
        CompanyTransactionEnum::PROCEDURE_TYPE_SELECTION => [
            'uz' => 'Elektron do\'kon', // uz
            'uzk' => 'Электрон дўкон',   // kr
            'ru' => 'Эл.магазин',       // ru
        ],
        CompanyTransactionEnum::PROCEDURE_TYPE_E_SHOP => [
            'uz' => 'Tanlash',
            'uzk' => 'Танлаш',
            'ru' => 'Отбор',
        ],
        CompanyTransactionEnum::PROCEDURE_TYPE_N_SHOP => [
            'uz' => 'Milliy do\'kon',
            'uzk' => 'Миллий дўкон',
            'ru' => 'Нац.магазин',
        ],
        CompanyTransactionEnum::PROCEDURE_TYPE_AUCTION => [
            'uz' => 'Auktsion',
            'uzk' => 'Аукцион',
            'ru' => 'Аукцион',
        ],
    ];
}
