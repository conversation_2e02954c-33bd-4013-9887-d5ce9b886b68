<?php

namespace api\modules\shop\resources;

use common\models\shop\ContractCancelRequest;
use common\models\shop\Order;

class ContractCancelRequestResource extends ContractCancelRequest
{
    public function fields()
    {
        return [
            'type',
            'message',
            'status',
            'created_at',
            'created_by',
            'last_contract_status',
            'id',
        ];
    }

    public function extraFields()
    {
        return [
            'producer',
            'customer',
//            'contract',
//            'user',
        ];
    }

}
