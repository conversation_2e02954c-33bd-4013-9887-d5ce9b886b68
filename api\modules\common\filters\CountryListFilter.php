<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\RegionResource;

class CountryListFilter extends BaseRequest
{
    public $title;

    public function rules()
    {
        return [
            ['title', 'safe'],
        ];
    }

    public function getResult()
    {
        $query = RegionResource::find()->where(['type' => [3, 4]]);
        if ($this->title) {
            $query->andWhere(['or',
                ['like', 'title_uz', $this->title],
                ['like', 'title_ru', $this->title],
                ['like', 'title_uzk', $this->title]
            ]);
        }
        $query->orderBy(['type' => SORT_DESC, 'title_uz' => SORT_ASC]);
        return paginate($query);
    }
}