<?php

namespace api\modules\tender\forms;

use api\components\BaseRequest;
use api\modules\tender\resources\CommissionGroupMemberResource;
use api\modules\tender\resources\CommissionMemberResource;
use api\modules\tender\resources\TenderCommissionMemberResource;
use common\enums\StatusEnum;
use common\enums\TenderEnum;
use yii\base\Exception;

class MemberRemoveAllGroupDeleteForm extends BaseRequest
{

    public $member_id;

    public function rules()
    {
        return [
            ['member_id', 'required', 'message' => t('{attribute} yuborish majburiy')],
            ['member_id', 'integer'],
            [['member_id'], 'exist', 'skipOnError' => true, 'targetClass' => CommissionMemberResource::class, 'targetAttribute' => ['member_id' => 'id']],
        ];
    }

    public function getResult()
    {
        if (!CommissionMemberResource::find()->notDeletedAndFromCompany()->andWhere('id=' . $this->member_id)->exists()) {
            $this->addError('member_id',t("Komissiya a'zosi topilmadi"));
            return false;
        }
        if (TenderCommissionMemberResource::find()->where(['commission_member_id' => $this->member_id, 'status' => TenderEnum::STATUS_ACTIVE])->exists()) {
            $this->addError('member_id',t("Komisiya azosi tenderda ishtirok etgan, barcha guruhlardan o'chirish mumkin emas"));
            return false;
        }
        CommissionGroupMemberResource::updateAll([
            'deleted_at' => date("Y-m-d H:i:s"),
            'updated_by' => \Yii::$app->user->id,
            'status' => TenderEnum::STATUS_DELETED
        ], [
            'status' => TenderEnum::STATUS_ACTIVE,
            'commission_member_id' => $this->member_id
        ]);

        return true;
    }
}