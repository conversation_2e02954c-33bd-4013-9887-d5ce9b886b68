<?php

namespace common\models\shop;

use common\models\Classifier;
use common\models\ClassifierProperties;
use Yii;

/**
 * This is the model class for table "product_draft_classifier_unit".
 *
 * @property int $id
 * @property int|null $product_id
 * @property int|null $classifier_id
 * @property int|null $classifier_properties_id
 *
 * @property Classifier $classifier
 * @property ClassifierProperties $classifierProperties
 * @property ProductDraft $product
 */
class ProductDraftClassifierUnit extends \yii\db\ActiveRecord
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'product_draft_classifier_unit';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['product_id', 'classifier_id', 'classifier_properties_id'], 'default', 'value' => null],
            [['product_id', 'classifier_id', 'classifier_properties_id'], 'integer'],
            [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => Classifier::class, 'targetAttribute' => ['classifier_id' => 'id']],
            [['classifier_properties_id'], 'exist', 'skipOnError' => true, 'targetClass' => ClassifierProperties::class, 'targetAttribute' => ['classifier_properties_id' => 'id']],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => ProductDraft::class, 'targetAttribute' => ['product_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'product_id' => 'Product ID',
            'classifier_id' => 'Classifier ID',
            'classifier_properties_id' => 'Classifier Properties ID',
        ];
    }

    /**
     * Gets query for [[Classifier]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClassifier()
    {
        return $this->hasOne(Classifier::class, ['id' => 'classifier_id']);
    }

    /**
     * Gets query for [[ClassifierProperties]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClassifierProperties()
    {
        return $this->hasOne(ClassifierProperties::class, ['id' => 'classifier_properties_id']);
    }

    /**
     * Gets query for [[Product]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        return $this->hasOne(ProductDraft::class, ['id' => 'product_id']);
    }

}
