<?php

namespace api\modules\shop\controllers;

use api\components\ApiController;
use api\modules\shop\filters\FavoriteListFilter;
use api\modules\shop\forms\FavoriteDeleteForm;
use api\modules\shop\forms\FavoriteForm;
use api\modules\shop\resources\FavoriteResource;
use common\behaviors\RoleAccessBehavior;
use Yii;

/**
 * Default controller for the `shop` module
 */
class FavoriteController extends ApiController
{
    public function behaviors()
    {
        $parent = parent::behaviors();
        $parent['accessByRole'] = [
            'class' => RoleAccessBehavior::class,
            'rules' => [
                'index' => ['user'],
                'create' => ['user'],
                'delete' => ['user'],
            ],
        ];
        return $parent;
    }
    public function actionIndex()
    {
        return $this->sendResponse(
            new FavoriteListFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionCreate()
    {
        return $this->sendResponse(
            new FavoriteForm(new FavoriteResource()),
            Yii::$app->request->bodyParams
        );
    }

    public function actionDelete($id)
    {
        return $this->sendResponse(
            new FavoriteDeleteForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }
    private function findOne($id)
    {
        $model = FavoriteResource::findOne($id);

        if ($model instanceof FavoriteResource){
            return $model;
        }
     throw new \Exception("Product favourite not found");
    }
}
