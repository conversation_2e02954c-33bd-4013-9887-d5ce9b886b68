<?php

namespace api\modules\shop\resources;

use common\models\ProductDraft;
use common\enums\ProductEnum;

class ProductDraftResource extends ProductDraft
{
    public function fields()
    {
        return [
            'id',
            'classifier_category_id',
            'classifier_id',
            'company_id',
            'account_number',
            'title',
            'brand_title',
            'description',
            'year',
            'quantity',
            'price' => 'sumPrice',
            'min_order',
            'max_order' => function ($model) {
                return min($model->quantity, $model->max_order);
            },
            'delivery_period',
            'delivery_period_type',
            'warranty_period',
            'warranty_period_type',
            'expiry_period',
            'expiry_period_type',
            'status',
            'type',
            'unit_price' => 'unitPrice',
            'made_in',
            'platform_display',
            'state',
            'stateName',
            'created_at',
            'country_id',
            'updated_at',
            'unit_id',
            'images',
            'files',
            'unit',
        ];
    }

    public function getSumPrice()
    {
        return $this->price / 100;
    }

    public function getUnitPrice()
    {
        return $this->unit_price / 100;
    }

    public function getStateName()
    {
        return ProductEnum::getShopStateLabels()[$this->state] ?? '';
    }

    public function extraFields()
    {
        return [
            'classifier',
            'classifierCategory',
            'company',
            'files',
            'images',
            'country',
            'unit'
        ];
    }

    public function getIsOwnerProduct()
    {
        return $this->company_id == \Yii::$app->user->identity->company_id;
    }
}