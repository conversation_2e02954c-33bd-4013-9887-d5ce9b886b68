<?php


namespace api\modules\shop\resources;


use common\enums\ProductEnum;
use common\models\shop\ProductDraft;
use yii\helpers\ArrayHelper;

class ProductDraftResource extends ProductDraft
{
    public function fields()
    {
        return [
            'id',
            'classifier_category_id',
            'classifier_id',
            'company_id',
            'title',
            'brand_title',
            'description',
            'year',
            'quantity',
            'price' => 'sumPrice',
            'min_order',
            'max_order' => function ($model) {
                return min($model->quantity, $model->max_order);
            },
            'delivery_period',
            'delivery_period_type',
            'warranty_period',
            'warranty_period_type',
            'expiry_period',
            'expiry_period_type',
            'status',
            'is_have_license',
            'type',
            'unit_price' => 'unitPrice',
            'made_in',
            'platform_display',
            'state',
            'stateName',
//            'delete_reason',
            'created_at',
            'country_id',
            'account_number',
            'deleted_at',
            'active_date',
            'inactive_date',
            'isOwnerProduct',
            'id',
            'unit_id',
            'updated_at',
            'deliveryDate',
            'images',
            'files',
            'unit',
            'warranty',
        ];
    }

    public function getSumPrice()
    {
        return $this->price / 100;
    }

    public function getUnitPrice()
    {
        return $this->unit_price / 100;
    }

    public function extraFields()
    {
        return [
            'classifier',
            'classifierCategory',
            'company',
            'region',
            'files',
            'images',
            'favorite',
            'comments',
            'country',
            'moderatorLog',
            'hasOrder',
            'classifierUnit'
        ];
    }

    public function getStateName()
    {
        return ArrayHelper::getValue(
            [
                ProductEnum::SHOP_STATE_NEW => \Yii::t('app', 'Moderator tekshiruvida'),
                ProductEnum::SHOP_STATE_RETURN_MODERATOR => \Yii::t('app', 'Moderatordan qaytarilgan'),
                ProductEnum::SHOP_STATE_NO_MONEY => \Yii::t('app', 'Mablag` yetarli emas'),
                ProductEnum::SHOP_STATE_ACTIVE => \Yii::t('app', 'Sotuvda'),
                ProductEnum::SHOP_STATE_IN_ACTIVE => \Yii::t('app', 'Aktiv holatda emas'),
                ProductEnum::SHOP_STATE_DELETED => \Yii::t('app', 'O`chirilgan'),
                ProductEnum::SHOP_STATE_CANCEL => \Yii::t('app', 'Rad qilingan'),
            ],
            $this->state);

    }

    public function getDeliveryDate()
    {
        switch ($this->delivery_period_type) {
            case 1 :
            {
                return date("Y-m-d H:i:s", time() + 86400 * $this->delivery_period);
            }
            case 2 :
            {
                return date("Y-m-d H:i:s", time() + 86400 * 30 * $this->delivery_period);
            }
            case 3 :
            {
                return date("Y-m-d H:i:s", time() + 86400 * 365 * $this->delivery_period);
            }
            default:
                return date("Y-m-d H:i:s");
        }

    }

    public function getWarranty()
    {
        return $this->warranty_period . " " . ArrayHelper::getValue(self::getWarrantyList(), $this->warranty_period_type);
    }

    public function getWarrantyList()
    {
        return [
            1 => "год",
            2 => "месяц",
            3 => "день",
        ];
    }

    public function getClassifierUnit()
    {
        return $this->hasMany(ProductClassifierUnitResource::class, ['product_id' => 'id']);
    }
}