<?php


namespace api\modules\shop\forms;


use api\modules\common\resources\VirtualTransactionResource;
use common\enums\OperationTypeEnum;
use common\models\VirtualTransaction;
use Yii;
use api\components\BaseModel;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\shop\resources\ContractCancelRequestResource;
use api\modules\shop\resources\ContractResource;
use api\modules\shop\resources\OrderResource;
use api\modules\shop\resources\ProductResource;
use common\enums\CompanyTransactionEnum;
use common\enums\ContractEnum;
use common\models\CompanyTransaction;
use common\models\shop\ContractCancelRequest;
use yii\base\Exception;
use yii\web\HttpException;
use yii\web\NotFoundHttpException;

class CancelContractForm extends BaseModel
{
    public $customer_id;
    public $producer_id;
    public ?ContractResource $model;
    public $id;


    public function rules()
    {
        return [
            [['id'], 'required'],
            [['customer_id', 'producer_id'], 'safe'],
        ];
    }


    /**
     * @throws Exception
     * @throws NotFoundHttpException
     * @throws HttpException
     * @throws \yii\db\Exception
     * @throws \Throwable
     */
    public function getResult()
    {
        $this->model = ContractResource::findOne(['id' => $this->id, 'deleted_at' => null]);
        if (!$this->model)
            throw new NotFoundHttpException("Product not found");

        if ($this->model->auction_id != null) {
            $productName = OperationTypeEnum::PRODUCT_NAME_AUCTION;
            $productID = $this->model->auction_id;
        } else if ($this->model->order_id != null) {
            $productName = OperationTypeEnum::PRODUCT_NAME_ORDER;
            $productID = $this->model->order_id;
        } else if ($this->model->tender_id != null) {
            $productName = OperationTypeEnum::PRODUCT_NAME_TENDER;
            $productID = $this->model->tender_id;
        } else {
            throw new NotFoundHttpException('Product not found');
        }

        $company_id = Yii::$app->user->identity->company_id;

        if ($this->model->customer_id == $company_id) {
            $this->customer_id = $company_id;
        } else if ($this->model->producer_id == $company_id) {
            $this->producer_id = $company_id;
        } else {
            throw new \yii\web\HttpException(
                403,
                t("Shartnoma sizga tegishli emas")
            );
        }

        if ($this->model->status != ContractEnum::STATUS_CANCEL_PROCESS) {
            throw new Exception(t("Shartnoma bir tomonlama bekor qilish xolatida emas"));
        }


        $transaction = \Yii::$app->db->beginTransaction();

        $this->model->status = ContractEnum::STATUS_CANCEL;
        $this->model->customer_cancel_date = $this->customer_id ? date("Y-m-d H:i:s") : $this->model->customer_cancel_date;
        $this->model->producer_cancel_date = $this->producer_id ? date("Y-m-d H:i:s") : $this->model->producer_cancel_date;

        if ($this->model->attributes && $this->model->validate() && $this->model->save()) {


            $order = OrderResource::findOne($this->model->order_id);
            $product = ProductResource::findOne($order->product_id);

            $contractCancelRequest = ContractCancelRequestResource::find()
                ->where('status=' . ContractEnum::STATUS_REQUEST_NEW)
                ->andWhere(['contract_id' => $this->model->id])
                ->orderBy(['id' => SORT_DESC])->one();
            if (!$contractCancelRequest) {
                $transaction->rollBack();
                throw new Exception(t("Shartnoma bir tomonlama bekor qilish so'rovi topilmadi"));
            }

            $contractCancelRequest->status = ContractEnum::STATUS_REQUEST_DONE;
            if (!$contractCancelRequest->save()) {
                $transaction->rollBack();
                $this->addErrors($contractCancelRequest->errors);
                return false;
            }
            $date = date("Y-m-d H:i:s");


            if ($contractCancelRequest && $contractCancelRequest->type != ContractCancelRequest::TYPE_WITHOUT_PENALTY) {
                //TODO jarimalarni berish
                $customerCompanyID = $this->model->customer_id;
                $producerCompanyID = $this->model->producer_id;
                $producer = $this->model->producer;
                $customer = $this->model->customer;
                $isProducerPenalty = $contractCancelRequest->type == ContractCancelRequest::TYPE_PRODUCER_PENALTY;
                $isCustomerPenalty = !$isProducerPenalty;

                /** @var VirtualTransactionResource  $customerTransaction */
                $customerTransaction = VirtualTransactionResource::findOneByCreditCompanyIDAndProductAndTypeAndNotRefunded(
                    $customerCompanyID,
                    $productName,
                    $productID,
                    null,
                    ["contract_id" => $this->model->id,'operation_type' => OperationTypeEnum::BLOCK_TRANSACTION_DEPOSIT_AGAIN]
                );
                if (!$customerTransaction)
                    throw new NotFoundHttpException("Ushbu contractga tegishli transaksiya mavjud emas.");
                if ($isCustomerPenalty) {
                    $customerTransactionRevertID = VirtualTransaction::saveTransaction(
                        $customer,
                        $producer,
                        OperationtypeEnum::P_K_30301,
                        OperationtypeEnum::P_K_30101,
                        $customerTransaction->credit,
                        'Buyurtmachi shartnoma qabul qilinmagani uchun garov uchun bandlangan summada(jarima) qabul qilib olyapti',
                        $productName,
                        $productID,
                        $customerTransaction->contract_id,
                        OperationTypeEnum::PAY_DEPOSIT_AS_FINE,
                    );
                } else {
                    $customerTransactionRevertID = VirtualTransaction::saveTransaction(
                        $customerTransaction->creditCompany,
                        $customerTransaction->debitCompany,
                        $customerTransaction->creditAccount->prefix_account,
                        $customerTransaction->debitAccount->prefix_account,
                        $customerTransaction->credit,
                        'Shartnoma uchun buyurtmachini zaklat puli blokirovkadan chiqarildi.',
                        $productName,
                        $productID,
                        $customerTransaction->contract_id,
                        OperationTypeEnum::CANCEL_BLOCK_TRANSACTION_DEPOSIT,
                    );
                }
                $customerTransaction->parent_id = $customerTransactionRevertID;
                if (!$customerTransaction->save()) {
                    $transaction->rollBack();
                    $this->addErrors($customerTransaction->errors);
                    return false;
                }

                /** @var VirtualTransactionResource  $producerTransaction */
                $producerTransaction = VirtualTransactionResource::findOneByCreditCompanyIDAndProductAndTypeAndNotRefunded(
                    $producerCompanyID,
                    $productName,
                    $productID,
                    null,
                    ["contract_id" => $this->model->id,'operation_type' => OperationTypeEnum::BLOCK_TRANSACTION_DEPOSIT_AGAIN]
                );
                if (!$producerTransaction)
                    throw new NotFoundHttpException("Ushbu contractga tegishli transaksiya mavjud emas.");
                if ($isProducerPenalty) {
                    $revert = VirtualTransaction::saveTransaction(
                        $producer,
                        $customer,
                        OperationTypeEnum::P_K_30301,
                        OperationTypeEnum::P_K_30101,
                        $producerTransaction->credit,
                        'Shartnoma qabul qilinmagani uchun yetkazib beruvchini garov uchun bandlangan summada(jarima) qabul qilib olyapti',
                        $productName,
                        $productID,
                        $producerTransaction->contract_id,
                        OperationTypeEnum::PAY_DEPOSIT_AS_FINE_SELLER
                    );
                } else {
                    $revert = VirtualTransaction::saveTransaction(
                        $producerTransaction->creditCompany,
                        $producerTransaction->debitCompany,
                        $producerTransaction->creditAccount->prefix_account,
                        $producerTransaction->debitAccount->prefix_account,
                        $producerTransaction->credit,
                        'Shartnoma uchun yetkazib beruvchini zaklat puli blokirovkadan chiqarildi.',
                        $productName,
                        $productID,
                        $producerTransaction->contract_id,
                        OperationTypeEnum::CANCEL_BLOCK_TRANSACTION_DEPOSIT
                    );
                }
                $producerTransaction->parent_id = $revert;
                if (!$producerTransaction->save()) {
                    $transaction->rollBack();
                    $this->addErrors($producerTransaction->errors);
                    return false;
                }
//                if ($contractCancelRequest->type == ContractCancelRequest::TYPE_PRODUCER_PENALTY) {
//                   $company_out_id = $this->model->producer_id;
//                   $company_in_id = $this->model->customer_id;
//                    $transactionOut = CompanyTransaction::find()
//                        ->where(['order_id' => $order->id, 'type' => CompanyTransactionEnum::TYPE_ZALOG, 'status' => CompanyTransactionEnum::STATUS_SUCCESS, 'reverted_id' => null])
//                        ->andWhere(['in', 'company_id', [$company_out_id]])->one();
//                    $transactionOutReverted = CompanyTransaction::find()->where(['order_id' => $order->id, 'type' => CompanyTransactionEnum::TYPE_ZALOG, 'status' => CompanyTransactionEnum::STATUS_SUCCESS, 'reverted_id' => null])
//                        ->andWhere(['in', 'company_id', [$company_out_id, $company_in_id]])->all();
//                    foreach ($transactionOutReverted as $company_transaction1) {
//                        $revert = new CompanyTransaction([
//                            'company_id' => $company_transaction1->company_id,
//                            'contract_id' => $this->model->id,
//                            'order_id' => $company_transaction1->order_id,
//                            'amount' => $company_transaction1->amount,
//                            'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
//                            'description' => Yii::t("main", "Garov bandlashdan chiqarildi."),
//                            'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                            'transaction_date' => $date,
//                        ]);
//                        if ($revert->save()) {
//                            $company_transaction1->reverted_id = $revert->id;
//                            $company_transaction1->save(false);
//                        } else {
//                            $transaction->rollBack();
//                            $this->addErrors($revert->errors);
//                            return false;
//                        }
//                    }
//
//
//                    $company_transaction_penalty_out = new CompanyTransaction([
//                        'company_id' => $company_out_id,
//                        'contract_id' => $this->model->id,
//                        'order_id' => $this->model->order_id,
//                        'auction_id' => $this->model->auction_id,
//                        'amount' => $transactionOut->amount,
//                        'type' => CompanyTransactionEnum::TYPE_PENALTY_OUT,
//                        'description' => \Yii::t("main", "Shartnoma qabul qilmagani uchun garov uchun bandlangan summada jarima beryapti"),
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'transaction_date' => $date,
//                    ]);
////
//                    if (!$company_transaction_penalty_out->save()) {
//                        $this->addErrors($company_transaction_penalty_out->errors);
//                        $transaction->rollBack();
//                        throw new \yii\web\HttpException(
//                            400,
//                            t("Garov uchun tranzaksiya saqlanmadi: ") . $company_transaction_penalty_out->errors
//                        );
//                    }
//
//                    $company_transaction_penalty_in = new CompanyTransaction([
//                        'company_id' => $company_in_id,
//                        'contract_id' => $this->model->id,
//                        'order_id' => $this->model->order_id,
//                        'auction_id' => $this->model->auction_id,
//                        'amount' => $company_transaction_penalty_out->amount,
//                        'type' => CompanyTransactionEnum::TYPE_PENALTY_IN,
//                        'description' => \Yii::t("main", "Shartnoma qabul qilinmagani uchun garov uchun bandlangan summada(jarima) qabul qilib olyapti"),
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'transaction_date' => $date,
//                    ]);
////
//                    if (!$company_transaction_penalty_in->save()) {
//                        $this->addErrors($company_transaction_penalty_in->errors);
//                        $transaction->rollBack();
//                        throw new \yii\web\HttpException(
//                            400,
//                            t("Garov uchun tranzaksiya saqlanmadi: ") . $company_transaction_penalty_in->errors
//                        );
//                    }
//                } else {
//                    $company_out_id = $this->model->customer_id;
//                    $company_in_id = $this->model->producer_id;
//
//                    $transactionOut = CompanyTransaction::find()->where(['order_id' => $order->id, 'type' => CompanyTransactionEnum::TYPE_ZALOG, 'status' => CompanyTransactionEnum::STATUS_SUCCESS, 'reverted_id' => null])
//                        ->andWhere(['in', 'company_id', [$company_out_id]])->one();
//
//                    $transactionOutReverted = CompanyTransaction::find()->where(['order_id' => $order->id, 'type' => CompanyTransactionEnum::TYPE_ZALOG, 'status' => CompanyTransactionEnum::STATUS_SUCCESS, 'reverted_id' => null])
//                        ->andWhere(['in', 'company_id', [$company_out_id, $company_in_id]])->all();
//
//                    foreach ($transactionOutReverted as $company_transaction1) {
//                        $revert = new CompanyTransaction([
//                            'company_id' => $company_transaction1->company_id,
//                            'contract_id' => $this->model->id,
//                            'order_id' => $company_transaction1->order_id,
//                            'amount' => $company_transaction1->amount,
//                            'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
//                            'description' => Yii::t("main", "Garov bandlashdan chiqarildi."),
//                            'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                            'transaction_date' => $date,
//                        ]);
//                        if ($revert->save()) {
//                            $company_transaction1->reverted_id = $revert->id;
//                            $company_transaction1->save(false);
//                        } else {
//                            $transaction->rollBack();
//                            $this->addErrors($revert->errors);
//                            return false;
//                        }
//                    }
//
//                    $company_transaction_penalty_out = new CompanyTransaction([
//                        'company_id' => $company_out_id,
//                        'contract_id' => $this->model->id,
//                        'order_id' => $this->model->order_id,
//                        'amount' => $transactionOut->amount,
//                        'type' => CompanyTransactionEnum::TYPE_PENALTY_OUT,
//                        'description' => \Yii::t("main", "Shartnoma qabul qilmagani uchun garov uchun bandlangan summada jarima beryapti"),
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'transaction_date' => $date,
//                    ]);
//                    if (!$company_transaction_penalty_out->save()) {
//                        $this->addErrors($company_transaction_penalty_out->errors);
//                        $transaction->rollBack();
//                        throw new \yii\web\HttpException(
//                            400,
//                            t("Garov uchun tranzaksiya saqlanmadi: ") . $company_transaction_penalty_out->errors
//                        );
//                    }
//
//                    $company_transaction_penalty_in = new CompanyTransaction([
//                        'company_id' => $company_in_id,
//                        'contract_id' => $this->model->id,
//                        'order_id' => $this->model->order_id,
//                        'amount' => $company_transaction_penalty_out->amount,
//                        'type' => CompanyTransactionEnum::TYPE_PENALTY_IN,
//                        'description' => \Yii::t("main", "Shartnoma qabul qilinmagani uchun garov uchun bandlangan summada(jarima) qabul qilib olyapti"),
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'transaction_date' => $date,
//                    ]);
//                    if (!$company_transaction_penalty_in->save()) {
//                        $this->addErrors($company_transaction_penalty_in->errors);
//                        $transaction->rollBack();
//                        throw new \yii\web\HttpException(
//                            400,
//                            t("Garov uchun tranzaksiya saqlanmadi: ") . $company_transaction_penalty_in->errors
//                        );
//                    }
//                }

            } else {
//                $zalogs = CompanyTransaction::find()->where(['order_id' => $order->id, 'type' => CompanyTransactionEnum::TYPE_ZALOG])->andWhere(['in', 'company_id', [$this->model->customer_id, $this->model->producer_id]])->all();
                $zalogs = VirtualTransactionResource::find()
                    ->where([
                        $productName => $productID,
                        'contract_id' => $this->model->id,
                        'parent_id' => null,
                        'operation_type' => OperationTypeEnum::BLOCK_TRANSACTION_DEPOSIT_AGAIN,
                    ])
                    ->andWhere(['in','credit_company_id',[$this->model->customer_id, $this->model->producer_id]])
                    ->andWhere(['>','credit',0])->all();
                foreach ($zalogs as $company_transaction) {
//                    $revert = new CompanyTransaction([
//                        'company_id' => $company_transaction->company_id,
//                        'contract_id' => $this->model->id,
//                        'order_id' => $company_transaction->order_id,
//                        'amount' => $company_transaction->amount,
//                        'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
//                        'description' => Yii::t("main", "Shartnoma rad etilgani uchun(jarimasiz) zalog summa blokdan chiqarildi"),
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'transaction_date' => date("Y-m-d H:i:s"),
//                    ]);
//                    if ($revert->save()) {
//                        $company_transaction->reverted_id = $revert->id;
//                        $company_transaction->save(false);
//                    } else {
//                        $transaction->rollBack();
//                        $this->addErrors($revert->errors);
//                        return false;
//                    }
                    /** @var VirtualTransaction  $company_transaction */
                    $revertID = VirtualTransaction::saveTransaction(
                        $company_transaction->creditCompany,
                        $company_transaction->debitCompany,
                        $company_transaction->creditAccount->prefix_account,
                        $company_transaction->debitAccount->prefix_account,
                        $company_transaction->credit,
                        Yii::t("main", "Shartnoma rad etilgani uchun(jarimasiz) zalog summa blokdan chiqarildi"),
                        $productName,
                        $productID,
                        $this->model->id,
                        OperationTypeEnum::CANCEL_BLOCK_TRANSACTION_DEPOSIT,
                    );
                    $company_transaction->parent_id = $revertID;
                    if (!$company_transaction->save()) {
                        $this->addErrors($company_transaction->errors);
                        $transaction->rollBack();
                        return false;
                    }
                }
            }

            //Shartnoma to'lovi customerni o'ziga qaytarildi
//            $transactionContractPayment = CompanyTransaction::find()
//                ->where([
//                    'contract_id' => $this->model->id,
//                    'type' => CompanyTransactionEnum::TYPE_BLOCK_PAYMENT_FOR_CONTRACT,
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'reverted_id' => null
//                ])
//                ->andWhere(['company_id' => $this->model->customer_id])->all();
//
//            foreach ($transactionContractPayment as $company_transaction1) {
//                $revert = new CompanyTransaction([
//                    'company_id' => $company_transaction1->company_id,
//                    'contract_id' => $company_transaction1->contract_id,
//                    'auction_id' => $company_transaction1->auction_id,
//                    'order_id' => $company_transaction1->order_id,
//                    'amount' => $company_transaction1->amount,
//                    'type' => CompanyTransactionEnum::TYPE_REVERT_PAYMENT_FOR_CONTRACT,
//                    'description' => Yii::t("main", "Shartnoma to'lovi bandlashdan chiqarildi."),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => $date,
//                ]);
//                if ($revert->save()) {
//                    $company_transaction1->reverted_id = $revert->id;
//                    if (!$company_transaction1->save()) {
//                        $transaction->rollBack();
//                        $this->addErrors($company_transaction1->errors);
//                        return false;
//                    }
//                } else {
//                    $transaction->rollBack();
//                    $this->addErrors($revert->errors);
//                    return false;
//                }
//            }
            $transactionContractPayment = VirtualTransaction::find()
                    ->where([
                        'contract_id' => $this->model->id,
                        $productName => $productID,
                        'parent_id' => null,
                        'credit_company_id' => $this->model->customer_id,
                        "operation_type" => OperationTypeEnum::BLOCK_TRANSACTION_FULL_PAYMENT
                    ])->andWhere([">","credit",0])->one();
            if ($transactionContractPayment)
            {
                /** @var VirtualTransaction $transactionContractPayment  */
                try {
                    $revert = VirtualTransaction::saveTransaction(
                        $transactionContractPayment->creditCompany,
                        $transactionContractPayment->debitCompany,
                        OperationTypeEnum::P_K_30301,
                        OperationTypeEnum::P_K_30101,
                        $transactionContractPayment->credit,
                        Yii::t("main", "Shartnoma to'lovi bandlashdan chiqarildi."),
                        $productName,
                        $productID,
                        $transactionContractPayment->contract_id,
                        OperationTypeEnum::CANCEL_BLOCK_TRANSACTION_FULL_PAYMENT,
                    );
                    $transactionContractPayment->parent_id = $revert;
                    if (!$transactionContractPayment->save()) {
                        $this->addErrors($transactionContractPayment->errors);
                        $transaction->rollBack();
                        return false;
                    }
                } catch (\Throwable $e) {
                    $transaction->rollBack();
                    $this->addError('error', $e->getMessage());
                    return false;
                }
            }
            // komisiya xarajatga o'tkazildi
//            $comissions = CompanyTransaction::find()->where(['order_id' => $this->model->order_id, 'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION, 'reverted_id' => null])
//                ->andWhere(['in', 'company_id', [$this->model->customer_id, $this->model->producer_id]])->all();
//
//
//            foreach ($comissions as $company_transaction1) {
//                $revert = new CompanyTransaction([
//                    'company_id' => $company_transaction1->company_id,
//                    'contract_id' => $this->model->id,
//                    'order_id' => $company_transaction1->order_id,
//                    'amount' => $company_transaction1->amount,
//                    'type' => CompanyTransactionEnum::TYPE_REVERT_BLOCK_COMMISION,
//                    'description' => Yii::t("main", "Shartnoma rad etildi. Komissiya summasi blokdan chiqarildi"),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => $date,
//                ]);
//                if ($revert->save()) {
//                    $company_transaction1->reverted_id = $revert->id;
//                    $company_transaction1->save(false);
//                } else {
//                    $transaction->rollBack();
//                    $this->addErrors($revert->errors);
//                    return false;
//                }
//            }
//
//
//            foreach ($comissions as $company_transaction) {
//                $cc = new CompanyTransaction([
//                    'company_id' => $company_transaction->company_id,
//                    'contract_id' => $this->model->id,
//                    'order_id' => $company_transaction->order_id,
//                    'amount' => $company_transaction->amount,
//                    'type' => CompanyTransactionEnum::TYPE_COMMISSION,
//                    'description' => Yii::t("main", "Shartnoma rad etildi. Komissiya summasi xarajatga o'tkazildi"),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => $date,
//                ]);
//                if (!$cc->save()) {
//                    $transaction->rollBack();
//                    $this->addErrors($cc->errors);
//                    return false;
//                }
//            }

            $commissions = VirtualTransactionResource::find()
                ->where([
                    $productName => $productID,
                    'contract_id' => $this->model->id,
                    'parent_id' => null,
                    "operation_type" => OperationTypeEnum::HOLD_TRANSACTION_COMMISSION
                ])->andWhere(['>', 'credit', 0])->andWhere(["in", "credit_company_id",[$this->model->customer_id, $this->model->producer_id]])->all();
            try {
                foreach ($commissions as $commission) {
                    /** @var VirtualTransaction  $commission */
                    $revert = VirtualTransactionResource::saveTransaction(
                        $commission->creditCompany,
                        _company(),
                        OperationTypeEnum::P_K_30501,
                        OperationTypeEnum::A_50111,
                        $commission->credit,
                        "Shartnoma rad etildi. Komissiya summasi xarajatga o'tkazildi",
                        $productName,
                        $productID,
                        $commission->contract_id,
                        OperationTypeEnum::PAY_TRANSACTION_COMMISSION
                    );
                    $commission->parent_id = $revert;
                    if (!$commission->save()) {
                        $this->addErrors($commission->errors);
                        $transaction->rollBack();
                        break;
                    }
                }
            } catch (Exception $ex) {
                $transaction->rollBack();
                throw $ex;
            }
            $product->updateAttributes([
                'quantity' => $product->quantity + $order->count
            ]);

            $plan_schedule = PlanScheduleClassifierResource::find()->andWhere(['plan_schedule_id' => $order->plan_schedule_id])->andWhere(['classifier_id' => $order->classifier_id])->one();
            if (!$plan_schedule) {
                $transaction->rollBack();
                throw new Exception(t("Reja grafik topilmadi"));
            }

            $plan_schedule->updateAttributes([
                'count_used' => $plan_schedule->count_used - $order->count,
                'count_live' => $plan_schedule->count_live + $order->count,
            ]);
            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}