<?php


namespace api\modules\common\resources;


use common\models\Region;

class DistrictResource extends Region
{
    public function fields()
    {
        return [
            'id',
            'title_uz',
            'title_uzk',
            'title_ru',
            'title_en',
            'parent_id',
            'type'
        ];
    }

    public function extraFields()
    {
        return [
            'region'
        ];
    }

    public function getRegion()
    {
        return $this->hasOne(RegionResource::class, ['id' => 'parent_id']);
    }
}
