<?php

use yii\db\Migration;

class m250717_121034_add_column_in_table_product_draft extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('product_draft' , 'product_draft' , $this->string(255));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('product_draft' , 'product_draft');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250717_121034_add_column_in_table_product_draft cannot be reverted.\n";

        return false;
    }
    */
}
