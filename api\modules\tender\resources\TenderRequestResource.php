<?php


namespace api\modules\tender\resources;


use api\modules\common\resources\FileResource;
use common\enums\TenderEnum;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderRequest;

class TenderRequestResource extends TenderRequest
{

    public function fields()
    {
        return [
            'id',
            'tender_id',
            'price',
            'price_qqs',
            'status',
            'description',
            'preference_local_producer',
            'preference_local_producer_file_id' => function ($model) {
                return $model->file;
            },
            'disclassification_system',
            'disclassification',
            'disclassification_text',
            'conflict_interest',
            'conflict_interest_text',
        ];
    }

    public function extraFields()
    {
        return [
            'tenderRequestValues'
        ];
    }


    public function getTenderRequestValues()
    {
        return TenderRequestValuesResource::find()->where(['tender_request_id' => $this->id, 'status' => TenderEnum::STATUS_ACTIVE])->all();
//        return $this->hasMany(TenderRequestValuesResource::class, ['tender_request_id' => 'id']);
    }

    public function getQualificationSelection()
    {
        return TenderQualificationSelectionResource::find()->where(['tender_request_id' => $this->id, 'status' => TenderEnum::STATUS_ACTIVE])->all();
//        return $this->hasMany(TenderQualificationSelectionResource::class, ['tender_request_id' => 'id']);
    }

    public function getTender()
    {
        return $this->hasOne(TenderResource::class, ['id' => 'tender_id']);
    }

    public function getFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'preference_local_producer_file_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}