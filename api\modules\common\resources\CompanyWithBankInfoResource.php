<?php


namespace api\modules\common\resources;


use common\models\Company;
use common\models\CompanyBankAccount;
use common\models\query\NotDeletedFromCompanyQuery;

class CompanyWithBankInfoResource extends Company
{
    public function fields()
    {
        return [
            'id',
            'tin',
            'pinfl',
            'title',
        ];
    }

    public function extraFields()
    {
        return [
            'companyBankAccount'
        ];
    }

    public function getCompanyBankAccount()
    {
        return $this->hasOne(CompanyBankAccountResource::className(), ['company_id' => 'id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

}