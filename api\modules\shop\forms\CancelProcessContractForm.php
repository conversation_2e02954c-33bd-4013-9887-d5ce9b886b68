<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\shop\resources\ContractResource;
use common\enums\ContractEnum;

class CancelProcessContractForm extends BaseRequest
{
    public ContractResource $model;
    public $type;
    public $customer_id;
    public $producer_id;


    public function __construct(ContractResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }
    public function rules()
    {
        return [
            [['type'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['customer_id', 'producer_id'], 'safe'],
        ];
    }

    public function getResult()
    {

        $transaction = \Yii::$app->db->beginTransaction();

        $this->model->status = ContractEnum::STATUS_CANCEL_PROCESS;
        $this->model->customer_cancel_date = $this->customer_id ? date("Y-m-d H:i:s"):null ;
        $this->model->producer_cancel_date = $this->producer_id ? date("Y-m-d H:i:s"):null ;

        if($this->model->attributes && $this->model->validate() && $this->model->save()){





            $transaction->commit();
            return true;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}