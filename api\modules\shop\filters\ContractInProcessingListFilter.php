<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\shop\resources\CartResource;
use api\modules\shop\resources\ContractResource;
use common\enums\ContractEnum;

class ContractInProcessingListFilter extends BaseRequest
{
    public $producer_id;
    public $customer_id;
    public $order_id;
    public $status;
    public $pageNo = 0;
    public $pageSize = 10;
    public function rules()
    {
        return [
            [['pageNo', 'pageSize'], 'integer'],
            [['customer_id','status','customer_id','order_od'], 'safe']
        ];
    }

    public function getResult()
    {
        $model = ContractResource::find()
            ->andWhere(['customer_id'=>\Yii::$app->user->identity->company_id])
            ->andWhere(['status'=>ContractEnum::STATUS_PROCESSING]);
        if ($this->producer_id) {
            $model->andWhere(['producer_id' => $this->producer_id]);
        }
        if ($this->customer_id) {
            $model->andWhere(['customer_id' => $this->customer_id]);
        }

        if ($this->order_id) {
            $model->andWhere([ 'order_id'=> $this->order_id]);
        }
        $model->orderBy([ 'created_at'=> SORT_DESC]);
        return paginate($model);
    }
}
