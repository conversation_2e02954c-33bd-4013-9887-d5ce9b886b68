<?php

namespace backend\models;

use api\modules\common\resources\FileResource;
use api\modules\shop\resources\FavoriteResource;
use api\modules\shop\resources\ProductCommentResource;
use common\enums\ProductEnum;
use common\models\Classifier;
use common\models\ClassifierCategory;
use common\models\Company;
use common\models\CompanyBankAccount;
use common\models\Region;
use common\models\shop\Cart;
use common\models\shop\OrderList;
use common\models\shop\ProductFile;
use common\models\shop\ProductRegion;
use Yii;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "product".
 *
 * @property int $id
 * @property int|null $classifier_category_id
 * @property int|null $classifier_id
 * @property int|null $company_id
 * @property string|null $account_number
 * @property string|null $title
 * @property string|null $brand_title
 * @property string|null $description
 * @property int|null $year
 * @property int|null $quantity
 * @property int|null $unit_id
 * @property float|null $price
 * @property int|null $min_order
 * @property int|null $max_order
 * @property string|null $type
 * @property int|null $country_id
 * @property string|null $unit_price
 * @property string|null $made_in
 * @property string|null $platform_display
 * @property int|null $state
 * @property int|null $delivery_period
 * @property int|null $delivery_period_type
 * @property int|null $warranty_period
 * @property int|null $warranty_period_type
 * @property int|null $expiry_period
 * @property int|null $expiry_period_type
 * @property int|null $status
 * @property string|null $active_date
 * @property string|null $inactive_date
 * @property int|null $is_have_license
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $deleted_at
 * @property string|null $delete_reason
 * @property int|null $created_by
 * @property int|null $updated_by
 *
 * @property Cart[] $carts
 * @property Classifier $classifier
 * @property ClassifierCategory $classifierCategory
 * @property Company $company
 * @property OrderList[] $orderLists
 * @property ProductFile[] $productFiles
 * @property ProductRegion[] $productRegions
 */
class Product extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'product';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['classifier_category_id', 'classifier_id', 'company_id', 'year', 'quantity', 'unit_id', 'min_order', 'max_order', 'country_id', 'state', 'delivery_period', 'delivery_period_type', 'warranty_period', 'warranty_period_type', 'expiry_period', 'expiry_period_type', 'status', 'is_have_license', 'created_by', 'updated_by'], 'integer'],
            [['description'], 'string'],
            [['price'], 'number'],
            [['active_date', 'inactive_date', 'created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['account_number', 'title', 'brand_title', 'type', 'unit_price', 'made_in', 'platform_display', 'delete_reason'], 'string', 'max' => 255],
            [['classifier_category_id'], 'exist', 'skipOnError' => true, 'targetClass' => ClassifierCategory::class, 'targetAttribute' => ['classifier_category_id' => 'id']],
            [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => Classifier::class, 'targetAttribute' => ['classifier_id' => 'id']],
            [['company_id'], 'exist', 'skipOnError' => true, 'targetClass' => Company::class, 'targetAttribute' => ['company_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => t("ID"),
            'classifier_category_id' => t('Classifier Category ID'),
            'classifier_id' => t('Classifier ID'),
            'company_id' => t('Company ID'),
            'account_number' => t('Account Number'),
            'title' => t('Title'),
            'brand_title' => t('Brand Title'),
            'description' => t('Description'),
            'year' => t('Year'),
            'quantity' => t('Quantity'),
            'unit_id' => t('Unit ID'),
            'price' => t('Price'),
            'min_order' => t('Min Order'),
            'max_order' => t('Max Order'),
            'type' => t('Type'),
            'country_id' => t('Country ID'),
            'unit_price' => t('Unit Price'),
            'made_in' => t('Made In'),
            'platform_display' => t('Platform Display'),
            'state' => t('State'),
            'delivery_period' => t('Delivery Period'),
            'delivery_period_type' => t('Delivery Period Type'),
            'warranty_period' => t('Warranty Period'),
            'warranty_period_type' => t('Warranty Period Type'),
            'expiry_period' => t('Expiry Period'),
            'expiry_period_type' => t('Expiry Period Type'),
            'status' => t('Status'),
            'active_date' => t('Active Date'),
            'inactive_date' => t('Inactive Date'),
            'is_have_license' => t('Is Have License'),
            'created_at' => t('Created At'),
            'updated_at' => t('Updated At'),
            'deleted_at' => t('Deleted At'),
            'delete_reason' => t('Delete Reason'),
            'created_by' => t('Created By'),
            'updated_by' => t('Updated By'),
        ];
    }

    /**
     * Gets query for [[Carts]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCarts()
    {
        return $this->hasMany(Cart::class, ['product_id' => 'id']);
    }

    public function getBankAccount()
    {
        return $this->hasOne(CompanyBankAccount::class, ['id' => 'account_number']);
    }
    /**
     * Gets query for [[Classifier]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClassifier()
    {
        return $this->hasOne(Classifier::class, ['id' => 'classifier_id']);
    }

    /**
     * Gets query for [[ClassifierCategory]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getClassifierCategory()
    {
        return $this->hasOne(ClassifierCategory::class, ['id' => 'classifier_category_id']);
    }

    /**
     * Gets query for [[Company]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCompany()
    {
        return $this->hasOne(Company::class, ['id' => 'company_id']);
    }

    /**
     * Gets query for [[OrderLists]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getOrderLists()
    {
        return $this->hasMany(OrderList::class, ['product_id' => 'id']);
    }


    /**
     * Gets query for [[ProductRegions]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProductRegions()
    {
        return $this->hasMany(ProductRegion::class, ['product_id' => 'id']);
    }
    public function getStateName()
    {
        return  ArrayHelper::getValue(
            [
                ProductEnum::SHOP_STATE_NEW=>\Yii::t('app','Moderator tekshiruvida'),
                ProductEnum::SHOP_STATE_RETURN_MODERATOR =>\Yii::t('app','Moderatordan qaytarilgan'),
                ProductEnum::SHOP_STATE_NO_MONEY=>\Yii::t('app','Mablag` yetarli emas'),
                ProductEnum::SHOP_STATE_ACTIVE=>\Yii::t('app','Sotuvda'),
                ProductEnum::SHOP_STATE_IN_ACTIVE=>\Yii::t('app','Aktiv holatda emas'),
                ProductEnum::SHOP_STATE_DELETED=>\Yii::t('app','O`chirilgan'),
            ],
            $this->state);

    }
    public function fields()
    {
        return [
          'stateName'
        ];
    }
    /**
     * Gets query for [[ProductFiles]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProductFiles()
    {
        return $this->hasMany(ProductFile::class, ['product_id' => 'id'])->andWhere([ProductFile::tableName().'.type'=>ProductFile::TYPE_FILE]);
    }
    /**
     * Gets query for [[ProductFiles]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProductImages()
    {
        return $this->hasMany(ProductFile::class, ['product_id' => 'id'])->andWhere([ProductFile::tableName().'.type'=>ProductFile::TYPE_IMAGE]);
    }
    public function getFiles()
    {
        return $this->hasMany(FileResource::class, ['id' => 'file_id'])->via('productFiles');
    }
    public function getImages()
    {
        return $this->hasMany(FileResource::class, ['id' => 'file_id'])->via('productImages');
    }
    public function getFavorite()
    {
        return $this->hasOne(FavoriteResource::class, ['product_id' => 'id'])->andWhere(['user_id'=>Yii::$app->user->id]);
    }
    public function getComments()
    {
        return $this->hasMany(ProductCommentResource::class, ['product_id' => 'id']);
    }
    /**
     * Gets query for [[Region]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getRegion()
    {
        return $this->hasMany(Region::class, ['id' => 'region_id'])->via('productRegions');
    }
}
