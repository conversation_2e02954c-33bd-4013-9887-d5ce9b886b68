<?php

namespace api\modules\tender\forms;

use api\components\BaseRequest;
use api\modules\tender\resources\CommissionGroupMemberResource;
use api\modules\tender\resources\CommissionGroupResource;
use api\modules\tender\resources\TenderCommissionMemberResource;
use common\enums\TenderEnum;

class CommissionGroupDeleteForm extends BaseRequest {
    public CommissionGroupResource $model;

    public function __construct(CommissionGroupResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function getResult()
    {

        if(TenderCommissionMemberResource::find()->where(['commission_group_member_id' => $this->model->id, 'company_id' => \Yii::$app->user->identity->company_id])->exists()){
            $this->addError("id", t("Guruh azosi tenderga a'zo qilingan, o'chirish mumkin emas"));
            return false;
        }
        $this->model->deleted_at = date("Y-m-d H:i:s");
        $this->model->updated_by = \Yii::$app->user->id;
        if($this->model->save()){
            return true;
        }
        $this->addErrors($this->model->errors);
        return false;
    }
}