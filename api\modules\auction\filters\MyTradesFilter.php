<?php

namespace api\modules\auction\filters;

use api\components\BaseRequest;
use api\modules\auction\resources\AuctionMyLotsResource;
use api\modules\auction\resources\AuctionOfferResource;
use api\modules\auction\resources\AuctionResource;
use common\models\User;
use Yii;
use yii\helpers\ArrayHelper;

class MyTradesFilter extends BaseRequest
{
  public $name;
  public $region_id;
  public $summa_from;
  public $summa_to;
  public $product_id;
  public $auction_end;

  public function rules()
  {
    return [
      ['name', 'safe'],
      ['region_id', 'safe'],
      ['summa_from', 'safe'],
      ['summa_to', 'safe'],
      ['product_id', 'safe'],
      ['auction_end', 'safe'],
    ];
  }

  public function getResult()
  {

      $requesIds = ArrayHelper::getColumn(AuctionOfferResource::findAll(['company_id' => Yii::$app->user->identity->company_id]), 'auction_id');

    $query = AuctionMyLotsResource::find()
      ->where(['id' => $requesIds])
      ->orderBy("created_at desc");

    // if ($params['name']) {
    //   $query->andWhere([
    //     'or',
    //     ['auction.id' => $params['name']],
    //     ['like', 'tn_translate.title', $params['name']]
    //   ]);
    // }

    // if ($params['region_id']) {
    //   $query->andWhere(['auction.region_id' => $params['region_id']]);
    // }

    // if ($params['summa_from']) {
    //   $query->andWhere(['>=', 'auction.total_sum', $params['summa_from']]);
    // }

    // if ($params['summa_to']) {
    //   $query->andWhere(['<=', 'auction.total_sum', $params['summa_to']]);
    // }

    // if ($params['product_id']) {
    //   $query->andWhere(['auction_category.category_id' => $params['product_id']]);
    // }

    // if ($params['auction_end']) {
    //   $query->andWhere(['DATE(auction.auction_id)' => $params['auction_end']]);
    // }

    return paginate($query);
  }
}
