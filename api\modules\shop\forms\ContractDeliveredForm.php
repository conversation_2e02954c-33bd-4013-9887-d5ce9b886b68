<?php


namespace api\modules\shop\forms;


use api\modules\common\resources\VirtualTransactionResource;
use common\enums\OperationTypeEnum;
use common\models\VirtualTransaction;
use Yii;
use api\components\BaseModel;
use api\modules\shop\resources\ContractResource;
use common\enums\CompanyTransactionEnum;
use common\enums\ContractEnum;
use common\models\CompanyTransaction;
use yii\db\Exception;
use yii\web\NotFoundHttpException;

class ContractDeliveredForm extends BaseModel
{

    public ?ContractResource $model;
    public $factureFile;
    public $id;

    public function rules()
    {
        return [
            [['id'],'required'],
            [['factureFile'], 'safe'],
        ];
    }


    /**
     * @throws Exception
     * @throws NotFoundHttpException
     * @throws \yii\base\Exception
     * @throws \Throwable
     */
    public function getResult()
    {
        $this->model = ContractResource::findOne(['id' => $this->id, 'deleted_at' => null]);
        if (!$this->model)
            throw new NotFoundHttpException("Product not found");

        $company_id = \Yii::$app->user->identity->company_id;
        if ($this->model->customer_id != $company_id) {
            $this->addError("error", t("Shartnoma sizga tegishli emas"));
            return false;
        }
        if ($this->model->status != ContractEnum::STATUS_PAYMENT_END) {
            $this->addError("error", t("Shartnoma yakunlash uchun muqobil statusda emas"));
            return false;
        }

        $transaction = \Yii::$app->db->beginTransaction();
        $date = date("Y-m-d H:i:s");

        $this->model->status = ContractEnum::STATUS_DONE;
        $this->model->customer_mark_delivered_date = $date;
        $this->model->facture_file_id = $this->factureFile;
//        $att = $this->attributes;
//        $this->model->setAttributes($att,false); //$this->model->attributes && $this->model->validate() &&
        if ($this->model->save()) {

            //TODO zaloglarni qaytarish
            $zalogSum = 0;
//            $zalogs = CompanyTransaction::find()->where(['order_id' => $this->model->order_id, 'type' => CompanyTransactionEnum::TYPE_ZALOG, 'reverted_id' => null])->andWhere(['in', 'company_id', [$this->model->customer_id, $this->model->producer_id]])->all();
//
//            foreach ($zalogs as $company_transaction) {
//                $revert = new CompanyTransaction([
//                    'company_id' => $company_transaction->company_id,
//                    'contract_id' => $this->model->id,
//                    'order_id' => $company_transaction->order_id,
//                    'amount' => $company_transaction->amount,
//                    'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
//                    'description' => Yii::t("main", "Shartnoma to'liq tuzilganligi uchun zalog qaytarildi"),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => $date,
//                ]);
//                if ($revert->save()) {
//                    $company_transaction->reverted_id = $revert->id;
//                    $company_transaction->save(false);
//                } else {
//                    $transaction->rollBack();
//                    $this->addErrors($revert->errors);
//                    return false;
//                }
//                $zalogSum = $company_transaction->amount;
//            }
//
//            // komisiya xarajatga o'tkazildi
//            $comissions = CompanyTransaction::find()->where(['order_id' => $this->model->order_id, 'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION, 'reverted_id' => null])
//                ->andWhere(['in', 'company_id', [$this->model->customer_id, $this->model->producer_id]])->all();
//
//
//            foreach ($comissions as $company_transaction1) {
//                $revert = new CompanyTransaction([
//                    'company_id' => $company_transaction1->company_id,
//                    'contract_id' => $this->model->id,
//                    'order_id' => $company_transaction1->order_id,
//                    'amount' => $company_transaction1->amount,
//                    'type' => CompanyTransactionEnum::TYPE_REVERT_BLOCK_COMMISION,
//                    'description' => Yii::t("main", "Shartnoma to'liq tuzilganligi uchun komissiya summasi blokdan chiqarildi"),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => $date,
//                ]);
//                if ($revert->save()) {
//                    $company_transaction1->reverted_id = $revert->id;
//                    $company_transaction1->save(false);
//                } else {
//                    $transaction->rollBack();
//                    $this->addErrors($revert->errors);
//                    return false;
//                }
//
//                $cc = new CompanyTransaction([
//                    'company_id' => $company_transaction1->company_id,
//                    'contract_id' => $this->model->id,
//                    'order_id' => $company_transaction1->order_id,
//                    'amount' => $company_transaction1->amount,
//                    'type' => CompanyTransactionEnum::TYPE_COMMISSION,
//                    'description' => Yii::t("main", "Shartnoma to'liq tuzilganligi uchun komissiya xarajatga o'tdi"),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => $date,
//                ]);
//                if (!$cc->save()) {
//                    $transaction->rollBack();
//                    $this->addErrors($cc->errors);
//                    return false;
//                }
//            }


            $zalogs = VirtualTransactionResource::find()
                ->where([
                    "order_id" => $this->model->order_id,
                    "parent_id" => null,
                    "contract_id" => $this->model->id,
                    "operation_type" => OperationTypeEnum::HOLD_TRANSACTION_COMMISSION,
                ])
                ->andWhere(['>', 'credit', 0])
                ->andWhere(['in','credit_company_id', [$this->model->customer, $this->model->producer]])->all();
            foreach ($zalogs as $company_transaction) {
                /** @var VirtualTransaction  $company_transaction */
                $_company = $company_transaction->creditCompany;
                $revert = VirtualTransaction::saveTransaction(
                    $_company,
                    _company(),
                    $_company->isBudget() ? OperationTypeEnum::P_B_31501 : OperationTypeEnum::P_K_30501,
                    $_company->isBudget() ? OperationTypeEnum::A_50113 : OperationTypeEnum::A_50111,
                    $company_transaction->credit,
                    "Shartnoma to'liq tuzilganligi uchun komissiya xarajatga o'tdi",
                    OperationTypeEnum::PRODUCT_NAME_ORDER,
                    $this->model->order_id,
                    $company_transaction->contract_id,
                );
                $company_transaction->parent_id = $revert;
                if (!$company_transaction->save()) {
                    $transaction->rollBack();
                    $this->addErrors($company_transaction->getErrors());
                    return false;
                }
                $zalogSum = $company_transaction->credit;
            }

            // TODO tolov qilganni otkazish

//            $rkp = CompanyTransaction::findOne([
//                'company_id' => $company_id,
//                'contract_id' => $this->model->id,
//                'type' => CompanyTransactionEnum::TYPE_BLOCK_PAYMENT_FOR_CONTRACT,
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS
//            ]);
//
//            if ($rkp) {
//                $rkp_back = new CompanyTransaction([
//                    'company_id' => $this->model->customer_id,
//                    'amount' => $rkp->amount,
//                    'order_id' => $rkp->order_id,
//                    'contract_id' => $this->model->id,
//                    'type' => CompanyTransactionEnum::TYPE_REVERT_PAYMENT_FOR_CONTRACT,
//                    'description' => Yii::t("main", "Yetkazib berilganligi uchun shartnoma summasi blokdan chiqarildi"),
//                    'transaction_date' => $date,
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS
//                ]);
//                if ($rkp_back->save()) {
//                    $rkp->reverted_id = $rkp_back->id;
//                    $rkp->save(false);
//
//                    $outgoingToBank = new CompanyTransaction([
//                        'company_id' => $rkp->company_id,
//                        'contract_id' => $this->model->id,
//                        'order_id' => $rkp->order_id,
//                        'amount' => $rkp->amount + $zalogSum,
//                        'type' => CompanyTransactionEnum::TYPE_PAY_TO_CONTRACT,
//                        'description' => Yii::t("main", "Shartnoma summasi bank xisob raqamga chiqib ketdi"),
//                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                        'transaction_date' => $date,
//                    ]);
//
//                    if (!$outgoingToBank->save()) {
//                        $this->addErrors($outgoingToBank->errors);
//                        $transaction->rollBack();
//                        return false;
//                    }
//
//                }
//            }

            $rkp = VirtualTransactionResource::find()->where([
                'contract_id' => $this->model->id,
                'credit_company_id'  => $company_id,
                'operation_type' => OperationTypeEnum::BLOCK_TRANSACTION_DEPOSIT_AGAIN,
                'order_id' => $this->model->order_id,
            ])->andWhere(['>','credit',0])->one();
            if ($rkp) {
                $revert = VirtualTransaction::saveTransaction(
                     $this->model->customer,
                     $this->model->producer,
                     OperationTypeEnum::P_K_30301,
                     OperationTypeEnum::P_K_30101,
                    $rkp->credit + $zalogSum,
                    "Shartnoma bo'yicha to'lov to'landi.",
                    OperationTypeEnum::PRODUCT_NAME_ORDER,
                    $this->model->order_id,
                    $this->model->id,
                    OperationTypeEnum::PAY_FOR_DELIVERED_GOODS,
                );
                $rkp->parent_id = $revert;
                if (!$rkp->save()) {
                    $transaction->rollBack();
                    $this->addErrors($rkp->getErrors());
                    return false;
                }
            }

            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}