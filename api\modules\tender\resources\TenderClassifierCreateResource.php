<?php


namespace api\modules\tender\resources;


use common\enums\TenderEnum;
use common\models\Classifier;
use common\models\PlanSchedule;
use common\models\PlanScheduleClassifier;
use common\models\Tender;
use common\models\TenderClassifier;
use Yii;

class TenderClassifierCreateResource extends TenderClassifier
{

    public function rules()
    {
        return [
            [['tender_id', 'classifier_id', 'plan_schedule_classifier_id', 'number_purchased', 'expiry_date_value', 'expiry_date_unit', 'delivery_period', 'price'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['tender_id', 'classifier_id', 'plan_schedule_classifier_id', 'number_purchased', 'additional_charges_non_residents', 'unit_id', 'expiry_date_value', 'expiry_date_unit', 'delivery_period', 'created_by', 'updated_by'], 'integer'],
            [['price'], 'number'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['additional_charges_amount'], 'string', 'max' => 255],
            [['additional_charges_amount'], 'required', 'when' => function ($model) {
                return $this->additional_charges_non_residents == TenderEnum::NON_RESIDENT_ADDITIONAL_YES;
            }],
            [['description'], 'string', 'max' => 512],
            ['additional_charges_non_residents', 'checkAdditionalChargesNonResidents'],
            [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => Classifier::class, 'targetAttribute' => ['classifier_id' => 'id']],
            [['plan_schedule_classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => PlanScheduleClassifier::class, 'targetAttribute' => ['plan_schedule_classifier_id' => 'id']],
            [['tender_id'], 'exist', 'skipOnError' => true, 'targetClass' => Tender::class, 'targetAttribute' => ['tender_id' => 'id']],
        ];
    }

    public function checkAdditionalChargesNonResidents()
    {
        if ($this->additional_charges_non_residents == TenderEnum::NON_RESIDENT_ADDITIONAL_YES || $this->additional_charges_non_residents == TenderEnum::NON_RESIDENT_ADDITIONAL_NO) {
            return true;
        } else {
            $this->addError('additional_charges_non_residents', t("Norezidentlar uchun qo'shimcha xarajatlar qo'shiladi maydoni qiymati noto'g'ri yuborildi"));
            return false;
        }
    }

}