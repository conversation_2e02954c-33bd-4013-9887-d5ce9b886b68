<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\widgets\DetailView;

/**
 * @var yii\web\View $this
 * @var common\models\auction\Auction $model
 */

$this->title = $model->id;
$this->params['breadcrumbs'][] = ['label' => t('Auction'), 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="auction-view">
    <div class="card">
        <div class="card-header">
            <?php
            if (Yii::$app->user->can('moderator') && ($model->status == \common\enums\AuctionEnum::STATUS_MODERATING)) {
                echo Html::a(t('moderator-accept'), ['moderator-accept', 'id' => $model->id], ['class' => 'btn btn-primary mx-2', 'data-toggle' => "modal", 'data-id' => $model->id, 'data-target' => "#moderatorAccept"]);
                echo Html::a(t('moderator-reject'), ['moderator-reject', 'id' => $model->id], ['class' => 'btn btn-danger mx-2', 'data-toggle' => "modal", 'data-id' => $model->id, 'data-target' => "#moderatorReject"]);
            }
            ?>
            <?php
            if (Yii::$app->user->can('moderator') && ($model->status == \common\enums\AuctionEnum::STATUS_REJECTED)) {
                echo Html::a(t('moderator-accept'), ['moderator-accept', 'id' => $model->id], ['class' => 'btn btn-primary ', 'data-toggle' => "modal", 'data-id' => $model->id, 'data-target' => "#moderatorAccept"]);
            }

            if (Yii::$app->user->can('moderator') && ($model->status == \common\enums\AuctionEnum::STATUS_DMBAT)) {
                echo Html::a(t('DMBAT'), ['dmbat', 'id' => $model->id], ['class' => 'btn btn-primary ']);
                echo Html::a(t('DMBAT Reject'), ['dmbat-reject', 'id' => $model->id], ['class' => 'btn btn-danger mx-2', 'data-toggle' => "modal", 'data-id' => $model->id, 'data-target' => "#dmbatReject"]);
            }
            ?>
        </div>
        <div class="card-body">
            <?php echo DetailView::widget([
                'model' => $model,
                'attributes' => [
                    'id',
                    'lot',
                    'company.title',
                    'classifierCategory.title_uz',
                    'status',
                    [
                        'attribute' => 'total_sum',
                        'value' => function ($model) {
                            return $model->total_sum / 100;
                        }
                    ],
                    'cancel_reason',
                    'auction_end',
                    'cancel_date',
                    'payment_status',
                    'payment_date',
                    'delivery_period',
                    'payment_period',
                    'receiver_email:email',
                    'receiver_phone',
                    'region.title_uz',
                    'zip_code',
                    'address',
                    'created_at',
                    'updated_at',
                    'deleted_at',
                    'created_by',
                    'updated_by',
                    'account',
                    'plan_schedule_id',

                ],
            ]) ?>
        </div>
        <div class="row">
            <div class="col-md-1"></div>
            <div class="col-md-4">
                <h3 style="display:block; ">    <?= t("Fayllar") ?></h3>
                <?php foreach ($model->auctionFiles as $key => $files) { ?>

                    <button class="btn btn-success">
                        <a style="color: white"
                           href="<?= env('STORAGE_URL', 'https://xarid-storage.ebirja.uz') . '/source/' . $files->file->day . $files->file->path ?>"> <?= t("Download") ?></a>
                    </button>

                <?php } ?>
            </div>
            <div class="col-md-6">
                <h3 style="display:block; ">    <?= t("Mahsulotlar") ?></h3>

                <table class="table">
                    <thead>
                    <th>
                        <?= t("№") ?>
                    </th>
                    <th>
                        <?= t("Name") ?>
                    </th>
                    <th>
                        <?= t("Quantity") ?>
                    </th>
                    <th>
                        <?= t("Price") ?>
                    </th>
                    <th>
                        <?= t("Total Sum") ?>
                    </th>
                    </thead>
                    <tbody>
                    <?php foreach ($model->auctionClassifiers as $key => $auctionClassifiers) { ?>
                        <tr>
                            <td>
                                <?= $key + 1 ?>
                            </td>
                            <td>
                                <a href="<?= \yii\helpers\Url::to(['classifier/view', 'id' => $auctionClassifiers->classifier->id]) ?>"> <?= $auctionClassifiers->classifier->title_uz ?></a>
                            </td>
                            <td>
                                <?= $auctionClassifiers->quantity ?>
                            </td>
                            <td>
                                <?= number_format($auctionClassifiers->price / 100) ?>
                            </td>
                            <td>
                                <?= number_format($auctionClassifiers->total_sum / 100) ?>
                            </td>
                        </tr>
                    <?php } ?>
                    </tbody>
                </table>


            </div>
            <div class="col-md-1"></div>
        </div>
    </div>
</div>

<div class="modal fade" id="moderatorAccept" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h1><?= t("Moderator tomonidan tasdiqlash") ?></h1>
            </div>
            <div class="modal-body">
                <?php
                $form = ActiveForm::begin(
                    ['action' => ['auction/moderator-accept'], 'options' => ['method' => 'post']]
                ); ?>

                <div class="row">
                    <div class="form-group col-sm-12 col-md-4 modal-col" style="margin-bottom: 0 !important;">

                        <div class="form-group" style="  text-align: center">
                            <input type="hidden" name="id" value="<?= $model->id ?>">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal"><?= t("Close") ?></button>
                <?= Html::submitButton(t("Accept"), ['class' => 'btn btn-primary']) ?>
            </div>

            <?php ActiveForm::end(); ?>
        </div>
    </div>
</div>
<div class="modal fade" id="moderatorReject" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h1><?= t("Moderator tomonidan rad etish") ?></h1>
            </div>
            <div class="modal-body">
                <?php
                $form = ActiveForm::begin(
                    ['action' => ['auction/moderator-reject'], 'options' => ['method' => 'post']]
                ); ?>

                <div class="row">
                    <div class="form-group col-sm-12  col-md-12 modal-col" style="margin-bottom: 0 !important;">
                        <select name="select" class="form-control">
                            <option value="CANCEL_1"><?= t("CANCEL_1"); ?></option>
                            <option value="CANCEL_2"><?= t("CANCEL_2"); ?></option>
                            <option value="CANCEL_3"><?= t("CANCEL_3"); ?></option>
                            <option value="CANCEL_4"><?= t("CANCEL_4"); ?></option>
                            <option value="CANCEL_5"><?= t("CANCEL_5"); ?></option>
                            <option value="CANCEL_6"><?= t("CANCEL_6"); ?></option>
                            <option value="CANCEL_7"><?= t("CANCEL_7"); ?></option>
                            <option value="CANCEL_8"><?= t("CANCEL_8"); ?></option>
                            <option value="CANCEL_9"><?= t("CANCEL_9"); ?></option>
                            <option value="CANCEL_10"><?= t("CANCEL_10"); ?></option>
                            <option value="CANCEL_11"><?= t("CANCEL_11"); ?></option>
                            <option value="CANCEL_12"><?= t("CANCEL_12"); ?></option>
                            <option value="CANCEL_13"><?= t("CANCEL_13"); ?></option>
                            <option value="CANCEL_14"><?= t("CANCEL_14"); ?></option>
                            <option value="CANCEL_15"><?= t("CANCEL_15"); ?></option>
                            <option value="CANCEL_16"><?= t("CANCEL_16"); ?></option>
                            <option value="CANCEL_17"><?= t("CANCEL_17"); ?></option>
                            <option value="CANCEL_18"><?= t("CANCEL_18"); ?></option>
                            <option value="CANCEL_19"><?= t("CANCEL_19"); ?></option>
                            <option value="CANCEL_20"><?= t("CANCEL_20"); ?></option>
                            <option value="CANCEL_21"><?= t("CANCEL_21"); ?></option>
                        </select>
                    </div>
                    <div class="form-group col-sm-12 col-md-12 modal-col mt-4" style="margin-bottom: 0 !important;">
                        <textarea type="text" name="description" class="form-control"></textarea>
                    </div>

                    <div class="form-group col-sm-12 col-md-4 modal-col" style="margin-bottom: 0 !important;">
                        <div class="form-group" style="  text-align: center">
                            <input type="hidden" name="id" value="<?= $model->id ?>">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal"><?= t("Close") ?></button>
                <?= Html::submitButton(t("Reject"), ['class' => 'btn btn-danger']) ?>
            </div>

            <?php ActiveForm::end(); ?>
        </div>
    </div>
</div>

<div class="modal fade" id="dmbatReject" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h1><?= t("DMBAT tomonidan rad etish") ?></h1>
            </div>
            <div class="modal-body">
                <?php
                $form = ActiveForm::begin(
                    ['action' => ['auction/dmbat-reject'], 'options' => ['method' => 'post']]
                ); ?>

                <div class="row">
                    <div class="form-group col-sm-12 col-md-12 modal-col mt-4" style="margin-bottom: 0 !important;">
                        <textarea type="text" name="description" class="form-control"></textarea>
                    </div>

                    <div class="form-group col-sm-12 col-md-4 modal-col" style="margin-bottom: 0 !important;">
                        <div class="form-group" style="  text-align: center">
                            <input type="hidden" name="id" value="<?= $model->id ?>">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal"><?= t("Close") ?></button>
                <?= Html::submitButton(t("Reject"), ['class' => 'btn btn-danger']) ?>
            </div>

            <?php ActiveForm::end(); ?>
        </div>
    </div>
</div>

