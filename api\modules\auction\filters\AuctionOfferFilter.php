<?php


namespace api\modules\auction\filters;


use api\components\BaseRequest;
use api\modules\auction\resources\AuctionActiveResource;
use api\modules\auction\resources\AuctionOfferResource;

class AuctionOfferFilter extends BaseRequest
{
    public $id;
    public AuctionActiveResource $model;

    public function __construct(AuctionActiveResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function getResult()
    {
        return AuctionOfferResource::find()->where(['auction_id' => $this->model->id, 'deleted_at' => null])->orderBy(['created_at' => SORT_DESC])->all();
    }

}