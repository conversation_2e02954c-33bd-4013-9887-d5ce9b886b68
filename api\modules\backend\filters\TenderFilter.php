<?php


namespace api\modules\backend\filters;


use api\components\BaseRequest;
use api\modules\backend\resources\TenderResource;
use common\enums\TenderEnum;

class TenderFilter extends BaseRequest
{
    public $pageNo = 0;
    public $pageSize = 10;

    public function rules (){
        return [
            [['pageNo', 'pageSize'], 'integer'],
        ];
    }

    public function getResult()
    {
        $model = TenderResource::find()->where('state='.TenderEnum::STATE_NEW);

        $pagination = new \yii\data\Pagination([
            'totalCount' => $model->count(),
            'pageSize' => $this->pageSize,
            'page' => $this->pageNo
        ]);

        $model->offset($pagination->offset);
        $model->limit($pagination->pageSize);

        return [
            'meta' => [
                'pageCount' => $pagination->pageCount,
                'pageSize' => $pagination->pageSize,
                'pageNo' => $pagination->page,
                'totalCount' => $pagination->totalCount
            ],
            'data' => $model->all(),

        ];


    }
}