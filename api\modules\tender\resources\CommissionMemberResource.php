<?php

namespace api\modules\tender\resources;

use common\models\CommissionMember;
use common\models\query\NotDeletedFromCompanyQuery;

class CommissionMemberResource extends CommissionMember {

    public function fields (){
        return ['id', 'pinfl', 'fullname', 'passport_serie', 'passport_number', 'mail', 'phone', 'birthday', 'position', 'company_name', 'status', 'created_at', 'reacted_at'];
    }

    public function extraFields() {
        return [
            'commissionGroupMembers',
            'tenderCommissionMembers',
        ];
    }

    public function getCommissionGroupMembers (){
        return CommissionGroupMemberResource::find()->notDeleted()->andWhere(['commission_member_id' => $this->id])->all();
//        return $this->hasMany(CommissionGroupMemberResource::class, ['commission_member_id' => 'id']);
    }

    public function getTenderCommissionMembers()
    {
        return $this->hasMany(TenderCommissionMemberResource::class, ['commission_member_id' => 'id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}