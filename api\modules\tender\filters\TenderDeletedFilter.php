<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;
use Yii;

class TenderDeletedFilter extends BaseRequest
{
    public $lot;

    public function rules()
    {
        return [
            [['lot'], 'safe'],
        ];
    }

    public function getResult()
    {
        $model = TenderResource::find()->where('company_id=' . Yii::$app->user->identity->company_id);
        $model->andWhere(['state' => TenderEnum::STATE_CANCELLED]);
        if ($this->lot) {
            $model->andWhere(['like', 'lot', $this->lot]);
        }

        return paginate($model);


    }
}