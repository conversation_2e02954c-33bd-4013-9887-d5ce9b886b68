<?php


namespace backend\modules\admin\resources;

use common\models\Tender;

class TenderResource extends Tender
{
    public function fields(){
        return [
            'id',
            'type',
            'created_at',
            'updated_at',
            'tenderTotalPrice' => function($model){
                return $model->getTenderTotalPriceBase();
            },
            'title',
            'purchase_currency',
            'description',
            'amount_deposit',
            'accept_guarantee_bank',
            'method_payment',
            'advance_payment_percentage',
            'advance_payment_percentage',
            'advance_payment_period',
            'unblocking_type',
            'contact_position',
            'contact_phone',
            'address',
            'delivery_phone',
        ];
    }

    public function extraFields (){
        return [
            'contactFile',
            'technicalDocumentFile',
            'technicalTaskFile',
            'planSchedule',
            'region',
            'district',
            'tenderTotalPrice' => function($model){
                return $model->getTenderTotalPriceBase();
            },
            'tenderClassifiers',
            'tenderRequirements',
            'tenderCommissionMembers',
        ];
    }

}