<?php

namespace api\modules\auth\forms;

use api\components\BaseRequest;
use api\modules\auth\resources\UserResource;
use cheatsheet\Time;
use common\enums\RoleEnum;
use common\enums\TenderEnum;
use common\enums\UserEnum;
use common\models\BlackList;
use common\models\CommissionMember;
use common\models\UserToken;
use Exception;
use Yii;

class LoginEdsForm extends BaseRequest
{
    public $pkcs7;

    public function rules()
    {
        return [
            [['pkcs7'], 'required', 'message' => t('{attribute} yuborish majburiy')],
        ];
    }


    public function getResult()
    {
        $pkcs7 = $this->pkcs7;

        if (!$pkcs7) {
            throw new \yii\base\Exception("Yuborilgan ma'lumotlar to'liq emas", 422);
        }

        $response = checkPkcs($pkcs7);
        $response = json_decode($response);
        if (isset($response->subjectCertificateInfo) && $response->subjectCertificateInfo != null) {
            if ($response->subjectCertificateInfo->subjectName) {
                $tin = isset($response->subjectCertificateInfo->subjectName->{'1.2.860.3.16.1.1'}) && $response->subjectCertificateInfo->subjectName->{'1.2.860.3.16.1.1'} != null ? $response->subjectCertificateInfo->subjectName->{'1.2.860.3.16.1.1'} : null;
                if ($tin) {

                    $user = UserResource::findOne(['username' => $tin]);

                    if ($user) {
                        if ($user->status == UserEnum::STATUS_ACTIVE) {

                            if (BlackList::find()->where(['inn' => $user->username])->exists()) {
                                throw new Exception(Yii::t('main', "Tizimga kirish mumkin emas (insofsiz ijrochi)"), 422);
                            }


                            Yii::$app->user->setIdentity($user);

                            $result = $user->toArray([]);
                            $access_token = $this->getAccessToken($user);
                            $result['access_token'] = $access_token;

                            return $result;


                        } else {
                            throw new Exception('Foydalanuvchi aktiv emas', 422);
                        }

                    } else {
                        throw new Exception('Foydalanuvchi topilmadi', 422);
                    }

                } else {
                    //throw new Exception('E-imzo MCHJ ga tegishli emas');

                    $pinfl = $response->subjectCertificateInfo->subjectName->{'1.2.860.3.16.1.2'};
                    if ($pinfl) {
                        $user = UserResource::findOne(['username' => $pinfl]);

                        if ($user) {
                            if ($user->role == RoleEnum::ROLE_COMMISSION) {
                                if ($user->status == UserEnum::STATUS_NOT_ACTIVE) {
                                    $transaction = Yii::$app->db->beginTransaction();
                                    $user->status = UserEnum::STATUS_ACTIVE;
                                    if ($user->save()) {
                                        $com = CommissionMember::findOne(['user_id' => $user->id]);
                                        if ($com) {
                                            $com->status = TenderEnum::STATUS_ACTIVE;
                                            if (!$com->save()) {
                                                $transaction->rollBack();
                                                throw new Exception('Komisiya azosini aktivlashtirishda xatolik', 422);
                                            }
                                            $transaction->commit();
                                        } else {
                                            $transaction->rollBack();
                                            throw new Exception('Komisiya azosi topilmadi', 422);
                                        }
                                    }
                                }
                            } else {
                                throw new Exception('Komisiya azosi topilmadi', 422);
                            }

                            if ($user->status == UserEnum::STATUS_ACTIVE) {

                                Yii::$app->user->setIdentity($user);

                                $result = $user->toArray([]);
                                $access_token = $this->getAccessToken($user);
                                $result['access_token'] = $access_token;

                                return $result;

                            } else {
                                throw new Exception('Foydalanuvchi aktiv emas', 422);
                            }

                        } else {
                            throw new Exception('Foydalanuvchi topilmadi', 422);
                        }
                    } else {
                        throw new Exception('pinfl not found', 422);
                    }
                }
            } else {
                throw new Exception('subjectCertificateInfo->subjectName not found', 422);
            }
        } else {
            throw new Exception('subjectCertificateInfo not found', 422);
        }
    }

    public function loginSucceeded($user)
    {
        if ($user !== null) {
            $user->failed_attempts = 0;
            $user->last_failed_attempt_time = null;
            $user->save(false);
        }
    }

    protected function getAccessToken($user)
    {
        $token = UserToken::find()
            ->where(['user_id' => $user->id, 'type' => 'access'])
            ->andWhere(['>', 'expire_at', time()])
            ->one();

        if (!$token) {
            $token = UserToken::create($user->id, UserToken::TYPE_LOGIN_DEFAULT, Time::SECONDS_IN_AN_HOUR);
        }

        $user->access_token = $token->token;

        if (!$user->save(false)) {
            throw new Exception('User access token save error');
        }

        $this->loginSucceeded($user);

        return $token->token;
    }
}
