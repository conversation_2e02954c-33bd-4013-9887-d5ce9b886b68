<?php

namespace api\modules\auth\forms;

use api\components\BaseRequest;
use api\modules\auth\resources\UserResource;
use cheatsheet\Time;
use common\enums\RoleEnum;
use common\enums\TenderEnum;
use common\enums\UserEnum;
use common\models\BlackList;
use common\models\CommissionMember;
use common\models\UserToken;
use Exception;
use Yii;

class LoginForm extends BaseRequest
{
    public $username;
    public $password;

    private $_user;

    public function rules()
    {
        return [
            [['username', 'password'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [
                ['password'],
                function ($attribute) {
                    if (null === $this->user || !$this->checkPassword()) {
                        $this->loginFailed();
                        if ($this->isAccountLocked()) {
                            $this->addError(
                                'error',
                                Yii::t('main', "Profil vaqtincha bloklangan. Iltimos, keyinroq urinib ko'ring.")
                            );
                        } else {
                            $this->addError(
                                'error',
                                Yii::t('main', "Login yoki parol noto'g'ri")
                            );
                        }

                    }
                }
            ],

            [
                ['username'],
                function ($attribute) {
                    if (null !== $this->user) {
                        if ($this->user->status != UserEnum::STATUS_ACTIVE) {
                            $this->addError(
                                'error',
                                Yii::t('main', "Profil faol emas, ma'muriyatga murojaat qiling")
                            );
                        }
                    }
                }
            ],
        ];
    }

    /**
     * @return UserResource|null
     */
    public function getUser(): ?UserResource
    {
        if (null == $this->_user) {
            $this->_user = UserResource::findOne([
                'username' => $this->username,
            ]);
        }

        return $this->_user;
    }

    private function checkPassword()
    {
        return $this->user->validatePassword($this->password);
    }

    public function getResult()
    {
        $user = $this->user;
        if (BlackList::find()->where(['inn' => $user->username])->exists()) {
            $this->addError(
                'error',
                Yii::t('main', "Tizimga kirish mumkin emas (insofsiz ijrochi)")
            );
            return false;

        }

//        if ($this->isAccountLocked()) {
//            $this->addError(
//                "error",
//                "Hisobingizga ko'plab noto'g'ri kirish urinishlari amalga oshirildi. Tizimga kirish uchun to'g'ri login va parolni kiritishingiz zarur"
//            );
//            return false;
//        }

        Yii::$app->user->setIdentity($user);

        $result = $this->user->toArray([]);

        $result['access_token'] = $this->getAccessToken();

        return $result;
    }

    protected function getAccessToken()
    {
        $user = $this->user;

        $token = UserToken::find()
            ->where(['user_id' => $user->id, 'type' => UserToken::TYPE_LOGIN_DEFAULT])
            ->andWhere(['>', 'expire_at', time()])
            ->one();

        if (!$token) {
            $token = UserToken::create($user->id, UserToken::TYPE_LOGIN_DEFAULT, Time::SECONDS_IN_AN_HOUR);
        }

        $user->access_token = $token->token;

        if (!$user->save(false)) {
            throw new Exception('User access token save error');
        }

        $this->loginSucceeded();

        return $token->token;
    }

    protected function isAccountLocked()
    {
        $user = $this->user;
        if ($user != null) {
            $lockedUntil = $user->account_locked_until;

            if ($lockedUntil !== null && strtotime($lockedUntil) > time()) {
                $lockTimeMinutes = 3;

                $newLockUntil = date('Y-m-d H:i:s', strtotime("+{$lockTimeMinutes} minutes"));
                $user->account_locked_until = $newLockUntil;
                $user->last_failed_attempt_time = date('Y-m-d H:i:s');
                $user->save();

                return true;
            }
        }


        return false;

    }

    public function loginFailed()
    {
        $user = $this->user;

        if ($user !== null) {
            $user->failed_attempts = $user->failed_attempts + 1;
            $user->last_failed_attempt_time = date('Y-m-d H:i:s');

            $maxFailedAttempts = 3;
            $lockTimeMinutes = 3;

            if ($user->failed_attempts >= $maxFailedAttempts) {
                $user->account_locked_until = date('Y-m-d H:i:s', strtotime("+{$lockTimeMinutes} minutes"));
            }

            $user->save(false);
        }
    }

    public function loginSucceeded()
    {
        $user = $this->user;

        if ($user !== null) {
            $user->failed_attempts = 0;
            $user->last_failed_attempt_time = null;
            $user->save(false);
        }
    }
}
