<?php

namespace api\modules\common\forms;

use api\components\BaseRequest;
use common\enums\OperationTypeEnum;
use common\models\Company;
use common\models\CompanyVirtualAccount;
use common\models\VirtualTransaction;
use Yii;
use yii\db\Exception;
use yii\web\NotFoundHttpException;

class TestPayInForm extends BaseRequest
{
    public $tin;
    public $amount;
    public function rules(): array
    {
        return [
            [['tin', 'amount'], 'required'],
            ['amount', 'number'],
        ];
    }

    /**
     * @throws Exception
     * @throws NotFoundHttpException
     * @throws \yii\base\Exception
     * @throws \Throwable
     */
    public function getResult(): bool
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $_company = _company();
            $_account = CompanyVirtualAccount::findOne(['company_id' => $_company->id,'prefix_account' => OperationTypeEnum::A_50111]);
            if (!$_account)
                throw new NotFoundHttpException("Birjani accounti hali kiritilmagan.");
            $_account->price = $_account->price + $this->amount;
            if (!$_account->save())
            {
                $transaction->rollBack();
                $this->addErrors($_account->getErrors());
                return false;
            }
            $company = Company::findOne(['tin' => $this->tin]);
            if (!$company) {
                $this->addError('tin','Company not found');
            }
            VirtualTransaction::saveTransaction(
                $_company,
                $company,
                OperationTypeEnum::A_50111,
                OperationTypeEnum::P_K_30101,
                $this->amount,
                "TEST PUL BIRIKTIRISH",
                null,
                null,
                null,
                OperationTypeEnum::TEST_FILL_ACCOUNT,
            );
        } catch (Exception $e) {
            $transaction->rollBack();
            $this->addError("error",$e->getMessage());
        }
        $transaction->commit();
        return true;
    }
}