<?php

use yii\db\Migration;

class m250717_115144_add_column_in_table_product_draft_file extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('product_draft_file' , 'type' , $this->integer());
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('product_draft_file' , 'type');
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250717_115144_add_column_in_table_product_draft_file cannot be reverted.\n";

        return false;
    }
    */
}
