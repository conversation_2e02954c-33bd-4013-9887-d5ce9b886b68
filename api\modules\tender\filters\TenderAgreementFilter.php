<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;

class TenderAgreementFilter extends BaseRequest
{
    public $lot;

    public function rules()
    {
        return [
            [['lot'], 'safe'],
        ];
    }

    public function getResult()
    {
        $model = TenderResource::find()->notDeletedAndFromCompany();
        $model->andWhere(['in', 'state', [TenderEnum::STATE_ACCEPT_MODERATOR, TenderEnum::STATE_READY_FOR_CHAIRMAN, TenderEnum::STATE_READY_TO_PRESENT]]);
        if ($this->lot) {
            $model->andWhere(['like', 'lot', $this->lot]);
        }
        $model->orderBy(['updated_at' => SORT_DESC]);
        return paginate($model);
    }
}