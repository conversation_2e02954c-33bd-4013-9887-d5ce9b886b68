<?php


namespace backend\modules\admin\resources;


use common\models\VirtualTransaction;

class BankAccountResource extends VirtualTransaction
{
    public function fields()
    {
        return [
            'id',
            'credit_company_id',
            'debit_company_tin',
            'credit_company_tin',
            'debit_account_id',
            'credit_account_id',
            'debit_account',
            'credit_account',
            'prefix_account',
            'description',
            'operation_type',
            'credit',
            'debit_id',
            'created_at',
            'updated_at',
            'created_by',
        ];
    }
}