<?php


namespace api\modules\tender\resources;


use api\modules\auth\resources\UserResource;
use api\modules\backend\resources\TenderModeratorLogResource;
use api\modules\client\resources\TenderRequestResource;
use api\modules\client\resources\TenderRequirementsAnswerResource;
use api\modules\common\resources\DistrictResource;
use api\modules\common\resources\FileResource;
use api\modules\common\resources\RegionResource;
use common\enums\TenderEnum;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\Tender;
use common\models\TenderRequirements;
use Yii;
use yii\db\ActiveQuery;
use yii\db\Expression;
use yii\db\Query;
use yii\web\NotFoundHttpException;

/**
 *
 * @property TenderCommissionVoteResource $tenderCommissionVoteChairman
 * @property TenderCommissionVoteResource $tenderCommissionVoteSecretary
 * @property TenderRequirements[] $tenderRequirementExpert
 */

class TenderResource extends Tender
{
    public function fields()
    {
        return [
            'id',
            'lot',
            'type',
            'state',
            'created_at',
            'updated_at',
            'tenderTotalPrice' => function ($model) {
                return $model->getTenderTotalPriceBase();
            },
            'tenderClassifiers',
            'tenderRequirements',
            'title',
            'purchase_currency',
            'description',
            'amount_deposit',
            'accept_guarantee_bank',
            'method_payment',
            'advance_payment_percentage',
            'contact_file_id',
            'advance_payment_period',
            'unblocking_type',
            'contact_position',
            'contact_phone',
            'contact_fio',
            'address',
            'delivery_phone',
            'publish_days',
            'end_date',
            'discussion_end_date',
            'criteria_evaluation_proposals',
            'technical_part',
            'price_part',
            'language',
            'funding_source',
            'preference_local_producer',
            'commissionMemberRole',
            'placement_period',
            'plan_schedule_id',
            'cancel_reason',
            'expertise_conclusion_number',
            'expertise_conclusion_date'
        ];
    }

    public function extraFields()
    {
        return [
            'contactFile',
            'files',
            'planSchedule',
            'region',
            'district',
            'tenderCommissionMembers',
            'tenderRequirementsAnswer',
            'tenderRequest',
            'qualificationList',
            'qualificationSelection',
            'tenderModeratorLog',
            'tenderRequestRating',
            'tenderRequestCount',
            'commissionVote',
            'deleted_reason',
            'deleted_at',
            'deletedBy' => function ($model) {
                return $model->updatedBy ? $model->updatedBy->username : $model->updated_by;
            }
        ];
    }

    public static function generateLotNumber($id)
    {

        $YY = substr(date('Y'), -2);
        $P = env('LOT_OPERATOR_NUMBER', 5);
        $CUSTOMER_TYPE = env('LOT_CUSTOMER_TYPE', 2);
        $K = 1;
        $CCC = '008';

        return $YY . $P . $CUSTOMER_TYPE . $K . $CCC . sprintf('%06d', $id);

    }

    public function getCommissionMemberRole()
    {
        $model = TenderCommissionMemberResource::find()->notDeleted()
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_id' => $this->id])
            ->andWhere(['commission_member_id' => Yii::$app->user->identity->commissionMemberId])
            ->one();
        return $model ? $model->role : 0;
    }


    public function getContactFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'contact_file_id']);
    }


    public function getDistrict()
    {
        return $this->hasOne(DistrictResource::class, ['id' => 'district_id']);
    }


//    public function getPlanSchedule()
//    {
//        return $this->hasOne(FileResource::class, ['id' => 'plan_schedule_id']);
//    }


    public function getRegion()
    {
        return $this->hasOne(RegionResource::class, ['id' => 'region_id']);
    }


    public function getFiles()
    {
        return $this->hasMany(TenderFilesResource::class, ['tender_id' => 'id']);
    }

    /**
     * Gets query for [[TenderClassifiers]].
     *
     * @return ActiveQuery
     */
    public function getTenderClassifiers()
    {
        return $this->hasMany(TenderClassifierResource::class, ['tender_id' => 'id'])->andWhere([TenderClassifierResource::tableName() . '.status' => TenderEnum::STATUS_ACTIVE]);
    }

    public function getTenderRequirements()
    {
        return $this->hasMany(TenderRequirementsResource::class, ['tender_id' => 'id'])->andWhere([TenderRequirementsResource::tableName() . '.status' => TenderEnum::STATUS_ACTIVE]);
    }

    public function getTenderRequirementsAnswer()
    {
        return $this->hasMany(TenderRequirementsAnswerResource::class, ['tender_id' => 'id']);
    }

    public function getQualificationList()
    {
        return $this->hasMany(TenderQualificationFileListResource::class, ['tender_id' => 'id']);
    }

    public function getTenderRequestRating()
    {
        return $this->hasMany(TenderRequestRatingResource::class, ['tender_id' => 'id']);
    }


    public function getTenderRequest()
    {
        return TenderRequestResource::find()
            ->notDeleted()
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_id' => $this->id])
            ->all();
        //return $this->hasMany(TenderRequestResource::class, ['tender_id' => 'id']);
    }

    public function getTenderRequestWinner()
    {
        return TenderRequestResource::find()
            ->notDeleted()
            ->andWhere(['is_winner' => TenderEnum::IS_WINNER_YES])
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_id' => $this->id])
            ->one();
        //return $this->hasMany(TenderRequestResource::class, ['tender_id' => 'id']);
    }

    public function getTenderRequestSecondWinner()
    {
        return TenderRequestResource::find()
            ->notDeleted()
            ->andWhere(['is_winner' => TenderEnum::IS_SECOND_WINNER_YES])
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_id' => $this->id])
            ->one();
    }

    public function getTenderRequestCount()
    {
        return count($this->tenderRequest);
    }

    public function getQualificationSelection()
    {
        return $this->hasMany(TenderQualificationSelectionResource::class, ['tender_id' => 'id'])->andWhere([TenderQualificationSelectionResource::tableName() . '.status' => TenderEnum::STATUS_ACTIVE]);
    }

    public function getTenderCommissionMembers()
    {
        return TenderCommissionMemberResource::find()->notDeletedAndFromCompany()->andWhere(['tender_id' => $this->id, 'status' => TenderEnum::STATUS_ACTIVE])->all();
//        return $this->hasMany(TenderCommissionMemberResource::class, ['tender_id' => 'id']);
    }

    public function getTenderCommissionMembersCount()
    {
        return TenderCommissionMemberResource::find()->notDeletedAndFromCompany()->where(['tender_id' => $this->id, 'status' => TenderEnum::STATUS_ACTIVE])->count();
    }


    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

//    public function getTenderTotalPrice()
//    {
//        $price = 0;
//        foreach ($this->tenderClassifiers as $classifier) {
//            $price += ($classifier->price * $classifier->number_purchased);
//        }
//        return $price;
//    }

    public function getCommissionMember(int $commissionId)
    {
        $model = TenderCommissionMemberResource::find()->notDeletedAndFromCompany()
            ->andWhere(['tender_commission_member.tender_id' => $this->id, 'tender_commission_member.commission_member_id' => $commissionId, 'tender_commission_member.status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_commission_member.status' => TenderEnum::STATUS_ACTIVE])
            ->one();
        if (!$model) new NotFoundHttpException("Ma'lumot topilmadi");
        return $model;
    }

    public function getCommissionVote(int $commissionId = null): bool
    {
        if ($commissionId === null) {
            $commissionId = \Yii::$app->user->identity->commissionMemberId;
        }
        return TenderCommissionVoteResource::find()
            ->where(['tender_id' => $this->id, 'commission_member_id' => $commissionId, 'status' => TenderEnum::STATUS_ACTIVE])
            ->exists();
    }

    public function getCommissionVoteCount()
    {
        return TenderCommissionVoteResource::find()
            ->where(['tender_id' => $this->id, 'status' => TenderEnum::STATUS_ACTIVE])
            ->count();
    }

    public function getCommissionVoteYesCount()
    {
        return TenderCommissionVoteResource::find()
            ->where(['tender_id' => $this->id, 'vote' => TenderEnum::VOTE_YES, 'status' => TenderEnum::STATUS_ACTIVE])
            ->count();
    }

    public function getCommissionVoteChairmanCount()
    {
        $chairman = TenderCommissionMemberResource::find()->where(['tender_id' => $this->id, 'status' => TenderEnum::STATUS_ACTIVE, 'role' => TenderEnum::ROLE_CHAIRMAN])->one();
        $vote = TenderCommissionVoteResource::find()
            ->where(['tender_id' => $this->id, 'status' => TenderEnum::STATUS_ACTIVE, 'commission_member_id' => $chairman->id])
            ->one();
        return $vote ? $vote->vote : null;
    }

    public function getCommissionVoteNoCount()
    {
        return TenderCommissionVoteResource::find()
            ->where(['tender_id' => $this->id, 'vote' => TenderEnum::VOTE_NO, 'status' => TenderEnum::STATUS_ACTIVE])
            ->count();
    }

    public function getTenderModeratorLog()
    {
        return TenderModeratorLogResource::find()->where(['tender_id' => $this->id])->orderBy(['id' => SORT_DESC])->one();
    }

    public function getUpdatedBy()
    {
        return $this->hasOne(UserResource::class, ['id' => 'updated_by']);
    }

    public function getTenderCommissionVoteChairman()
    {

        return TenderCommissionVoteResource::find()
            ->where(['tender_id' => $this->id, 'role' => TenderEnum::ROLE_CHAIRMAN])
            ->one();
    }
    public function getTenderCommissionVoteSecretary()
    {

        return TenderCommissionVoteResource::find()
            ->where(['tender_id' => $this->id, 'role' => TenderEnum::ROLE_SECRETARY])
            ->one();
    }

    public function getTenderCommissionMember($role = null)
    {
        $query = (new Query())
            ->from(TenderCommissionMemberResource::tableName())
            ->leftJoin('commission_member', 'commission_member.id = tender_commission_member.commission_member_id')
            ->where(['tender_id' => $this->id,'commission_member.deleted_at' => null]);
        if ($role !== null) {
            $query->andWhere(['tender_commission_member.role' => $role]);
        }
        return $query->one();
    }

    /**
     * @param $status - Tender action request status, see TenderEnum
     * @return array|bool
     */
    public function getTenderActionRequestVoteCount($status = null)
    {
        $query = (new Query())
            ->select(["vote","COUNT(*) AS count"])
            ->from(TenderActionRequestResource::tableName())
            ->leftJoin('tender_action_request_commission_vote','tender_action_request_commission_vote.tender_action_request_id = tender_action_request.id')
            ->groupBy('vote');
        if ($status !== null) {
            $query->andWhere(['tender_action_request.status' => $status]);
        }
        return $query->indexBy('vote')->one();
    }

    public function _generateSummaryForProtocol(): string
    {
        return $this->description . '(№' . $this->lot . ')';
    }

    /**
     * @param array $condition - extra condition for ignore secretary for vote
     * @return array all tender commission members
     */
    public function getTenderCommissionMemberAsArray(array $condition = []): array
    {
        $query = (new Query())
            ->from(TenderCommissionMemberResource::tableName())
            ->leftJoin('commission_member', 'commission_member.id = tender_commission_member.commission_member_id')
            ->where(['tender_commission_member.status' => TenderEnum::STATUS_ACTIVE,'tender_id' => $this->id]);
        if (!empty($condition)) {
            $query->andWhere($condition);
        }

        return $query->orderBy(['tender_commission_member.role' => SORT_DESC])->all();
    }

    /**
     * @return array get tender requests with company information
     */
    public function getTenderRequestAsArray(): array
    {
        $query = (new Query())
                ->from(TenderRequestResource::tableName())
                ->leftJoin('company', 'company.id = tender_request.company_id')
                ->where([
                    'tender_id' => $this->id,
                    'tender_request.deleted_at' => null
                ]);
        return $query->orderBy(['tender_request.id' => SORT_ASC])->all();
    }

    /**
     * @return array | ActiveQuery - get sorted data
     */
    public function getTenderRequirementExpert()
    {
        $parent = parent::getTenderRequirementExpert();
        return $parent->orderBy(['tender_requirements.id' => SORT_DESC]);
    }

    /**
     * @return array - get tender requests answers with tender requirements
     */
    public function getTenderRequestAnswersAsArray(): array
    {
        $query = (new Query())
            ->select([
                "trr.tender_requirement_answer_id as id",
                "tra.title as requirement",
                "c.title as company",
                "tra.value as score",
                "tra.value as grade",
                new Expression("'-' AS description"),
            ])
            ->from('tender_request_rating as trr')
            ->leftJoin('tender_requirements_answer as tra', 'tra.id = trr.tender_requirement_answer_id')
            ->leftJoin('company as c', 'c.id = tra.company_id')
            ->where(['trr.tender_id' => $this->id,'trr.deleted_at' => null])
            ->orderBy(['id' => SORT_ASC]);
        return $query->all();
    }

    public function _prepareTenderRequestAnswersAsArray(): array
    {
        $newArr = [];
        $data = $this->getTenderRequestAnswersAsArray();
        foreach ($data as $item) {
            $id = $item['id'];

            if (!isset($newArr[$id])) {
                $newArr[$id] = [
                    'id' => $id,
                    'requirement' => $item['requirement'],
                    'requests' => [], // bu yerga company, score, grade qo‘shamiz
                ];
            }

            $newArr[$id]['requests'][] = [
                'company' => $item['company'],
                'score' => $item['score'],
                'grade' => $item['grade'],
                'description' => $item['description'],
            ];
        }

        return $newArr;
    }

    public function getTenderCommissionVoteMembers()
    {
        $data = (new Query())->from(TenderCommissionVoteResource::tableName())
            ->leftJoin('commission_member', 'commission_member.id = tender_commission_vote.commission_member_id')
            ->where(['tender_id' => $this->id])
            ->orderBy(['tender_commission_vote.role' => SORT_DESC])
            ->all();
        $unique = [];
        $seen = [];

        foreach ($data as $item) {
            $key = $item['role'] . '|' . $item['commission_member_id'];
            if (!isset($seen[$key])) {
                $unique[] = $item;
                $seen[$key] = true;
            }
        }

        return $unique;
    }

    public static function _findOne($id, $state = null)
    {
        $model = TenderResource::find()->where(['company_id' => Yii::$app->user->identity->company_id])->andWhere(['id' => $id]);

        if ($state != null && in_array($state, TenderEnum::STATE_LIST)) {
            $model->andWhere(['state' => $state]);
        }
        $model = $model->one();

        if (!$model) throw new NotFoundHttpException("Tender is not found");

        return $model;
    }
}