<?php

namespace api\modules\common\resources;

use common\models\VirtualTransaction;

class VirtualTransactionResource extends VirtualTransaction
{
    public static function findOneByCreditCompanyIDAndProductAndTypeAndNotRefunded($companyID, $productName, $productID, $operationType = null, $extraCondition = [])
    {
        $query = static::find()
            ->andWhere(['credit_company_id' => $companyID,$productName => $productID,"parent_id" => null])
            ->andWhere(['>','credit',0])
            ->andWhere($extraCondition)
            ->andFilterWhere(['operation_type' => $operationType]);
        return $query->one();
    }

    public static function findAllByProductAndOperationAndNotRefund($productName, $productID, $operationType): array
    {
        return self::find()
            ->andWhere([$productName => $productID,'operation_type' => $operationType,'parent_id' => null])
            ->andWhere(['>', 'credit', 0])
            ->all();
    }
}