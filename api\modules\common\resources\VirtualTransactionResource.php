<?php

namespace api\modules\common\resources;

use common\enums\CompanyTransactionEnum;
use common\enums\OperationTypeEnum;
use common\enums\TenderEnum;
use common\models\VirtualTransaction;
use Yii;

class VirtualTransactionResource extends VirtualTransaction
{
    public function fields(): array
    {
        return [
            'id',
            'contract_id',
            'procedure_type' => function (VirtualTransactionResource $model) {
                return $model->getProcedureLabel();
            },
            'payer' =>  function (VirtualTransactionResource $model) {
                return $model->debitCompany->title ?? null;
            },
            'payer_inn' =>  function (VirtualTransactionResource $model) {
                return $model->debitCompany->tin ?? null;
            },
            'recipient' =>  function (VirtualTransactionResource $model) {
                return $model->creditCompany->title ?? null;
            },
            'recipient_inn' =>  function (VirtualTransactionResource $model) {
                return $model->creditCompany->tin ?? null;
            },
            'debit',
            'credit',
            'description' => function (VirtualTransactionResource $model) {
                return $model->getOperationTypeLabel();
            },
            'created_date' => function (VirtualTransactionResource $model) {
                return $model->created_at ? date('d/m/Y', strtotime($model->created_at)) : null;
            },
            'created_time' => function (VirtualTransactionResource $model) {
                return $model->created_at ? date('H:i:s', strtotime($model->created_at)) : null;
            },
        ];
    }

    public static function findOneByCreditCompanyIDAndProductAndTypeAndNotRefunded($companyID, $productName, $productID, $operationType = null, $extraCondition = [])
    {
        $query = static::find()
            ->andWhere(['credit_company_id' => $companyID,$productName => $productID,"parent_id" => null])
            ->andWhere(['>','credit',0])
            ->andWhere($extraCondition)
            ->andFilterWhere(['operation_type' => $operationType]);
        return $query->one();
    }

    public static function findAllByProductAndOperationAndNotRefund($productName, $productID, $operationType): array
    {
        return self::find()
            ->andWhere([$productName => $productID,'operation_type' => $operationType,'parent_id' => null])
            ->andWhere(['>', 'credit', 0])
            ->all();
    }

    public function getProcedureType(): ?int
    {
        if ($this->tender_id != null) {
            if ($this->tender->type == TenderEnum::TYPE_INVITE) {
                return CompanyTransactionEnum::PROCEDURE_TYPE_SELECTION;
            }
            return  CompanyTransactionEnum::PROCEDURE_TYPE_TENDER;
        } else if ($this->auction_id != null) {
            return CompanyTransactionEnum::PROCEDURE_TYPE_AUCTION;
        } else if ($this->order_id != null) {
            if ($this->order->product->platform_display == 'e-shop')
            {
                return CompanyTransactionEnum::PROCEDURE_TYPE_E_SHOP;
            }
            return CompanyTransactionEnum::PROCEDURE_TYPE_N_SHOP;
        }
        return null;
    }

    public function getProcedureLabel()
    {
        $lang = Yii::$app->language;
        $type = $this->getProcedureType();
        if ($labels = CompanyTransactionEnum::PROCEDURE_LIST_LABEL[$type]) {
            return $labels[$lang] ?? null;
        }
        return null;
    }

    public function getOperationTypeLabel()
    {
        if ($labels = OperationTypeEnum::TYPES[$this->operation_type])
        {
            return $labels[Yii::$app->language] ?? null;
        }
        return null;
    }
}