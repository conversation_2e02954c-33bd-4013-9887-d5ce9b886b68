<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\shop\resources\CartResource;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;
use Yii;
use yii\data\ActiveDataProvider;

class CartListFilter extends BaseRequest
{
    public $product_id;
    public $quantity;
    public $company_id;
    public $user_id;
    public $pageNo = 0;
    public $pageSize = 10;
    public function rules()
    {
        return [
            [['quantity', 'pageSize'], 'integer'],
            [['company_id','product_id','user_id'], 'safe']
        ];
    }

    public function getResult()
    {

        $model = CartResource::find();
        if ($this->company_id) {
            $model->andWhere(['company_id' => $this->company_id]);
        }
        if ($this->user_id) {
            $model->andWhere(['user_id' => $this->user_id]);
        }

        if ($this->product_id) {
            $model->andWhere([ 'title'=> $this->product_id]);
        }

      return paginate($model);
    }
}
