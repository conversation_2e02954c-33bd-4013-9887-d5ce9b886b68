<?php


namespace api\modules\common\forms;


use api\components\BaseRequest;
use common\models\CompanyBankAccount;
use yii\base\Exception;

class CompanyBankAccountSetMainForm extends BaseRequest
{
    public CompanyBankAccount $model;

    public function __construct(CompanyBankAccount $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function getResult()
    {
        $user = \Yii::$app->user->identity;
        $companyId = $user->company_id;
        $userId = $user->id;
        $model = $this->model;
        $date = date("Y-m-d H:i:s");
        $model->is_main = 1;
        if (!$model->save(false)) {
            throw new Exception('Hisob raqamni asosiy qilishda xatolik');
        }
        $where = 'company_id=' . $companyId . ' and id != ' . $model->id;
        if ($user->isBudget) {
            $where .= ' and organ = \'' . $user->organ . '\'';
        } else {
            $where .= ' and organ is null';
        }

        CompanyBankAccount::updateAll(['is_main' => 0, 'updated_at' => $date, 'updated_by' => $userId], $where);

        return true;
    }
}