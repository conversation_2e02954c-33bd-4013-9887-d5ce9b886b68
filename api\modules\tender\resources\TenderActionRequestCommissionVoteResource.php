<?php

namespace api\modules\tender\resources;

use common\models\TenderActionRequestCommissionVote;

class TenderActionRequestCommissionVoteResource extends TenderActionRequestCommissionVote
{
    public function fields()
    {
        return [
            'commission_member' => 'commissionMember',
            'role',
            'vote',
            'description'
        ];
    }

    public function getCommissionMember()
    {
        return $this->hasOne(CommissionMemberShortResource::class, ['id' => 'commission_member_id']);
    }
}