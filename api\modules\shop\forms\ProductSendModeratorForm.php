<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;
use common\models\CompanyBalance;
use common\models\TenderModeratorLog;
use Yii;
use yii\base\Exception;
use function foo\func;

class ProductSendModeratorForm extends BaseRequest
{

    public ProductResource $model;

    public $state;
    public $description;
    public $moderator_pinfl;


    public function __construct(ProductResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [ ['description'
            ], 'safe'],

        ];
    }



    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        $this->model->state = ProductEnum::SHOP_STATE_NEW;
        if ($this->model->save()){
            $transaction->commit();
            return true;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}