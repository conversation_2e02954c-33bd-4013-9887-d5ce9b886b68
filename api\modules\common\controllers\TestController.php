<?php

namespace api\modules\common\controllers;

use api\modules\common\filters\TestShowAccountFilter;
use api\modules\common\forms\TestPayInForm;
use Yii;
use api\components\ApiController;
use yii\filters\auth\HttpBearerAuth;

class TestController extends ApiController
{
    public function behaviors()
    {
        $parent = parent::behaviors();
        $parent['bearerAuth'] =  [
                'class' => HttpBearerAuth::class,
                'except' => [
                    'pay-in',
                    'view',
                ]
        ];
        return $parent;
    }

    public function actionPayIn(): array
    {
        return $this->sendResponse(
            new TestPayInForm(),
            Yii::$app->request->bodyParams,
        );
    }

    public function actionView()
    {
        return $this->sendResponse(
            new TestShowAccountFilter(),
            Yii::$app->request->queryParams,
        );
    }
}