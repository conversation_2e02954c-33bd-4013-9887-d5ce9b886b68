<?php
namespace api\modules\backend\forms;

use api\modules\backend\resources\TenderModeratorLogResource;
use api\modules\backend\resources\TenderResource;
use common\enums\TenderEnum;
use Yii;

class TenderAcceptForm extends \api\components\BaseRequest
{
    public TenderResource $model;
    public TenderModeratorLogResource $log;

    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;
        $this->log = new TenderModeratorLogResource();

        parent::__construct($params);
    }
    public function getResult()
    {
        if($this->model->state == TenderEnum::STATE_NEW){
            $this->log->tender_id = $this->model->id;
            $this->log->state = TenderEnum::STATE_ACCEPT_MODERATOR;
            $this->log->moderator_pinfl = \Yii::$app->user->identity->username;
            if($this->log->save()){
                $this->model->state = TenderEnum::STATE_ACCEPT_MODERATOR;
                if($this->model->save()){
                    return true;
                } else {
                    $this->addErrors($this->model->errors);
                    return false;
                }
            }
            $this->addErrors($this->log->errors);
            return false;
        } else {
            $this->addError("state", "Tender moderatsiya holatida emas");

            return $this->model->errors;
        }

    }
}