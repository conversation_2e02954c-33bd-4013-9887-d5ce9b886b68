<?php


namespace api\modules\auction\resources;


use common\models\auction\AuctionOffer;

class AuctionOfferResource extends AuctionOffer
{
    public function fields()
    {
        return [
            'price' => function ($model) {
                return $model->price / 100;
            },
            'created_at',
            'isMyOffer' => function ($model) {
                return !(\Yii::$app->user->isGuest) && isset(\Yii::$app->user->identity->company_id) && \Yii::$app->user->identity->company_id != null ? $model->company_id == \Yii::$app->user->identity->company_id : false;
            },
            'countries' => function ($model) {
                $myOffer = !(\Yii::$app->user->isGuest) && isset(\Yii::$app->user->identity->company_id) && \Yii::$app->user->identity->company_id != null ? $model->company_id == \Yii::$app->user->identity->company_id : false;
                if ($myOffer) {
                    return $model->countries;
                } else {
                    return null;
                }
            }
        ];
    }

    public function getCountries()
    {
        return $this->hasMany(AuctionRequestClassifierCountryResource::class, ['auction_request_id' => 'id']);
    }
}