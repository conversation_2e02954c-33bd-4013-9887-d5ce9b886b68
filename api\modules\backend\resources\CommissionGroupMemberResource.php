<?php

namespace api\modules\backend\resources;

use common\models\CommissionGroupMember;

class CommissionGroupMemberResource extends CommissionGroupMember {

    public function fields (){
        return [
            'id',
            'commission_member_id',
            'commission_group_id',
            'role'
        ];
    }

    public function extraFields (){
        return [
            'commissionGroup',
            'commissionMember'
        ];
    }

    public function getCommissionGroup(){
        return $this->hasOne(CommissionGroupResource::class, ['id' => 'commission_group_id']);
    }

    public function getCommissionMember(){
        return $this->hasOne(CommissionMemberResource::class, ['id' => 'commission_member_id']);
    }
}