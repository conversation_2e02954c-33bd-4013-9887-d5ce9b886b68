<?php

namespace common\models\shop;

use common\components\ActiveRecordMeta;
use common\models\File;
use common\traits\NotDeleted;
use common\traits\SoftDelete;
use Yii;

/**
 * This is the model class for table "product_draft_file".
 *
 * @property int $id
 * @property int|null $product_id
 * @property int|null $file_id
 * @property int|null $sort
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $type
 *
 * @property File $file
 * @property ProductDraft $product
 */
class ProductDraftFile extends ActiveRecordMeta
{

    use SoftDelete;
    use NotDeleted;

    const TYPE_IMAGE =1;
    const TYPE_FILE =2;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'product_draft_file';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['product_id', 'file_id', 'created_at', 'updated_at', 'deleted_at', 'created_by', 'updated_by', 'type'], 'default', 'value' => null],
            [['sort'], 'default', 'value' => 0],
            [['product_id', 'file_id', 'sort', 'created_by', 'updated_by', 'type'], 'integer'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['file_id'], 'exist', 'skipOnError' => true, 'targetClass' => File::class, 'targetAttribute' => ['file_id' => 'id']],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => ProductDraft::class, 'targetAttribute' => ['product_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'product_id' => 'Product ID',
            'file_id' => 'File ID',
            'sort' => 'Sort',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'deleted_at' => 'Deleted At',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
            'type' => 'Type',
        ];
    }

    /**
     * Gets query for [[File]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getFile()
    {
        return $this->hasOne(File::class, ['id' => 'file_id']);
    }

    /**
     * Gets query for [[Product]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getProduct()
    {
        return $this->hasOne(ProductDraft::class, ['id' => 'product_id']);
    }

}
