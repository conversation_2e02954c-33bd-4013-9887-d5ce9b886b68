<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use common\models\shop\Product;
use common\models\User;

class StatisticsFilter extends BaseRequest
{

    public function getResult()
    {
        $sql = "select sum(case when order_id is not null then price else 0 end) orderPrice,
                       sum(case when auction_id is not null then price else 0 end) auctionPrice,
                       sum(case when tender_id is not null then price else 0 end) tenderPrice
                from contract
                where status = 3 or status = 300";
        $data = \Yii::$app->db->createCommand($sql)->queryAll();
//        $order = Contract::find()->where('order_id is not null and status = 3')->sum('price');
//        $auction = Contract::find()->where('auction_id is not null and status = 3')->sum('price');
//        $tender = Contract::find()->where('tender is not null and status = 3')->sum('price');
        $user = User::find()->where('status=10 and company_id is not null')->count();
        $product = Product::find()->where('state=300 and company_id is not null and quantity > 0 and platform_display != "national-shop"')->count();
        $productMilliy = Product::find()->where('state=300 and company_id is not null and quantity > 0 and platform_display = "national-shop"')->count();

        return [
            "orderCount" => isset($data[0]['orderPrice']) ? $data[0]['orderPrice'] : 0,
            "auctionCount" => isset($data[0]['auctionPrice']) ? $data[0]['auctionPrice'] : 0,
            "tenderCount" => isset($data[0]['tenderPrice']) ? $data[0]['tenderPrice'] : 0,
            "userCount" => $user,
            'productCount' => $product,
            'productMilliy' => $productMilliy,
        ];
    }
}