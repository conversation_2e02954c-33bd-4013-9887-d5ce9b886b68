<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\shop\resources\CartResource;
use api\modules\shop\resources\ContractResource;
use api\modules\shop\resources\OrderResource;
use common\enums\ContractEnum;
use common\models\shop\Product;

class ContractListFilter extends BaseRequest
{
    public $producer_id;
    public $customer_id;
    public $order_id;
    public $status;
    public $title;
    public $pageNo = 0;
    public $pageSize = 10;
    public $type = ContractEnum::TYPE_USER_PRODUCER; // producer

    public function rules()
    {
        return [
            [['pageNo', 'pageSize'], 'integer'],
            [['producer_id','status','customer_id','order_id','title'], 'safe']
        ];
    }

    public function __construct($type, $params = [])
    {
        $this->type = $type;
        parent::__construct($params);
    }

    public function getResult()
    {
        $company_id = \Yii::$app->user->identity->company_id;
        $model = ContractResource::find()->where('order_id is not null');
        if($this->type == ContractEnum::TYPE_USER_PRODUCER){
            $model->andWhere(['producer_id' => $company_id]);
        } else {
            $model->andWhere(['customer_id' => $company_id]);
        }
//        $model->andWhere([ 'or',
//            ['producer_id' => $company_id],
//            ['customer_id' => $company_id]
//        ]);

//        if ($this->producer_id) {
//            $model->andWhere(['producer_id' => $this->producer_id]);
//        }
//        if ($this->customer_id) {
//            $model->andWhere(['customer_id' => $this->customer_id]);
//        }

        if ($this->order_id) {
            $model->andWhere([ 'order_id'=> $this->order_id]);
        }
        if ($this->status) {
            $model->andWhere([ 'status'=> $this->status]);
        }
        $model->orderBy([ 'created_at'=> SORT_DESC]);

        if ($this->title){
            $model->leftJoin(OrderResource::tableName(),OrderResource::tableName().'.id'.'='.ContractResource::tableName().'.order_id');
            $model->leftJoin(Product::tableName(),Product::tableName().'.id'.'='.OrderResource::tableName().'.product_id');
            $model->andWhere(['like',Product::tableName().'.title',$this->title]);
        }
        return paginate($model);
    }
}
