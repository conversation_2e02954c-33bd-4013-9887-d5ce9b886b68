<?php


namespace api\modules\shop\forms;

use api\components\BaseRequest;
use api\modules\shop\resources\OrderRequestResource;
use api\modules\shop\resources\OrderResource;
use common\enums\ShopEnum;
use yii\base\Exception;

class NoOfferAcceptForm extends BaseRequest
{

    public ?OrderRequestResource $model;
    public $id;


    public function rules()
    {
        return [
            [['id'], 'required'],
            [['id'], 'number'],
        ];
    }

    /**
     * @throws \yii\db\Exception
     * @throws Exception
     */
    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        $this->model = OrderRequestResource::findOne(['id' => $this->id]);
        if (!$this->model) {
            $transaction->rollBack();
            throw new Exception(t("OrderRequest not found."));
        }
        $this->model->status = ShopEnum::ORDER_REQUEST_STATUS_DONE;
        if ($this->model->save()) {

            $order = OrderResource::findOne($this->model->order_id);

            if (!$order) {
                throw new Exception(t("Order not found"));
            }
            $order->updateAttributes([
                'status' => ShopEnum::ORDER_STATUS_DONE,
                'request_end' => date("Y-m-d H:i:s")
            ]);
            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}