<?php

namespace api\modules\client\filters;

use api\modules\client\resources\TenderResource;
use common\enums\TenderEnum;

class TenderFilter extends \api\components\BaseRequest
{

    public $lot;
    public $title;
    public $tin;
    public $pinfl;
    public $price_min;
    public $price_max;
    public $currency;
    public $region_id;
    public $district_id;
    public $from_date;
    public $to_date;

    public $pageNo = 0;
    public $pageSize = 10;

    public function rules (){
        return [
            [['pageNo', 'pageSize'], 'integer'],
            [['lot', 'tin', 'pinfl', 'currency', 'region_id', 'district_id'], 'integer'],
            [['title', 'price_min', 'price_max', 'from_date', 'to_date',], 'safe']
        ];
    }

    public function getResult()
    {
        $model = TenderResource::find()->notDeleted()->andWhere(['state' => TenderEnum::STATE_READY]);
        if($this->tin){
            $model->join('left join', 'company', 'company.id=tender.company_id');
            $model->andWhere(['company.tin' => $this->tin]);
        } else if($this->pinfl){
            $model->join('left join', 'company', 'company.id=tender.company_id');
            $model->andWhere(['company.pinfl' => $this->pinfl]);
        }

        if($this->lot){
            $model->andWhere(['like','lot',$this->lot]);
        }
        if($this->title){
            $model->andWhere(['like','title',$this->title]);
        }


        if($this->price_min || $this->price_max){
            $model->join('left join', 'tender_classifier', 'tender_classifier.tender_id=tender.id');
            $model->andWhere(['tender_classifier.status' => TenderEnum::STATUS_ACTIVE]);
        }
        if($this->price_min){
            $model->andWhere(['>=','tender_classifier.price', $this->price_min]);
        }
        if($this->price_max){
            $model->andWhere(['<=','tender_classifier.price', $this->price_max]);
        }
        if($this->currency){
            $model->andWhere(['purchase_currency' => $this->currency]);
        }
        if($this->region_id){
            $model->andWhere(['region_id' => $this->region_id]);
        }
        if($this->district_id){
            $model->andWhere(['district_id' => $this->district_id]);
        }
        if($this->from_date){
            $model->andWhere(['>=','end_date', date("Y-m-d", strtotime($this->from_date))]);
        }
        if($this->to_date){
            $model->andWhere(['<=','end_date', date("Y-m-d", strtotime($this->to_date))]);
        }

        return paginate($model);

    }
}