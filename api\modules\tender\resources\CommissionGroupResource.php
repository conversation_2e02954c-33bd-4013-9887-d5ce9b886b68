<?php

namespace api\modules\tender\resources;

use common\enums\TenderEnum;
use common\models\CommissionGroup;
use common\models\query\NotDeletedFromCompanyQuery;

class CommissionGroupResource extends CommissionGroup {

    public function fields (){
        return [
            'id',
            'title',
            'status',
        ];
    }

    public function extraFields(){
        return [
            'commissionGroupMembersCount',
            'commissionGroupMembers'
        ];
    }

    public function getCommissionGroupMembers (){
        return $this->hasMany(CommissionGroupMemberResource::class, ['commission_group_id' => 'id'])->joinWith('commissionMember')->where('commission_group_member.status='.TenderEnum::STATUS_ACTIVE)->all(); // GroupMemberResource
    }

    public function getCommissionGroupMembersCount(){
        return count($this->commissionGroupMembers);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

}