<?php

namespace app\modules\tender\resources;

use api\modules\tender\resources\TenderRequestResource;
use common\enums\TenderEnum;

class TenderRequestResourceForSecretary extends TenderRequestResource
{
    public function fields()
    {
        $parent = parent::fields();
        unset($parent['price']);
        $child = [
            'is_preference_local_producer',
            'price' =>  function ($model) {
                if ($model->preference_local_producer == 1 && $model->is_preference_local_producer == 1) {
                    return $model->price * (100 - env('TENDER_DIFFERENCIAL_PERCENT',15)) / 100;
                }
                return $model->price;
            }
        ];
        return array_merge($parent, $child);
    }

    public function getTenderRequestValues()
    {
        return TenderRequestValuesForSecretaryResource::find()->where(['tender_request_id' => $this->id, 'status' => TenderEnum::STATUS_ACTIVE])->all();
    }
}