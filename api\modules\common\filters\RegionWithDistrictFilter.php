<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\DistrictResource;
use api\modules\common\resources\RegionResource;
use common\models\Region;
use yii\data\ArrayDataProvider;

class RegionWithDistrictFilter extends BaseRequest
{
    public $title;

    public function rules()
    {
        return [
            ['title', 'safe'],
        ];
    }

    public function getResult()
    {
        $data = [];
        $regions = RegionResource::find();
        if ($this->title){
            $regions->leftJoin('region d', 'd.parent_id'.'='. 'region.id');
            $regions->andWhere(['or',
            ['like', RegionResource::tableName().'.title_uz', $this->title],
            ['like', RegionResource::tableName().'.title_ru', $this->title],
            ['like', RegionResource::tableName().'.title_en', $this->title],
            ['like', RegionResource::tableName().'.title_uzk', $this->title],
                ['like',  'd.title_uz', $this->title],
                ['like',  'd.title_ru', $this->title],
                ['like',  'd.title_en', $this->title],
                ['like',  'd.title_uzk', $this->title],
            ]);
        }
        $r = 0;
        $regions->andWhere([RegionResource::tableName().'.parent_id'=>null]);
        $regions->andWhere([RegionResource::tableName().'.type'=>Region::TYPE_REGION]);
        foreach ($regions->all() as $region) {
            $data[$r]['id'] = $region->id;
            $data[$r]['name'] = $region->title_uz;
            $district = DistrictResource::find()->andWhere(['parent_id' => $region->id]);
            if (!$district->exists()) {
                continue;
            }
            $districts = $district->all();
            $b = 0;
            foreach ($districts as $district) {
                $data[$r]['options'][$b] = [
                    "value" => $district->id,
                    "label" =>  $district->title_uz
                ];
                $b++;
            }
            $r++;
        }
        return new ArrayDataProvider(['allModels' => $data]);
    }
}
