<?php


namespace api\modules\tender\resources;


use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\UnitResource;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderClassifier;

class TenderClassifierResource extends TenderClassifier
{

    public function fields()
    {
        return [
            'tender_id',
            'additional_charges_non_residents',
            'additional_charges_amount',
            'unit_id',
            'id',
            'title' => function ($model) {
                return $model->classifier->title_uz;
            },
            'classifier_id',
            'number_purchased',
            'unit' => function ($model) {
                return $model->planScheduleClassifier->unit;
            },
            'description',
            'properties' => function ($model) {
                return $model->planScheduleClassifier->description;
            },
            'category' => function ($model) {
                return $model->classifier->classifierCategory;
            },
            'price' => function ($model) {
                return $model->price / 100; // tiyindan somga convert qilindi
            },
            'expiry_date_value',
            'expiry_date_unit',
            'delivery_period',
            'month' => function ($model) {
                return $model->planScheduleClassifier->month;
            },
            'plan_schedule_classifier_id'
        ];

    }

    public function extraFields()
    {
        return [
            'classifier',
            'planScheduleClassifier',
            'tender',
        ];
    }

    public function getClassifier()
    {
        return $this->hasOne(ClassifierResource::class, ['id' => 'classifier_id']);
    }

    public function getUnit()
    {
        return $this->hasOne(UnitResource::class, ['id' => 'unit_id']);
    }

    /**
     * Gets query for [[PlanScheduleClassifier]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getPlanScheduleClassifier()
    {
        return $this->hasOne(PlanScheduleClassifierResource::class, ['id' => 'plan_schedule_classifier_id']);
    }

    /**
     * Gets query for [[Tender]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTender()
    {
        return $this->hasOne(TenderResource::class, ['id' => 'tender_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

    public static function findOne($id)
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

}