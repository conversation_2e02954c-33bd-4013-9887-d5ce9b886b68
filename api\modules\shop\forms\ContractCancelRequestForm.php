<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\shop\resources\ContractCancelRequestResource;
use api\modules\shop\resources\ContractResource;
use common\enums\ContractEnum;

class ContractCancelRequestForm extends BaseRequest
{

    public ContractCancelRequestResource $model;

    public $contract_id;
    public $producer_id;
    public $customer_id;
    public $type;
    public $message;
    public $pkcs7;

    public $regions = [];

    public function rules()
    {
        return [
            [['type', 'contract_id'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['message', 'customer_id', 'producer_id'], 'safe'],
            [['contract_id'], 'exist', 'skipOnError' => true, 'targetClass' => ContractResource::class, 'targetAttribute' => ['contract_id' => 'id']],
        ];
    }



    public function getResult()
    {
        $this->model = new ContractCancelRequestResource();
        $company_id = \Yii::$app->user->identity->company_id;
        $contract = ContractResource::findOne($this->contract_id);

        $isCustomer = true;

        if ($contract->customer_id == $company_id) {
            $isCustomer = true;
        } else if ($contract->producer_id == $company_id) {
            $isCustomer = false;
        } else {
            $this->addError("error", t("Shartnoma sizga tegishli emas"));
            return false;
        }

        if ($contract->status == ContractEnum::STATUS_DONE) {
            $this->addError("error", t("Shartnoma tugatilgan xolatda, bekor qilish mumkin emas"));
            return false;
        }

        if ($contract->status == ContractEnum::STATUS_CANCEL_PROCESS) {
            $this->addError("error", t("Shartnoma bekor qilishga so'rov avval yuborilgan"));
            return false;
        }


        $transaction = \Yii::$app->db->beginTransaction();

        $this->model->created_at = date("Y-m-d H:i:s");
        $this->model->created_by = \Yii::$app->user->id;

        if ($isCustomer)
            $this->model->customer_id = $contract->customer_id;
        else
            $this->model->producer_id = $contract->producer_id;

        $this->model->type = $this->type;
        $this->model->last_contract_status = $contract->status;
        $this->model->status = ContractEnum::STATUS_REQUEST_NEW;
        $att = $this->attributes;
        $this->model->setAttributes($att, false);
        if ($this->model->attributes && $this->model->validate() && $this->model->save()) {

            $contract->updateAttributes([
                'status' => ContractEnum::STATUS_CANCEL_PROCESS,
                'customer_cancel_date' => $this->customer_id ? date("Y-m-d H:i:s") : null,
                'producer_cancel_date' => $this->producer_id ? date("Y-m-d H:i:s") : null,
            ]);

            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}