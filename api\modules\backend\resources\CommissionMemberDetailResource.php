<?php

namespace api\modules\backend\resources;

use common\models\CommissionMember;
use common\models\query\NotDeletedFromCompanyQuery;

class CommissionMemberDetailResource extends CommissionMember {

    public function fields (){
        return ['id', 'tin', 'pinfl', 'fullname', 'passport_serie', 'passport_number', 'mail', 'phone', 'birthday', 'position', 'company_name'];
    }

    public function extraFields() {
        return [
            'commissionGroupMembers'
        ];
    }

    public function getCommissionGroupMembers (){
        return $this->hasMany(CommissionGroupMemberResource::class, ['commission_member_id' => 'id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}