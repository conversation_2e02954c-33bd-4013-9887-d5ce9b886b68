<?php


namespace api\modules\client\resources;


use api\modules\common\resources\UnitResource;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderRequirements;

class TenderRequirementsResource extends TenderRequirements
{

    public function fields()
    {
        return [
            'id', 'type', 'evaluation_method', 'obligation', 'file_name',
            'max_ball', 'min_ball', 'comparative_weight', 'title', 'description',
            'file_uploading_necessary', 'file_is_required', 'value', 'value_condition', 'unit' => 'unitResource'
        ];
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

    public function getUnitResource()
    {
        return $this->hasOne(UnitResource::class, ['id' => 'unit']);
    }
}