<?php

namespace api\modules\shop\forms;

use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\shop\resources\OrderResource;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;
use common\enums\ShopEnum;
use common\models\Region;
use common\models\shop\Order;
use common\models\shop\Product;
use Yii;
use yii\data\ActiveDataProvider;
use yii\helpers\ArrayHelper;

class DeliveryProductForm extends BaseRequest
{
    public $classifier_category_id;
    public $classifier_id;
    public $company_id;
    public $title;

    public $pageNo = 0;
    public $pageSize = 10;

    public function rules()
    {
        return [
            [['pageNo', 'pageSize'], 'integer'],
            [['classifier_category_id', 'classifier_id', 'title'], 'safe']
        ];
    }

    public function getResult()
    {
        $company = Yii::$app->user->identity->company;
        $companyId = $company->id;
        $regionId = $company->region_id;

        $currentUserCodesEShop = array_unique(ArrayHelper::getColumn(Product::find()->joinWith('classifier')
            ->andWhere([Product::tableName() . '.company_id' => $companyId])
            ->andWhere([Product::tableName() . '.state' => ProductEnum::SHOP_STATE_ACTIVE])
            ->andWhere([Product::tableName() . '.platform_display' => ProductEnum::PLATFORM_DISPLAY_E_SHOP])
            ->all(), 'classifier.code'));

        $data = [];

        if (count($currentUserCodesEShop) > 0) {
            foreach ($currentUserCodesEShop as $code) {
                $data[] = substr($code, 0, 8);
            }
        }

        $currentUserCodesNationalShop = array_unique(ArrayHelper::getColumn(Product::find()->joinWith('classifier')
            ->andWhere([Product::tableName() . '.company_id' => $companyId])
            ->andWhere([Product::tableName() . '.state' => ProductEnum::SHOP_STATE_ACTIVE])
            ->andWhere([Product::tableName() . '.platform_display' => ProductEnum::PLATFORM_DISPLAY_NATIONAL])
            ->all(), 'classifier.code'));

        $nationalData = [];

        if (count($currentUserCodesNationalShop) > 0) {
            foreach ($currentUserCodesNationalShop as $code) {
                $nationalData[] = substr($code, 0, 8);
            }
        }

        $order = OrderResource::find()
            ->innerJoin('product', '`product`.`id` = `order`.`product_id`')
            ->innerJoin('classifier', '`classifier`.`id` = `product`.`classifier_id`')
            ->andWhere([
                'or',
                [
                    'and',
                    ['in', 'substring(classifier.code from 1 for 8)', $nationalData],
                    '`order`.created_at >= product.active_date',
                    "(`product`.`platform_display` = 'national-shop' and exists(select 1
                                                                          from product_region
                                                                                   inner join region on product_region.region_id = region.id
                                                                          where product_id = product.id
                                                                            and region.parent_id = $regionId) )"
                ],
                [
                    'and',
                    ['in', 'substring(classifier.code from 1 for 8)', $data],
                    '`order`.created_at >= product.active_date',
                    "`product`.`platform_display` = 'e-shop'"
                ],
                [OrderResource::tableName() . '.company_id' => $companyId]
            ]);

        $order->andWhere(['shop_end' => null]);
        $order->andWhere(['!=', 'user_id', Yii::$app->user->id]);
        $order->andWhere(['order.status' => ShopEnum::ORDER_STATUS_ACTIVE]);
        $order->orderBy(['id' => SORT_DESC]);

        if ($this->title) {
            $order->andWhere(['like', Product::tableName() . '.title', $this->title]);
        }
        return paginate($order);
    }
}
