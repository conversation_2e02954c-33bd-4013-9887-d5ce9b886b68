<?php

namespace api\modules\auction\resources;

use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\CompanyResource;
use api\modules\common\resources\FileResource;
use api\modules\common\resources\RegionResource;
use common\enums\StatusEnum;
use common\models\auction\Auction;
use common\models\auction\AuctionHistory;
use yii\helpers\ArrayHelper;

class AuctionActiveResource extends Auction
{
    public function fields()
    {
        return [
            'id',
            'lot',
            'updated_at',
            'auction_end',
            'total_sum' => function ($model) {
                return $model->total_sum / 100;
            },
            'status',
            'delivery_basis',
            'company',
            'delivery_period',
            'receiver_email',
            'receiver_phone',
            'address',
            'nextPrice',
            'currentPrice',
            'currentOffer',
            'responsible_person',
            'responsible_person_phone'
        ];
    }

    public function extraFields()
    {
        return [
            'auctionFiles',
            'offers',
            'auctionConditions',
            'classifiers',
            'auctionClassifiers',
            'classifierCountry',
        ];
    }

    public function getOffers()
    {
        return AuctionOfferResource::find()->where(['auction_id' => $this->id])->orderBy(['create' => SORT_ASC])->all();
//        return $this->hasMany(AuctionOfferResource::class, ['auction_id' => 'id']);
    }

    public function getCompany()
    {
        return $this->hasOne(CompanyResource::class, ['id' => 'company_id']);
    }

    public function getClassifiers()
    {
        $auctionClassifiers = $this->auctionClassifiers;
        $classfiers = ClassifierResource::find()->where(['id' => ArrayHelper::getColumn($auctionClassifiers, 'classifier_id')])->all();
        return $classfiers;
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getRegion()
    {
        return $this->hasOne(RegionResource::class, ['id' => 'region_id']);
    }


    public function getAuctionClassifiers()
    {
        return AuctionClassifierShortResource::find()->where(['auction_id' => $this->id, 'status' => StatusEnum::STATUS_ACTIVE])->all();
//        return $this->hasMany(AuctionClassifierResource::class, ['auction_id' => 'id']);
    }


    public function getAuctionConditions()
    {
        return AuctionConditionResource::find()->where(['auction_id' => $this->id, 'status' => StatusEnum::STATUS_ACTIVE])->all();
//        return $this->hasMany(AuctionConditionResource::class, ['auction_id' => 'id']);
    }


    public function getAuctionFiles()
    {
        return AuctionFileResource::find()->where(['auction_id' => $this->id, 'deleted_at' => null])->orderBy(['created_at' => SORT_ASC])->all();
//
//        return $this->hasMany(AuctionFileResource::class, ['auction_id' => 'id']);
    }

}
