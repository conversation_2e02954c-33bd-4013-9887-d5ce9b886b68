<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%product_draft_region}}`.
 */
class m250717_110937_create_product_draft_region_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%product_draft_region}}', [
            'id' => $this->primaryKey(),
            'product_id' => $this->integer(),
            'region_id' => $this->integer(),

            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);

        $this->createIndex("idx_product_draft_region_product_id", "product_draft_region", "product_id");
        $this->createIndex("idx_product_draft_region_region_id", "product_draft_region", "region_id");

        $this->addForeignKey("fk_product_draft_region_product_id", "product_draft_region", "product_id", "product_draft", "id", "cascade", "cascade");
        $this->addForeignKey("fk_product_draft_region_region_id", "product_draft_region", "region_id", "region", "id", "cascade", "cascade");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey("fk_product_draft_region_product_id", "product_region");
        $this->dropForeignKey("fk_product_draft_region_region_id", "product_region");

        $this->dropIndex("idx_product_draft_region_product_id", "product_region");
        $this->dropIndex("idx_product_draft_region_product_id", "product_region");

        $this->dropTable('{{%product_draft_region}}');
    }
}
