<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\CommissionMemberResource;
use api\modules\tender\resources\CommissionMemberShortResource;
use common\enums\TenderEnum;

class CommissionMemberNotByTenderIdFilter extends BaseRequest {

    public $id;

    public function rules (){
        return [
            [['id'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['id'], 'integer'],
        ];
    }

    public function getResult()
    {
        $query = CommissionMemberShortResource::find()->notDeleted()
            ->where(['commission_member.status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['commission_member.company_id' => \Yii::$app->user->identity->company_id])
            ->andWhere('not exists(select 1 from tender_commission_member where commission_member_id=commission_member.id and tender_id='.$this->id.')')
            ->all();
            return $query;

    }
}