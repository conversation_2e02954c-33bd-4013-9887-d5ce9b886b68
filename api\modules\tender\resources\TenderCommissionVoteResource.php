<?php


namespace api\modules\tender\resources;


use common\models\CommissionMember;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderCommissionVote;

/**
 * @property CommissionMemberShortResource $commissionMember
 * @property string $commissionMemberFullName
 */

class TenderCommissionVoteResource extends TenderCommissionVote
{

    public function fields()
    {
        return [
            'tender_id',
            'commission_member_id',
            'description',
            'role',
            'vote',
            'status',
            'created_at'
        ];
    }

    public function extraFields()
    {
        return [
            'commissionMember',
            'tender'
        ];
    }

    public function getTender()
    {
        return $this->hasOne(TenderResource::class, ['id' => 'tender_id']);
    }

    public function getCommissionMember()
    {
        return $this->hasOne(CommissionMemberShortResource::class, ['id' => 'commission_member_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

    public function getCommissionMemberFullName()
    {
        if ($this->commissionMember)
            return $this->commissionMember->fullname;
        return null;
    }
}