<?php

namespace api\modules\common\forms;


use api\components\BaseRequest;
use Yii;
use yii\base\Exception;

class Pkcs7Form extends BaseRequest
{
    public $pkcs7;
    public function rules()
    {
        return [
            [['pkcs7'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            ['pkcs7', 'string']
        ];
    }
    /**
     * @throws Exception
     */
    public function getResult()
    {
        $user = Yii::$app->user->identity;
        return verifyPkcs7($this->pkcs7, $user);
    }
}
