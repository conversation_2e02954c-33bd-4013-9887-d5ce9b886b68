<?php

namespace api\modules\client\controllers;

use api\components\ApiController;
use app\modules\client\filters\SoleSupplierFilter;
use Yii;
use yii\filters\auth\HttpBearerAuth;

class SoleSupplierController extends ApiController
{
    public function behaviors(): array
    {
        $parent = parent::behaviors();
        $parent['bearerAuth'] = [
            'class' => HttpBearerAuth::class,
            'except' => [
                'index',
            ]
        ];
        return $parent;
    }

    public function actionIndex(): array
    {
        return $this->sendResponse(
            new SoleSupplierFilter(),
            Yii::$app->request->queryParams,
        );
    }
}