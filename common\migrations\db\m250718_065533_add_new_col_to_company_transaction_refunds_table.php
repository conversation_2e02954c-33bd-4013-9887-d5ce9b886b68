<?php

use yii\db\Migration;

class m250718_065533_add_new_col_to_company_transaction_refunds_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->alterColumn('company_transaction_refunds', 'sum', $this->bigInteger()->unsigned());
        $this->addColumn('company_transaction_refunds', 'bank_account_id', $this->integer()->after('id'));
        $this->createIndex(
            'idx-company_transaction_refunds-bank_account_id',
            'company_transaction_refunds',
            'bank_account_id'
        );
        $this->addForeignKey(
            'fk-company_transaction_refunds-bank_account_id',
            'company_transaction_refunds',
            'bank_account_id',
            'company_bank_account',
            'id'
        );
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey('fk-company_transaction_refunds-bank_account_id', 'company_transaction_refunds');
        $this->dropIndex('idx-company_transaction_refunds-bank_account_id', 'company_transaction_refunds');
        $this->dropColumn('company_transaction_refunds', 'bank_account_id');
    }
}
