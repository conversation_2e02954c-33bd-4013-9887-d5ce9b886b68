<?php


namespace api\modules\client\controllers;


use api\components\ApiController;
use api\modules\client\filters\TenderFilter;
use api\modules\client\filters\TenderProtestFilter;
use api\modules\client\filters\TenderRequestHistoryFilter;
use api\modules\client\forms\TenderProtestDeleteForm;
use api\modules\client\forms\TenderProtestForm;
use api\modules\client\forms\TenderRequestCancelForm;
use api\modules\client\forms\TenderRequestForm;
use api\modules\client\forms\TenderRequestUpdateForm;
use api\modules\client\resources\TenderForEditResource;
use api\modules\client\resources\TenderRequestResource;
use api\modules\client\resources\TenderResource;
use api\modules\tender\resources\TenderActiveLotsDetailResource;
use common\behaviors\RoleAccessBehavior;
use common\enums\TenderEnum;
use yii\web\NotFoundHttpException;

class TenderController extends ApiController
{
    public function behaviors()
    {
        $parent = parent::behaviors();
        $parent['accessByRole'] = [
            'class' => RoleAccessBehavior::class,
            'rules' => [
                'index' => ['user'],
                'view' => ['user'],
                'view-for-edit' => ['user'],
                'active-lot' => ['user'],
                'send-request' => ['user'],
                'update-request' => ['user'],
                'cancel-request' => ['user'],
                'protest-request' => ['user'],
                'my-protest' => ['user'],
                'my-protest-delete' => ['user'],
                'request-history' => ['user'],
            ],
        ];
        return $parent;
    }

    public function actionIndex()
    {
        return $this->sendResponse(
            new TenderFilter(),
            \Yii::$app->request->queryParams
        );
    }

    public function actionView($id)
    {
        $request = TenderRequestResource::find()
            ->where([
                'tender_id' => $id,
                'company_id' => \Yii::$app->user->identity->company_id
            ])->exists();
        if ($request) {
            return $this->sendModel($this->findAnyOne($id));
        }

        return $this->sendModel($this->findAnyOne($id, TenderEnum::STATE_READY));
    }

    /**
     * @throws NotFoundHttpException
     */
    public function actionViewForEdit($id)
    {
        $model = TenderForEditResource::find()
            ->join('inner join', 'tender_request', 'tender_request.tender_id=tender.id')
            ->notDeleted()
            ->andWhere(['tender_request.id' => $id, 'tender_request.status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_request.company_id' => \Yii::$app->user->identity->company_id])
            ->andWhere(['tender.state' => TenderEnum::STATE_READY])
            ->one();

        if (!$model) throw new NotFoundHttpException("Tender is not found");

        return $this->sendModel($model);
    }

    //so'rov berish

    /**
     * @throws NotFoundHttpException
     */
    public function actionActiveLot($id)
    {
        $model = TenderActiveLotsDetailResource::find()
            ->where(['state' => TenderEnum::STATE_READY, 'id' => $id])->one();
        if (!$model) throw new NotFoundHttpException(t("Tender topilmadi"));
        return $this->sendModel($model);
    }

    public function actionSendRequest($id)
    {
        return $this->sendResponse(
            new TenderRequestForm($this->findAnyOne($id, TenderEnum::STATE_READY)),
            \Yii::$app->request->bodyParams
        );
    }

    //tahrirlash
    public function actionUpdateRequest($id)
    {
        return $this->sendResponse(
            new TenderRequestUpdateForm($this->findRequest($id)),
            \Yii::$app->request->bodyParams
        );
    }

    //bekor qilish
    public function actionCancelRequest($id)
    {
        return $this->sendResponse(
            new TenderRequestCancelForm($this->findRequest($id)),
            \Yii::$app->request->bodyParams
        );
    }

    //muxokama etiroz yuborish
    public function actionProtestRequest($id)
    {
        return $this->sendResponse(
            new TenderProtestForm($this->findAnyOne($id, TenderEnum::STATE_MADE_PROTOCOL)),
            \Yii::$app->request->bodyParams
        );
    }

    public function actionMyProtest($id)
    {
        return $this->sendResponse(
            new TenderProtestFilter($this->findAnyOne($id)),
            \Yii::$app->request->bodyParams
        );
    }

    public function actionMyProtestDelete($id)
    {
        return $this->sendResponse(
            new TenderProtestDeleteForm($this->findAnyOne($id, TenderEnum::STATE_MADE_PROTOCOL)),
            \Yii::$app->request->bodyParams
        );
    }

    //request lar
    public function actionRequestHistory($id)
    {
        return $this->sendResponse(
            new TenderRequestHistoryFilter($this->findAnyOne($id)),
            \Yii::$app->request->queryParams
        );
    }


    private function findAnyOne($id, $state = null)
    {
        $model = TenderResource::find()->notDeleted()->andWhere(['id' => $id]);

        if ($state != null && in_array($state, TenderEnum::STATE_LIST)) {
            $model->andWhere(['state' => $state]);
        }
        $model = $model->one();

        if (!$model) throw new NotFoundHttpException("Tender is not found");

        return $model;
    }

    /**
     * @throws NotFoundHttpException
     */
    private function findRequest($id)
    {
        $model = TenderRequestResource::find()
            ->notDeleted()
            ->andWhere([
                'id' => $id,
                'status' => TenderEnum::STATUS_ACTIVE,
                'company_id' => \Yii::$app->user->identity->company_id
            ])->one();
        if (!$model) throw new NotFoundHttpException(t("Tender topilmadi"));

        return $model;
    }

}