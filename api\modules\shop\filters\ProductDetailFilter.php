<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;

class ProductDetailFilter extends BaseRequest
{
    public ProductResource $product;

    public function __construct($product, $params = [])
    {
        $this->product = $product;

        parent::__construct($params);
    }

    public function getResult()
    {
        $relatedModels = ProductResource::find()
            ->where(['classifier_id' => $this->product->classifier_id])
            ->andWhere(['!=', 'product.id', $this->product->id])
            ->andWhere(['=', 'product.state', ProductEnum::SHOP_STATE_ACTIVE])
            ->andWhere(['>','inactive_date',date('Y-m-d H:i:s')])
            ->orderBy('id desc')
            ->limit(5)->all();

      return $relatedModels;
    }
}
