<?php


namespace api\modules\backend\resources;


use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderCommissionMember;

class TenderCommissionMemberResource extends TenderCommissionMember
{

    public function fields()
    {
        return [
            'commission_group_member_id',
            'commission_member_id',
            'role',
            'status',
        ];
    }
    public function extraFields()
    {
        return ['commissionMember'];
    }

    public function getCommissionMember()
    {
        return $this->hasOne(CommissionMemberResource::class, ['id' => 'commission_member_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}