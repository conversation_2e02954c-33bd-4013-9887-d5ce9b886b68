<?php
namespace api\modules\backend\forms;

use api\modules\backend\resources\TenderModeratorLogResource;
use api\modules\backend\resources\TenderResource;
use common\enums\TenderEnum;

class TenderRejectForm extends \api\components\BaseRequest
{
    public TenderResource $model;
    public TenderModeratorLogResource $log;
    public $description;

    public function rules()
    {
        return [
            ['description', 'required', 'message' => t('{attribute} yuborish majburiy')],
            ['description', 'string', 'max' => 255],
        ];
    }

    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;
        $this->log = new TenderModeratorLogResource();

        parent::__construct($params);
    }

    public function getResult()
    {
        if($this->model->status==TenderEnum::STATUS_NEW && $this->model->state == TenderEnum::STATE_NEW){
            $this->log->tender_id = $this->model->id;
            $this->log->state = TenderEnum::STATE_REJECT_MODERATOR;
            $this->log->moderator_pinfl = \Yii::$app->user->identity->username;
            $this->log->description = $this->description;
            if($this->log->save()){
                $this->model->state = TenderEnum::STATE_REJECT_MODERATOR;
                if($this->model->save()){
                    return true;
                } else {
                    $this->addErrors($this->model->errors);
                    return $this->model->errors;
                }
            }
            $this->addErrors($this->log->errors);
            return false;
        } else {
            $this->addError("state", "Tender moderatsiya holatida emas");

            return false;
        }


    }
}