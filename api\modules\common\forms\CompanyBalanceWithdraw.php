<?php

namespace api\modules\common\forms;

use api\components\BaseRequest;
use api\modules\common\resources\CompanyBankAccountResource;
use api\modules\common\resources\TransactionRefundsResource;
use common\enums\CompanyTransactionEnum;
use common\enums\OperationTypeEnum;
use common\models\VirtualTransaction;
use Yii;
use yii\db\Exception;

class CompanyBalanceWithdraw extends BaseRequest
{
    public ?int $bank_account_id = null;
    public ?string $account_number = null;
    public ?int $amount = null;
    public ?string $comment = null;
    public TransactionRefundsResource $model;
    public function __construct(TransactionRefundsResource $model, $params = [])
    {
        $this->model = $model;
        parent::__construct($params);
    }

    public function rules(): array
    {
        return [
            [['bank_account_id', 'amount', 'comment','account_number'], 'required'],
            [['amount'], 'integer',],
            [['comment',], 'string', 'max' => 255],
            [
                ['account_number'],
                'match',
                'pattern' => '/^[0-9]{20}+$/',
            ],
        ];
    }

    /**
     * @throws Exception
     * @throws \yii\base\Exception
     * @throws \Throwable
     */
    public function getResult()
    {
        $_company = _company(); // birja company
        $identity = Yii::$app->user->identity;

        if (!$identity || !($company = $identity->company))
            throw new Exception('Unauthorized');

        $account = CompanyBankAccountResource::findOne(['id' => $this->bank_account_id,'company_id' => $company->id,]);

        if (!$account)
            throw new Exception('Bank account not found');

        if (!hasMoney($company, $this->amount))
            throw new Exception('Недостаточно средств');

        $check = TransactionRefundsResource::find()->where(['company_id' => $company->id, 'status' => CompanyTransactionEnum::REFUNDS_STATUS_NEW])->one();
        if ($check) {
            $this->addError('amount', t('Avvalgi tasdiqlanmagan ariza mavjud'));
            return false;
        }

        $amount = $this->amount * 100; // in tiyin

        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $this->model->bank_account_id = $this->bank_account_id;
            $this->model->account_number = $this->account_number;
            $this->model->comment = $this->comment;
            $this->model->sum = $amount;
            $this->model->status = CompanyTransactionEnum::REFUNDS_STATUS_NEW;
            $this->model->created_at = date("Y-m-d H:i:s");
            $this->model->created_by = Yii::$app->user->id;
            $this->model->company_id = $company->id;
            if (!$this->model->save()) {
                $transaction->rollBack();
                $this->addErrors($this->model->errors);
                return false;
            }

            VirtualTransaction::saveTransaction(
                $company,
                $_company,
                $company->isBudget() ? OperationTypeEnum::P_B_31101 : OperationTypeEnum::P_K_30101,
                $company->isBudget() ? OperationTypeEnum::A_50113 : OperationTypeEnum::A_50111,
                $amount,
                $this->comment,
                null,
                null,
                null,
                OperationTypeEnum::WITHDRAW_FUNDS,
            );

            $transaction->commit();
            return true;
        } catch (\Throwable $e) {
            $transaction->rollBack();
            $this->addError('error', $e->getMessage());
            return false;
        }
    }
}