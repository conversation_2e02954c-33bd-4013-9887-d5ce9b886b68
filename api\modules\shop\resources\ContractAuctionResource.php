<?php

namespace api\modules\shop\resources;

use api\modules\common\resources\CompanyShortResource;
use api\modules\common\resources\CompanyTransactionResource;
use api\modules\common\resources\FileResource;
use common\enums\CompanyTransactionEnum;
use common\enums\ContractEnum;
use common\models\Contract;
use yii\helpers\ArrayHelper;

class ContractAuctionResource extends Contract
{
    public function fields()
    {
        return [
            'created_at',
            'price' => function ($model) {
                return $model->price / 100;
            },
            'status',
            'customer_signed',
            'producer_signed',
            'producer_sign_date',
            'customer_sign_date',
            'customer_pay_date',
            'customer_mark_delivered_date',
            'customer_mark_delivered_date',
            'customer_cancel_date',
            'producer_cancel_date',
            'stateName',
            'id',
            'auction',
            'file_id',
            'reserve',
            'number'
        ];
    }

    public function extraFields()
    {
        return [
            'producer',
            'customer',
            'contractCancelRequest',
            'transaction',
            'file',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getAuction()
    {
        return $this->hasOne(AuctionMyLotsResource::class, ['id' => 'auction_id']);
    }
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'file_id']);
    }
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCustomer()
    {
        return $this->hasOne(CompanyShortResource::class, ['id' => 'customer_id']);
    }
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProducer()
    {
        return $this->hasOne(CompanyShortResource::class, ['id' => 'producer_id']);
    }
    /**
     * @return \yii\db\ActiveQuery
     */
    public function getTransaction()
    {
        return $this->hasOne(CompanyTransactionResource::class, ['product_id' => 'order_id'])->andWhere([CompanyTransactionResource::tableName().'.type'=> CompanyTransactionEnum::TYPE_BLOCK_COMMISION]);
    }
}
