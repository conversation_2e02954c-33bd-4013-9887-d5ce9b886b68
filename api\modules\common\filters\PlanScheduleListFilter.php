<?php

namespace api\modules\common\filters;

use api\components\BaseRequest;
use api\modules\common\resources\PlanScheduleListResource;
use common\enums\StatusEnum;
use Yii;

class PlanScheduleListFilter extends BaseRequest
{
    public  $year;
    public  $classifier;
    public  $company;

    public function rules()
    {
        return [
            [['year', 'classifier', 'company'], 'safe'],
            ['year', 'number','min' => 1970, 'max' => date('Y'),],
        ];
    }

    public function getResult(): array
    {
        $query = PlanScheduleListResource::find();
        if ($this->year)
            $query->andWhere(['year' => $this->year]);
        if ($this->company)
            $query->andWhere(['company_id' => $this->company]);
        $query->andWhere(['status' => StatusEnum::STATUS_ACTIVE]);
        return paginate($query);
    }
}