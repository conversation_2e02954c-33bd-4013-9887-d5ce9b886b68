<?php

use yii\helpers\Html;
use yii\bootstrap4\ActiveForm;

/**
 * @var yii\web\View $this
 * @var common\models\auction\Auction $model
 * @var yii\bootstrap4\ActiveForm $form
 */
?>

<div class="auction-search">

    <?php $form = ActiveForm::begin([
        'action' => ['index'],
        'method' => 'get',
    ]); ?>

    <?php echo $form->field($model, 'id') ?>
    <?php echo $form->field($model, 'company_id') ?>
    <?php echo $form->field($model, 'classifier_category_id') ?>
    <?php echo $form->field($model, 'status') ?>
    <?php echo $form->field($model, 'total_sum') ?>
    <?php // echo $form->field($model, 'cancel_reason') ?>
    <?php // echo $form->field($model, 'auction_end') ?>
    <?php // echo $form->field($model, 'cancel_date') ?>
    <?php // echo $form->field($model, 'payment_status') ?>
    <?php // echo $form->field($model, 'payment_date') ?>
    <?php // echo $form->field($model, 'delivery_period') ?>
    <?php // echo $form->field($model, 'payment_period') ?>
    <?php // echo $form->field($model, 'receiver_email') ?>
    <?php // echo $form->field($model, 'receiver_phone') ?>
    <?php // echo $form->field($model, 'region_id') ?>
    <?php // echo $form->field($model, 'zip_code') ?>
    <?php // echo $form->field($model, 'address') ?>
    <?php // echo $form->field($model, 'created_at') ?>
    <?php // echo $form->field($model, 'updated_at') ?>
    <?php // echo $form->field($model, 'deleted_at') ?>
    <?php // echo $form->field($model, 'created_by') ?>
    <?php // echo $form->field($model, 'updated_by') ?>
    <?php // echo $form->field($model, 'account') ?>
    <?php // echo $form->field($model, 'plan_schedule_id') ?>

    <div class="form-group">
        <?php echo Html::submitButton('Search', ['class' => 'btn btn-primary']) ?>
        <?php echo Html::resetButton('Reset', ['class' => 'btn btn-secondary']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
