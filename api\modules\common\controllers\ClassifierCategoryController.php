<?php


namespace api\modules\common\controllers;


use api\components\ApiController;
use api\modules\common\filters\ClassifierCategoryFilter;
use api\modules\common\filters\ClassifierCategoryListFilter;
use api\modules\common\filters\ClassifierWithChildFilter;
use Yii;

class ClassifierCategoryController extends ApiController
{
    public function actionIndex()
    {
        return $this->sendResponse(
            new ClassifierCategoryFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionClassifierCategoryIndex()
    {
        return $this->sendResponse(
            new ClassifierCategoryFilter(),
            Yii::$app->request->queryParams
        );
    }


    public function actionClassifierWithChild()
    {
        return $this->sendResponse(
            new ClassifierWithChildFilter(),
            Yii::$app->request->queryParams
        );
    }
    public function actionClassifierCategoryList()
    {
        return $this->sendResponse(
            new ClassifierCategoryListFilter(),
            Yii::$app->request->queryParams
        );
    }
}