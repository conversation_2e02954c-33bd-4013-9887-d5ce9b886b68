<?php

namespace backend\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use backend\models\Product;

/**
 * ProductSearch represents the model behind the search form about `backend\models\Product`.
 */
class ProductSearch extends Product
{
    public $classifier;
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id', 'classifier_category_id', 'classifier_id', 'company_id', 'year', 'quantity', 'unit_id', 'min_order', 'max_order', 'country_id', 'state', 'delivery_period', 'delivery_period_type', 'warranty_period', 'warranty_period_type', 'expiry_period', 'expiry_period_type', 'status', 'is_have_license', 'created_by', 'updated_by'], 'safe'],
            [['account_number', 'title', 'brand_title', 'description', 'type', 'unit_price', 'made_in', 'platform_display', 'active_date', 'inactive_date', 'created_at', 'updated_at', 'deleted_at', 'delete_reason'], 'safe'],
            [['price'], 'safe'],
            [['classifier'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params,$state=300)
    {
        $query = Product::find();
        if ($state){
            $query->andWhere(['in','state',[$state]]);
        }

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);


        if (!($this->load($params) && $this->validate())) {
            return $dataProvider;
        }

        if ($this->classifier){
            $query->leftJoin('classifier','"product"."classifier_id" = "classifier"."id"');
            $query->andWhere(['or',
                ['like','classifier.title_uz',$this->classifier],
                ['like','classifier.title_ru',$this->classifier],
                ['like','classifier.title_en',$this->classifier],
                ]
            );
        }

        $query->andFilterWhere([
            'id' => $this->id,
            'classifier_category_id' => $this->classifier_category_id,
//            'classifier_id' => $this->classifier_id,
            'company_id' => $this->company_id,
            'year' => $this->year,
            'quantity' => $this->quantity,
            'unit_id' => $this->unit_id,
            'price' => $this->price,
            'min_order' => $this->min_order,
            'max_order' => $this->max_order,
            'country_id' => $this->country_id,
            'state' => $this->state,
            'delivery_period' => $this->delivery_period,
            'delivery_period_type' => $this->delivery_period_type,
            'warranty_period' => $this->warranty_period,
            'warranty_period_type' => $this->warranty_period_type,
            'expiry_period' => $this->expiry_period,
            'expiry_period_type' => $this->expiry_period_type,
            'status' => $this->status,
            'active_date' => $this->active_date,
            'inactive_date' => $this->inactive_date,
            'is_have_license' => $this->is_have_license,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'deleted_at' => $this->deleted_at,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
        ]);

        $query->andFilterWhere(['like', 'account_number', $this->account_number])
            ->andFilterWhere(['like', 'title', $this->title])
            ->andFilterWhere(['like', 'brand_title', $this->brand_title])
            ->andFilterWhere(['like', 'description', $this->description])
            ->andFilterWhere(['like', 'type', $this->type])
            ->andFilterWhere(['like', 'unit_price', $this->unit_price])
            ->andFilterWhere(['like', 'made_in', $this->made_in])
            ->andFilterWhere(['like', 'platform_display', $this->platform_display])
            ->andFilterWhere(['like', 'delete_reason', $this->delete_reason]);

        return $dataProvider;
    }
}
