<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderCommissionVoteResource;

class TenderCommissionVoteFilter extends BaseRequest
{
    public $tenderId;

    public function rules (){
        return [
            [['tenderId'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['tenderId'], 'integer'],
        ];
    }

    public function getResult()
    {
        $query = TenderCommissionVoteResource::find()->notDeleted()
            ->andWhere(['tender_id' => $this->tenderId])
            ->all();
        return $query;

    }
}