<?php


namespace api\modules\tender\forms;


use api\components\BaseModel;
use api\components\BaseRequest;
use api\modules\common\resources\FileResource;
use api\modules\tender\resources\TenderActionRequestResource;
use api\modules\tender\resources\TenderClassifierResource;
use api\modules\tender\resources\TenderCommissionMemberResource;
use api\modules\tender\resources\TenderCommissionVoteResource;
use api\modules\tender\resources\TenderRequestRatingCommissionResource;
use api\modules\tender\resources\TenderRequirementsResource;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;
use yii\base\Exception;

class TenderCancelActionForm extends BaseModel
{

    public TenderResource $model;
    public $file_id;
    public $pkcs7;
    public $type;

    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [['file_id'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['file_id', 'type'], 'integer'],
            ['type', 'checkType'],
            [['file_id'], 'exist', 'skipOnError' => true, 'targetClass' => FileResource::class, 'targetAttribute' => ['file_id' => 'id']],
        ];
    }

    public function checkType($attribute, $params)
    {
        if ($this->type == TenderEnum::TENDER_ACTION_TYPE_CANCEL || $this->type == TenderEnum::TENDER_ACTION_TYPE_UPDATE) {
            return true;
        } else {
            $this->addError('type', t("So'rov turi noto'g'ri yuborildi"));
            return false;
        }
    }

    public function getResult()
    {

        if (!in_array($this->model->state, [TenderEnum::STATE_READY, TenderEnum::STATE_READY_TO_RATING])) {
            $this->addError("error","Tender xolati bekor qilish so'rovini yuborish uchun muqobil xolatda emas");
            return false;
        }
        $commissionId = \Yii::$app->user->identity->commissionMemberId;
        $tenderCom = $this->model->getCommissionMember($commissionId);
        if ($tenderCom->role != TenderEnum::ROLE_SECRETARY) {
            $this->addError("error", t("Ruxsat mavjud emas"));
            return false;
        }

        $check = TenderActionRequestResource::findOne(['tender_id' => $this->model->id, 'status' => TenderEnum::TENDER_ACTION_STATUS_NEW]);
        if($check){
            $this->addError("error","Jarayondagi so'rov mavjud");
            return false;
        }

        $transaction = \Yii::$app->db->beginTransaction();

        $model = new TenderActionRequestResource();
        $model->tender_id = $this->model->id;
        $model->file_id = $this->file_id;
        $model->status = TenderEnum::TENDER_ACTION_STATUS_NEW;
        $model->type = $this->type;
        if($model->save()){
            $transaction->commit();
            return true;
        } else {
            $transaction->rollBack();
            $this->addErrors($model->errors);
            return false;
        }

    }
}
