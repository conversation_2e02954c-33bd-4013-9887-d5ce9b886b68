<?php

namespace api\modules\common\resources;

use common\models\CompanyTransactionRefunds;

class TransactionRefundsResource extends CompanyTransactionRefunds
{


    public function fields()
    {
        return [
            'id', 'transaction_id', 'account_number', 'sum' => function($model){return $model->sum / 100;}, 'status', 'comment','created_at'
        ];
    }

    public function extraFields()
    {
        return [
            'company',
            'transaction'
        ];
    }

}
