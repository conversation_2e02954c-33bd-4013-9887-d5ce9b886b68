<?php


namespace api\modules\common\forms;


use api\components\BaseRequest;
use common\models\Bank;
use common\models\CompanyBankAccount;
use Yii;
use yii\base\Exception;

class CompanyBankAccountUpdateForm extends BaseRequest
{
    public $id;
    public $account_number;
    public $mfo;
    public $is_main;
    public CompanyBankAccount $model;

    public function rules()
    {
        return [
            [['account_number', 'mfo'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [
                ['account_number'],
                'match',
                'pattern' => '/^[0-9]+$/',
            ],
            [
                ['account_number'],
                'string',
                'min' => 20,
                'max' => 20,
                'message' => Yii::t('api', 'Hisob raqam uzunligi 20 ta bolishi kerak'),
            ],
            [['mfo'], 'exist', 'skipOnError' => true, 'targetClass' => Bank::class, 'targetAttribute' => ['mfo' => 'mfo']],
            [
                ['mfo'],
                'string',
                'min' => 5,
                'max' => 5,
            ],
            [
                ['mfo'],
                'match',
                'pattern' => '/^[0-9]+$/',
            ],
            [['id'], 'number'],
            [['id'], 'exist', 'skipOnError' => true, 'targetClass' => CompanyBankAccount::class, 'targetAttribute' => ['id' => 'id']],
            [['is_main'], 'integer'],
        ];
    }

    public function __construct(CompanyBankAccount $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function getResult()
    {
        $model = $this->model;

        $main = CompanyBankAccount::findOne(['is_main' => 1, 'company_id' => \Yii::$app->user->identity->company_id]);
        if (!$main) {
            $this->addError("account_number", t("Asosiy hisob raqam mavjud emas"));
            return false;
        }
        $main_characters = substr($main->account, 9, 8);
        $new_characters = substr($this->account_number, 9, 8);
        if ($main_characters != $new_characters) {
            $this->addError("account_number", t("Yangi hisob raqam asosiy hisob raqamga mos emas :" . $main_characters));
            return false;
        }

        $transaction = Yii::$app->db->beginTransaction();
        if ($this->is_main == 1) {
            CompanyBankAccount::updateAll(['is_main' => 0], ['company_id' => \Yii::$app->user->identity->company_id]);
        }

        $model->mfo = $this->mfo;
        $model->account = $this->account_number;
        $model->is_main = $this->is_main;
        $model->updated_at = date("Y-m-d H:i:s");
        $model->updated_by = \Yii::$app->user->identity->id;
        if (!$model->save()) {
            $transaction->rollBack();
            $this->addErrors($model->getErrors());
            return false;
        }
        $transaction->commit();
        return true;
    }

}