<?php


namespace api\modules\common\resources;


use api\modules\shop\resources\ContractResource;
use common\models\CompanyTransaction;

class CompanyTransactionFullResource extends CompanyTransaction
{
    public function fields()
    {
        return [
            'id', 'amount', 'transaction_date', 'description', 'typeNamePlusMinus', 'contract_id', 'order_id','auction_id','tender_id',
            'counterpartyTitle' => function($model){
                return $model->counterpartyTitle;
            },
            'counterpartyTin' => function($model){
                return $model->counterpartyTin;
            }
        ];
    }

    public function getContract()
    {
        return $this->hasOne(ContractResource::class, ['id' => 'contract_id']);
    }

    public function getCounterpartyTitle()
    {
        $contract = $this->contract;
        if (isset($contract) && $contract != null) {
            if ($contract->customer_id == \Yii::$app->user->identity->company_id) {
                return isset($contract->producer) ? isset($contract->producer->companyBankAccount) ? $contract->producer->companyBankAccount->account : "" : "";
            } else {
                return isset($contract->customer) ? isset($contract->customer->companyBankAccount) ? $contract->customer->companyBankAccount->account : "" : "";
            }
        }
        return '';
    }

    public function getCounterpartyTin()
    {
        $contract = $this->contract;
        if (isset($contract) && $contract != null) {
            if ($contract->customer_id != \Yii::$app->user->identity->company_id) {
                return $contract->customer->tin;
            } else {
                return $contract->producer->tin;
            }
        }
        return '';
    }

//    public function fields()
//    {
//        return ['id', 'created_at', 'amount', 'type', 'transaction_date', 'description', 'typeNamePlusMinus'];
//    }

}