<?php

namespace api\modules\common\forms;

use api\components\BaseRequest;
use api\modules\common\resources\PlanScheduleClassifierBudgetCreateResource;
use api\modules\common\resources\PlanScheduleClassifierCreateResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\PlanSchedulePropertiesResource;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\StatusEnum;
use common\enums\TenderEnum;
use common\models\Classifier;
use common\models\CompanyBankAccount;
use common\models\PlanScheduleClassifierUnit;
use Yii;

class PlanScheduleBudgetForm extends BaseRequest
{

    public $year;
    public $quarter;
    public $title;

    public $month;
    public $description;
    public $count; //TOVARAMOUNT
    public $price;
    public $expense; // xarajat moddasi
    public $accountId; // company_bank_account_id -> kls
    public $unitId; // Ўлчов бирлиги
    public $srok; //yetkazib berish muddati
    public $address; //yetkazib berish manzili
    public $classifierId; //tovar, tovarname
    public array $conditions = []; // dinamichniy maxsulotga qrab chiqadigani


    public PlanScheduleResource $model;

    public function __construct($params = [])
    {
        parent::__construct($params);
    }

    /**
     * {@inheritdoc}
     * #return, year qiymatini aniqlashtirish kerak
     */
    public function rules()
    {
        return [
            [['conditions', 'price', 'title', 'quarter', 'year', 'month', 'description', 'count', 'expense', 'accountId', 'srok', 'address', 'classifierId', 'unitId'], 'required', 'message' => t('{attribute} yuborish kerak')],
            [['count', 'month', 'srok', 'classifierId', 'expense', 'accountId', 'classifierId', 'unitId'], 'integer'],
            [['title', 'description'], 'string', 'max' => 255],
            ['quarter', 'integer', 'min' => 1, 'max' => 4],
            ['conditions', 'checkCondition'],
            ['price', 'checkPrice'],
            ['year', 'integer', 'min' => date("Y"), 'max' => date("Y", strtotime("+1 year"))],
        ];
    }

    public function checkCondition()
    {
        if (!isset($this->conditions) || !is_array($this->conditions)) {
            $this->addError("conditions", "condition to'g'ri formatda yuborilmagan");
            return false;
        }
        foreach ($this->conditions as $condition) {
            if (
                !isset($condition['prop_numb']) ||
                !isset($condition['prop_name']) ||
                !isset($condition['val_numb']) ||
                !isset($condition['val_name'])
            ) {
                $this->addError("conditions", "condition to'g'ri formatda yuborilmagan");
                return false;
            }
        }
        return true;
    }

    public function checkPrice()
    {
        if (!isset($this->price) || $this->price <= 0) {
            $this->addError("price", "price to'g'ri formatda yuborilmagan");
            return false;
        }
        return true;
    }

    public function attributeLabels()
    {
        return [
            'year' => Yii::t('main', 'Yil'),
            'quarter' => Yii::t('main', 'Chorak'),
            'title' => Yii::t('main', 'Reja-jadval nomi'),
            'classifiers' => Yii::t('main', 'Mahsulot'),
        ];
    }


    public function getResult()
    {
        $user = Yii::$app->user->identity;
        if (!$user->isBudget) {
            $this->addError("error", t("Ruxsat mavjud emas"));
            return false;
        }
        $transaction = Yii::$app->db->beginTransaction();
        $model = new PlanScheduleResource([
            'title' => $this->title,
            'company_id' => $user->company_id,
            'status' => StatusEnum::STATUS_NEW,
            'year' => $this->year,
            'quarter' => $this->quarter,
            'total_product_count' => 0,
        ]);
        $model->organ = $user->organ;

        if (!$model->save()) {
            $this->addErrors($model->errors);
            $transaction->rollBack();
            return false;
        }

        if (PlanScheduleClassifierResource::find()->notDeleted()
            ->andWhere(['plan_schedule_id' => $model->id, 'classifier_id' => $this->classifierId, 'status' => TenderEnum::STATUS_ACTIVE])->exists()
        ) {
            $this->addError("classifiers", t("Bu maxsulot avval qo'shilgan"));
            $transaction->rollBack();
            return false;
        }

        $classfier = Classifier::findOne($this->classifierId);
        if (!$classfier) {
            $this->addError("classifiers", t("Bu mahsulot topilmadi"));
            $transaction->rollBack();
            return false;
        }

        $account = CompanyBankAccount::findOne(['id' => $this->accountId, 'company_id' => $user->company_id, 'organ' => $user->organ]);
        if (!$account) {
            $this->addError("account", t("Xisob raqam topilmadi"));
            $transaction->rollBack();
            return false;
        }

        $product = new PlanScheduleClassifierBudgetCreateResource([
            'plan_schedule_id' => $model->id,
            'classifier_id' => $this->classifierId,
            'description' => $this->description,
            'year' => $model->year,
            'month' => $this->month,
            'count' => $this->count,
            'count_live' => $this->count,
            'kls' => $account->account,
            'count_used' => 0,
            'status' => StatusEnum::STATUS_ACTIVE,
            'enabled' => 1,
            'tovar' => $classfier->code,
            'tovarname' => $classfier->title_uzk,
            'expense' => $this->expense,
            'tovarprice' => $this->price * 100,
            'summa' => $this->price * $this->count,
            'srok' => $this->srok,
            'address' => $this->address,
            'company_bank_account_id' => $account->id,
        ]);

        $model->total_product_count += $product->count;

        if (!$product->save()) {
            $this->addError('classifiers', $product->errors);
            $transaction->rollBack();
            return false;
        }

        foreach ($classifier['unit'] as $unitId) {
            $pscp = new PlanScheduleClassifierUnit();
            $pscp->plan_schedule_classifier_id = $product->id;
            $pscp->classifier_property_id = $unitId;
            $pscp->status = StatusEnum::STATUS_ACTIVE;
            if(!$pscp->save()){
                $this->addError('unit', $pscp->errors);
                $transaction->rollBack();
                return false;
            }
        }

        foreach ($this->conditions as $condition) {
            $item = new PlanSchedulePropertiesResource([
                'plan_schedule_id' => $model->id,
                'classifier_id' => $this->classifierId,
                'plan_schedule_classifier_id' => $product->id,
                'prop_numb' => $condition['prop_numb'],
                'prop_name' => $condition['prop_name'],
                'val_numb' => $condition['val_numb'],
                'val_name' => $condition['val_name'],
            ]);
            if (!$item->save()) {
                $this->addError('conditions', $item->errors);
                $transaction->rollBack();
                return false;
            }
        }


        if (!$model->save()) {
            $this->addError('classifiers', $model->errors);
            $transaction->rollBack();
            return false;
        }

        $transaction->commit();
        return true;
    }
}
