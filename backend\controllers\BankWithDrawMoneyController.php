<?php

namespace backend\controllers;

use Yii;
use common\models\bank\BankWithDrawMoney;
use common\models\bank\BankWithDrawMoneySearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * BankWithDrawMoneyController implements the CRUD actions for BankWithDrawMoney model.
 */
class BankWithDrawMoneyController extends BackendController
{
    /**
     * Lists all BankWithDrawMoney models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new BankWithDrawMoneySearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }
    public function actionAccept($id)
    {
        $model =$this->findModel($id);
        $model->updateAttributes(['status'=>1]);
        return $this->render('view', [
            'model' => $model,
        ]);
    }

    /**
     * Displays a single BankWithDrawMoney model.
     * @param int $id ID
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }



    /**
     * Finds the BankWithDrawMoney model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id ID
     * @return BankWithDrawMoney the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = BankWithDrawMoney::findOne($id)) !== null) {
            return $model;
        }
        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
