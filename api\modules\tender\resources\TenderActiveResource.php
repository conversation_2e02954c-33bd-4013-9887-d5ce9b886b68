<?php


namespace api\modules\tender\resources;


use api\modules\backend\resources\TenderModeratorLogResource;
use api\modules\client\resources\TenderRequestResource;
use api\modules\client\resources\TenderRequirementsAnswerResource;
use api\modules\common\resources\DistrictResource;
use api\modules\common\resources\RegionResource;
use common\enums\TenderEnum;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\Tender;
use yii\web\NotFoundHttpException;

class TenderActiveResource extends Tender
{
    public function fields()
    {
        return [
            'id',
            'lot',
            'updated_at',
            'end_date',
            'title',
            'tenderTotalPrice' => function($model){
                return $model->getTenderTotalPriceBase();
            },
            'criteria_evaluation_proposals',
            'tenderRequestCount',
            'state',
            'created_at',
            'purchase_currency',
            'description',
            'amount_deposit',
            'accept_guarantee_bank',
            'method_payment',
            'advance_payment_percentage',
            'advance_payment_percentage',
            'advance_payment_period',
            'unblocking_type',
            'contact_position',
            'contact_phone',
            'address',
            'delivery_phone',
            'publish_days',
            'technical_part',
            'price_part'
        ];
    }


    public function getTenderRequestCount()
    {
        return TenderRequestResource::find()
            ->notDeleted()
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_id' => $this->id])
            ->count();
        //return $this->hasMany(TenderRequestResource::class, ['tender_id' => 'id']);
    }


    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

//    public function getTenderTotalPrice()
//    {
//        $price = 0;
//        foreach ($this->tenderClassifiers as $classifier) {
//            $price += $classifier->price * $classifier->number_purchased;
//        }
//        return $price;
//    }


}