<?php


namespace api\modules\shop\resources;


use common\enums\TenderEnum;
use common\models\TenderModeratorLog;

class ProductModeratorLogResource extends TenderModeratorLog
{
    public function fields()
    {
        return [
            'created_at', 'description', 'moderator_pinfl','product'
        ];
    }

    public function getProduct()
    {
        return $this->hasOne(ProductResource::class, ['id' => 'product_id']);
    }

}