<?php


namespace api\modules\common\resources;


use common\models\Company;

class CompanyResource extends Company
{
    public function fields()
    {
        return [
            'id',
            'title',
            'tin',
            'pinfl',
            'region',
            'district',
            'address',
            'phone',
            'director',
        ];
    }
    public function extraFields()
    {
        return [
            'companyBankAccount',
        ];
    }

    public function getDistrict()
    {
        return $this->hasOne(DistrictResource::class, ['id' => 'district_id']);
    }

    public function getRegion()
    {
        return $this->hasOne(RegionResource::class, ['id' => 'region_id']);
    }

}