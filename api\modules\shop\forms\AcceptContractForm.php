<?php


namespace api\modules\shop\forms;


use api\components\BaseModel;
use api\components\BaseRequest;
use api\modules\shop\resources\ContractRequestResource;
use common\enums\ContractEnum;
use Yii;
use yii\base\Exception;
use yii\web\NotFoundHttpException;

class AcceptContractForm extends BaseRequest
{

    public ?ContractRequestResource $model;
    public $id;
    public $pkcs7;
    public $state;
    
    public function rules()
    {
        return [
            [['state','id'], 'required'],
            ['state', 'integer']
        ];
    }


    /**
     * @throws Exception
     * @throws \yii\db\Exception
     * @throws NotFoundHttpException
     */
    public function getResult()
    {   
        $this->model = ContractRequestResource::findOrFail($this->id);
        $user = Yii::$app->user->identity;
        $company_id = $user->company_id;

        if ($this->model->company_id != $company_id) {
            $this->addError("error", t("Bu amaliyotni bajarish uchun yetkazib beruvchi bo'lishingiz kerak"));
            return false;
        }

        if ($this->model->status != ContractEnum::STATUS_REQUEST_NEW) {
            throw new Exception(t("Shartnoma so'rovi zaxira g'olib tasdiqlash xolatida emas"));
        }

        $transaction = \Yii::$app->db->beginTransaction();

        if ($this->state == 1) {
            $contract = $this->model->contract;
            $this->model->status = ContractEnum::STATUS_REQUEST_DONE;
            \console\models\Contract::createContractShop($transaction, $this->model->order_id, $contract->customer_id, $company_id, $contract->price, ContractEnum::STATUS_SIGNED, $contract->number, 0);
        } else {
            $this->model->status = ContractEnum::STATUS_REQUEST_CANCELED;
        }

        if ($this->model->save()) {
            $transaction->commit();
            return $this->model->id;
        } else {
            $transaction->rollBack();
            $this->addErrors($this->model->errors);
            return false;
        }
    }
}