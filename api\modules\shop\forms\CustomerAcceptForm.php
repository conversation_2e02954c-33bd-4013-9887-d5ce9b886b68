<?php


namespace api\modules\shop\forms;


use api\components\BaseModel;
use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\shop\resources\OrderRequestResource;
use api\modules\shop\resources\OrderResource;
use api\modules\shop\resources\ProductResource;
use common\enums\CompanyEnum;
use common\enums\ProductEnum;
use common\enums\ShopEnum;
use common\models\Company;
use common\models\CompanyBalance;
use common\models\TenderModeratorLog;
use Yii;
use yii\base\Exception;
use function foo\func;

class CustomerAcceptForm extends BaseModel
{

    public ?OrderRequestResource $model;
    public $id;


    public function rules()
    {
        return [['id'],'required'];
    }

    /**
     * @throws \yii\db\Exception
     * @throws Exception
     */
    public function getResult()
    {
        $this->model = OrderRequestResource::findOne(['id' => $this->id]);
        if (!$this->model) {
            throw new Exception(t("Requested order not found"));
        }
        $transaction = \Yii::$app->db->beginTransaction();

        if ($this->model->save()) {
            $order = OrderResource::findOne($this->model->order_id);

            if (!$order) {
                throw new Exception(t("Order not found"));
            }
            $order->updateAttributes([
                'status' => ShopEnum::ORDER_STATUS_DONE
            ]);
            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}