<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderCommissionVoteResource;
use api\modules\tender\resources\TenderRequestRatingCommissionResource;
use api\modules\tender\resources\TenderResourceForCommission;
use common\enums\TenderEnum;

class TenderCommissionRateStatFilter extends BaseRequest
{
    public TenderResourceForCommission $model;

    public function __construct(TenderResourceForCommission $model, $params = [])
    {
        $this->model = $model;
        parent::__construct($params);
    }

    public function getResult()
    {
        $c = $this->model->getCommissionMember(\Yii::$app->user->identity->commissionMemberId);

        if($c)
        {
            return TenderRequestRatingCommissionResource::find()->notDeleted()
                ->andWhere(['tender_id' => $this->model->id])
                ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
                ->orderBy(['created_at' => SORT_DESC])
                ->all();
        }

        return true;


    }
}