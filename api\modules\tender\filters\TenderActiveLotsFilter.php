<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderActiveLotsResource;
use common\enums\TenderEnum;

class TenderActiveLotsFilter extends BaseRequest
{
    public $lot;

    public function rules()
    {
        return [
            [['lot'], 'safe'],
        ];
    }

    public $state;

    public function __construct($state, $params = [])
    {
        $this->state = $state;
        parent::__construct($params);
    }

    public function getResult()
    {
        $model = TenderActiveLotsResource::find()->where(['state' => TenderEnum::STATE_READY, 'type' => $this->state]);
        if ($this->lot) {
            $model->andWhere(['like', 'lot', $this->lot]);
        }
        $model->orderBy(['end_date' => SORT_DESC]);

        return paginate($model);
    }

}