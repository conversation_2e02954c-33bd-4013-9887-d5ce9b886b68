<?php

use common\models\auction\Auction;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\Url;

/**
 * @var yii\web\View $this
 * @var common\models\auction\AuctionSearch $searchModel
 * @var yii\data\ActiveDataProvider $dataProvider
 */

$this->title = t("Auctions");
$this->params['breadcrumbs'][] = $this->title;
?>
<div class="auction-index">
    <div class="card">


        <div class="card-body p-0">
            <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

            <?php echo GridView::widget([
                'layout' => "{items}\n{pager}",
                'options' => [
                    'class' => ['gridview', 'table-responsive'],
                ],
                'tableOptions' => [
                    'class' => ['table', 'text-nowrap', 'table-striped', 'table-bordered', 'mb-0'],
                ],
                'dataProvider' => $dataProvider,
                'filterModel' => $searchModel,
                'rowOptions' => function (
                    $model,
                    $key,
                    $index,
                    $grid
                ) {
                    return [
                        'id' => $key,
                        'ondblclick' => 'location.href="'
                            . Url::to(['view'])
                            . '?id="+(this.id);',
                    ];
                },
                'columns' => [
//                    ['class' => 'yii\grid\SerialColumn'],

                    [
                        'label'=>t("Lot"),
                        'format' => 'raw',
                        'value'=>function ($data) {
                            return Html::a($data->lot,['auction/view','id'=>$data->id]);
                        },
                    ],
                    [
                        'label'=>t("Buyurtmachi"),
                        'format' => 'raw',
                        'value'=>function ($data) {
                            return Html::a($data->company->title . " " . $data->company->tin,['auction/view','id'=>$data->id]);
                        },
                    ],


                    [
                        'attribute' => t("Status"),
                        'filter' => Auction::getStatuses(),
                        'value' => function($m){
                            return $m->statusName;
                        },

                    ],
                    [
                        'label' => t("Boshlang'ich narx"),
                        'attribute' => 'total_sum',
                        'value' => function($m){
                            return showPrice($m->total_sum / 100);
                        },
                    ],
                     'cancel_reason',
                    // 'auction_end',
                     'cancel_date',
                    // 'payment_status',
                    // 'payment_date',
                    // 'delivery_period',
                    // 'payment_period',
                    // 'receiver_email:email',
                    // 'receiver_phone',
                    // 'region_id',
                    // 'zip_code',
                    // 'address',
                     'created_at',
                    // 'updated_at',
                    // 'deleted_at',
                    // 'created_by',
                    // 'updated_by',
                    // 'account',
                    // 'plan_schedule_id',

//                    ['class' => \common\widgets\ActionColumn::class],
                ],
            ]); ?>

        </div>
        <div class="card-footer">
            <?php echo getDataProviderSummary($dataProvider) ?>
        </div>
    </div>

</div>
