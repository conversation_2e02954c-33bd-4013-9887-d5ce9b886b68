<?php

namespace api\modules\auction\forms;

use api\components\BaseModel;
use api\components\BaseRequest;
use api\modules\auction\resources\AuctionClassifierResource;
use api\modules\auction\resources\AuctionConditionResource;
use api\modules\auction\resources\AuctionDetailResource;
use api\modules\auction\resources\AuctionResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\PlanScheduleClassifierCreateResource;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\AuctionEnum;
use common\enums\CompanyEnum;
use common\enums\CompanyTransactionEnum;
use common\enums\StatusEnum;
use common\models\auction\AuctionClassifier;
use common\models\auction\AuctionCondition;
use common\models\auction\AuctionFile;
use common\models\auction\AuctionHistory;
use common\models\Bmh;
use common\models\ClassifierCategory;
use common\models\Company;
use common\models\CompanyTransaction;
use common\models\User;
use common\models\WorkdayCalendar;
use Yii;
use yii\helpers\ArrayHelper;

class AuctionReloadWithUpdatingForm extends BaseModel
{
    public AuctionDetailResource $model;

    public $delivery_period;
    public $address;
    public $account;
    public $pkcs7;

    public $auction_files = [];
    public $auction_conditions = [];


    public function __construct(AuctionDetailResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        $parent = parent::rules();
        $child = [
            [['auction_conditions', 'delivery_period', 'address', 'account'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['auction_files', 'auction_conditions'], 'safe'],
            [['address'], 'string', 'max' => 255],
            [['account'], 'string', 'min' => 20, 'max' => 20],
            [['delivery_period',], 'integer', 'min' => 7]
        ];
        return array_merge($parent, $child);
    }


    public function getResult()
    {
        /**
         * @var $company Company
         */
        if ($this->model->status != AuctionEnum::STATUS_NOT_HELD) {
            $this->addError("error", t("Auksion qayta e'lon qilish uchun muqobil xolatda emas"));
            return false;
        }

        $model = $this->model;
        $holidays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::HOLIDAY]), 'local_date', 'local_date');
        $workDays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::WORKDAY]), 'local_date', 'local_date');

        $endDate = $model->auction_end;
        $endDate = addDaysExcludingWeekends($endDate, 3, $workDays, $holidays);

        $endTime = strtotime($endDate);
        $time = time();
        if ($time > $endTime) {
            $this->addError("id", t("3 ish kunigacha qayta e'lon qilish mumkin. Vaqt o'tib ketdi"));
            return false;
        }

        if($model->age > 1){
            $this->addError("error", t("Auksion 1 marta qayta o'tkaziladi"));
            return false;
        }

        $transaction = \Yii::$app->db->beginTransaction();
        $user = Yii::$app->user->identity;
        $company = $user->company;

        $model->status = AuctionEnum::STATUS_MODERATING;
        $model->delivery_period = $this->delivery_period;
        $model->address = $this->address;
        $model->account = $this->account;
        $model->auction_end = null;

        if (!$model->save()) {
            throw new \yii\web\HttpException(400, t("Auksionni saqlashda xatolik"));
        }


        AuctionFile::updateAll(['deleted_at' => date("Y-m-d H:i:s"), 'updated_by' => $user->id], ['auction_id' => $this->model->id]);
        foreach ($this->auction_files as $fileId) {

            $auctionFile = new AuctionFile();
            $auctionFile->auction_id = $model->id;
            $auctionFile->file_id = $fileId;
            if (!$auctionFile->save()) {
                $this->addErrors($auctionFile->errors);
                $transaction->rollBack();
                return false;
            }

        }

        AuctionCondition::updateAll(['status' => StatusEnum::STATUS_DELETED], ['auction_id' => $this->model->id, 'status' => StatusEnum::STATUS_ACTIVE]);
        if ($this->auction_conditions && count($this->auction_conditions) > 0) {
            foreach ($this->auction_conditions as $condition) {
                if (isset($condition['id']) && $condition['id'] > 0) {
                    $auction_condition = AuctionConditionResource::findOne(['id' => $condition['id'], 'auction_id' => $model->id]);
                    if (!$auction_condition) {
                        $this->addError('auction_conditions', t("Shart topilmadi"));
                        $transaction->rollBack();
                        return false;
                    }
                } else {
                    $auction_condition = new AuctionConditionResource();
                    $auction_condition->auction_id = $model->id;
                }
                $auction_condition->status = StatusEnum::STATUS_ACTIVE;
                $auction_condition->condition = $condition['condition'];
                $auction_condition->text = $condition['text'];
                if (!$auction_condition->save()) {
                    $this->addErrors($auction_condition->errors);
                    $transaction->rollBack();
                    return false;
                }
            }
        }

        if (!$model->save()) {
            $this->addErrors($model->errors);
            $transaction->rollBack();
            return false;
        }


        $zalog_sum = 0;
        if (!$user->isBudget) {
            $zalog_sum = $model->total_sum * env('ZALOG_PERCENT', 0.03);

        }
        $commission_sum = $model->total_sum * env('COMMISSION_PERCENT', 0.0015);
        $commission_sum = $commission_sum > 1000000 ? 1000000 : $commission_sum;
        $total_block_sum = $zalog_sum + $commission_sum;

        $company->lastCompanyBalance->calculateBalance();

        if ($company->availableBalance < $total_block_sum) {
            $transaction->rollBack();
            $this->addError('error', t("Balansda yetarli mablag' mavjud emas"));
            return false;
        }

        $history = new AuctionHistory();
        $history->auction_id = $this->model->id;
        $history->user_id = $user->id;
        $history->status = $this->model->status;
        $history->comment = t("Amalga oshmagan lotni tahrirlandi. Moderatsiyaga yuborildi.");
        $history->created_at = date("Y-m-d H:i:s");
        if (!$history->save()) {
            $this->addErrors($history->errors);
            $transaction->rollBack();
            return false;
        }

        $transaction->commit();
        return true;
    }
}
