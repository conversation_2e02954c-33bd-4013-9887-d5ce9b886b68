<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\shop\resources\ProductDraftResource;
use api\modules\common\resources\ClassifierResource;
use common\enums\ProductEnum;
use common\models\Classifier;
use common\models\ClassifierProperties;
use common\models\File;
use common\models\ProductClassifierUnit;
use common\models\Region;
use common\models\shop\ProductFile;
use common\models\shop\ProductRegion;
use yii\db\Exception;

class ProductDraftForm extends BaseRequest
{
    public ProductDraftResource $model;

    public $classifier_id;
    public $type;
    public $brand_title;
    public $made_in;
    public $quantity;
    public $min_order;
    public $max_order;
    public $unit_price;
    public $price;
    public $title;
    public $description;
    public $year;
    public $delivery_period;
    public $warranty_period;
    public $warranty_period_type;
    public $expiry_period;
    public $expiry_period_type;
    public $country_id;
    public $platform_display;
    public $type_of_placement;
    public $account_number;
    public $main_image;


    public $product_files = [];
    public $product_images = [];
    public $regions = [];
    public $classifier_units = [];

    public function __construct(ProductDraftResource $model , $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        $parent = parent::rules();
        $child = [
            [['type', 'classifier_id', 'brand_title',
                'made_in', 'quantity', 'min_order', 'max_order',
                'unit_price', 'price', 'title', 'description',
                'delivery_period', 'warranty_period',
                'warranty_period_type', 'expiry_period', 'expiry_period_type', 'country_id', 'platform_display', 'type_of_placement',
                'account_number', 'year'
            ], 'required', 'message' => t('{attribute} yuborish majburiy')],
            ['type', 'checkType'],
            [['product_files', 'product_images', 'regions', 'main_image', 'classifier_units'], 'safe'],
            [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => ClassifierResource::class, 'targetAttribute' => ['classifier_id' => 'id']]
        ];
        return array_merge($parent, $child);
    }

    public function checkType($attribute, $params)
    {
        if ($this->type == ProductEnum::PRODUCT_TYPE_PRODUCT || $this->type == ProductEnum::PRODUCT_TYPE_SERVICE || $this->type == ProductEnum::PRODUCT_TYPE_WORK) {
            return true;
        } else {
            $this->addError('type', t("Tovar/xizmat noto'g'ri yuborildi"));
            return false;
        }
    }

    /**
     * @throws Exception
     */
    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();

        $this->price = $this->price * 100;
        $this->unit_price = $this->unit_price * 100;
        if (count($this->regions) == 0) {
            throw new Exception(t("Hudud tanlash majburiy"));
        }
        if (count($this->product_images) == 0) {
            throw new Exception(t("File yoki image tanlanmagan"));
        }
        if ($this->platform_display == \common\enums\ProductEnum::PLATFORM_DISPLAY_NATIONAL) {
            if (count($this->product_files) == 0) {
                throw new Exception(t("File yuborish kerak"));
            }
        }


        if (empty($this->main_image)) {
            $this->main_image = $this->product_images[0];
        }

        $classifier = Classifier::findOne($this->classifier_id);
        $checkClassifierProperties = ClassifierProperties::find()->where(['classifier_id' => $classifier->id, 'required' => 1])->count();
        if (count($this->classifier_units) >= $checkClassifierProperties) {
            throw new Exception(t("Klasifikator birligi tanlanmagan"));
        }
        $company = \Yii::$app->user->identity->company;
        $this->model->company_id = $company->id;
        $this->model->state = ProductEnum::SHOP_STATE_NEW;
        $this->model->status = ProductEnum::STATUS_NEW;
        $this->model->classifier_category_id = $classifier->classifier_category_id;
        $att = $this->attributes;
        $this->model->setAttributes($att, false);
        $this->model->delivery_period_type = ProductEnum::PERIOD_TYPE_DAY;

        if ($this->model->attributes && $this->model->validate() && $this->model->save()) {
            $productId = $this->model->id;
            foreach ($this->regions as $key => $region) {
                if ($this->platform_display == \common\enums\ProductEnum::PLATFORM_DISPLAY_NATIONAL) {
                    $r = Region::findOne($region);
                    if (!$r) {
                        $transaction->rollBack();
                        $this->addError('error', "Viloyat/Tuman topilmadi");
                        return false;
                    }
                    if ($company->region_id != $r->parent_id) {
                        $transaction->rollBack();
                        $this->addError('error', "Milliy do'konda yetkazib beruvchi yuridik manzili bir xil bo'lishi kerak");
                        return false;
                    }
                }

                $productRegion = new ProductRegion();

                $productRegion->region_id = $region;
                $productRegion->product_id = $productId;

                if (!($productRegion->validate() && $productRegion->save())) {
                    $transaction->rollBack();
                    $this->addError('regions', $productRegion->errors);
                    return false;
                }
            }

            foreach ($this->product_files as $key => $file) {
                $productFile = new ProductFile();
                $productFile->file_id = $file;
                $productFile->product_id = $productId;
                $productFile->type = ProductFile::TYPE_FILE;

                if (!($productFile->validate() && $productFile->save())) {
                    $transaction->rollBack();
                    $this->addError('product_files', $productFile->errors);
                    return false;
                }
            }

            foreach ($this->product_images as $key => $image) {
                $productImage = new ProductFile();

                $productImage->file_id = $image;
                $productImage->product_id = $productId;
                $productImage->type = ProductFile::TYPE_IMAGE;
                if (!($productImage->validate() && $productImage->save())) {
                    $transaction->rollBack();
                    $this->addError('product_images', $productImage->errors);
                    return false;
                }
            }

            if ($this->main_image) {
                $file = File::findOne($this->main_image);
                $file->updateAttributes(['is_main' => true]);
            }

            foreach ($this->classifier_units as $id) {
                $productClassifier = new ProductClassifierUnit();
                $productClassifier->classifier_id = $classifier->id;
                $productClassifier->product_id = $productId;
                $productClassifier->classifier_properties_id = $id;
                if (!($productClassifier->validate() && $productClassifier->save())) {
                    $transaction->rollBack();
                    $this->addError('classifier_units', $productClassifier->errors);
                }
            }

            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}