<?php


namespace api\modules\tender\resources;


use api\modules\common\resources\FileResource;
use common\enums\TenderEnum;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderRequirementsAnswer;

class TenderRequirementsAnswerResource extends TenderRequirementsAnswer
{

    public function fields()
    {
        return [
            'id',
            'tender_request_id',
            'tender_requirements_id',
            'company_id',
            'tender_id',
            'file',
            'title',
            'value'
        ];
    }

    public function extraFields()
    {
        return ['tenderRequirements', 'tenderRequestRating', 'tenderRequest'];
    }

    public function getTenderRequirements()
    {
        return $this->hasOne(TenderRequirementsResource::class, ['id' => 'tender_requirements_id'])->andWhere([TenderRequirementsResource::tableName() . '.status' => TenderEnum::STATUS_ACTIVE]);
    }

    public function getTenderRequest()
    {
        return $this->hasOne(TenderRequestResource::class, ['id' => 'tender_request_id']);
    }

    public function getTenderRequestRating()
    {
        return TenderRequestRatingResource::find()->where([
            'tender_request_id' => $this->tender_request_id,
            'tender_requirement_answer_id' => $this->id,
            'tender_id' => $this->tender_id,
        ])->one();
    }

    /**
     * Gets query for [[File]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'file_id']);
    }

    /**
     * Gets query for [[Tender]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTender()
    {
        return $this->hasOne(TenderResource::class, ['id' => 'tender_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}