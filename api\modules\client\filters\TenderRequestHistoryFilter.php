<?php


namespace api\modules\client\filters;


use api\modules\client\resources\TenderRequestHistoryResource;
use api\modules\client\resources\TenderResource;
use common\enums\TenderEnum;

class TenderRequestHistoryFilter extends \api\components\BaseRequest
{
    public TenderResource $model;

    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;
        parent::__construct($params);
    }

    public function getResult()
    {
        return TenderRequestHistoryResource::find()
            ->where([
                'company_id' => \Yii::$app->user->identity->company_id,
                'tender_id' => $this->model->id
            ])
            ->all();

    }
}