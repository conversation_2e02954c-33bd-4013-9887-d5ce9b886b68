<?php


namespace backend\forms;


use yii\base\Model;
use Yii;

class ExcelImportForm extends Model
{
    public $id;
    public $filial_code;
    public $count;
    public $fileImport;

    public function rules()
    {
        return [
            [['fileImport'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['fileImport'], 'file', 'skipOnEmpty' => false, 'extensions' => 'ods,xls,xlsx', 'maxSize'=>1024 * 1024, 'message'=>'Not more than 10MB'
            ],
        ];
    }

}