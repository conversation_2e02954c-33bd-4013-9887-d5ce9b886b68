<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderResourceForCommission;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;

class TenderForCommissionFilter extends BaseRequest
{
    public $state;
    public $lot;


    public function rules()
    {
        return [
            ['lot', 'safe']
        ];
    }


    public function __construct($state, $params = [])
    {
        $this->state = $state;
        parent::__construct($params);
    }

    public function getResult()
    {
        $commissionId = \Yii::$app->user->identity->commissionMemberId;

        $model = TenderResourceForCommission::find()
            ->join('inner join', 'tender_commission_member', 'tender.id = tender_commission_member.tender_id')
            ->where([
                'state' => $this->state,
                'tender_commission_member.commission_member_id' => $commissionId,
                'tender_commission_member.status' => TenderEnum::STATUS_ACTIVE
            ]);
        if ($this->lot) {
            $model->andWhere(['like', 'lot', $this->lot]);
        }
        $model->orderBy(['updated_at' => SORT_ASC]);
        return paginate($model);


    }
}