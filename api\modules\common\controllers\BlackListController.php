<?php

namespace api\modules\common\controllers;

use api\components\ApiController;
use api\modules\common\filters\BlackListFilter;
use api\modules\common\filters\StatisticsFilter;
use api\modules\common\filters\UnitFilter;
use Yii;

class BlackListController extends ApiController
{
    public function actionBlackList()
    {
        return $this->sendResponse(
            new BlackListFilter(),
            Yii::$app->request->queryParams,
        );
    }

    public function actionStatistics(){
        return $this->sendResponse(
            new StatisticsFilter(),
            Yii::$app->request->queryParams,
        );
    }
}
