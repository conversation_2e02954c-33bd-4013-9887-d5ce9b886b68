<?php

namespace api\modules\common\filters;

use api\components\BaseRequest;
use common\enums\OperationTypeEnum;
use common\models\Company;
use common\models\CompanyVirtualAccount;

class TestShowAccountFilter extends BaseRequest
{
    public $tin;
    public function rules()
    {
        return [
            ['tin', 'required'],
        ];
    }

    public function getResult()
    {
        $company = Company::findOne(['tin' => $this->tin]);
        if (!$company) {
            $this->addError('tin','Company not found');
        }
        $account = CompanyVirtualAccount::find()->where(['company_id' => $company->id,'prefix_account' => OperationTypeEnum::P_K_30101])->one();
        if (!$account)
        {
            $this->addError("tin","Company virtual account not found");
            return false;
        }
        return $account ? $account->price / 100 : null;
    }
}