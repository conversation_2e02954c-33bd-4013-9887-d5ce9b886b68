<?php


namespace api\modules\client\forms;


use api\components\BaseRequest;
use api\modules\client\resources\TenderDiscussionResource;
use api\modules\client\resources\TenderResource;
use common\enums\TenderEnum;
use yii\base\Exception;

class TenderProtestForm extends BaseRequest
{

    public TenderResource $tender;
    public $description;

    public function __construct(TenderResource $model, $params = [])
    {
        $this->tender = $model;
        parent::__construct($params);
    }

    public function rules()
    {
        return [
            ['description', 'required', 'message' => t('{attribute} yuborish majburiy')],
            ['description', 'string', 'max' => 255],
        ];
    }

    public function getResult()
    {
        date_default_timezone_set("Asia/Tashkent");

        $userId = \Yii::$app->user->identity->company_id;
        $model = TenderDiscussionResource::find()->where(['tender_id' => $this->tender->id, 'status' => TenderEnum::STATUS_ACTIVE, 'company_id' => $userId])->one();
        if (!$model) {
            $model = new TenderDiscussionResource();
            $model->tender_id = $this->tender->id;
            $model->company_id = $userId;
            $model->status = TenderEnum::STATUS_ACTIVE;
        }

        $transaction = \Yii::$app->db->beginTransaction();
        $model->description = $this->description;
        if (!$model->save()) {
            $this->addErrors($model->errors);
            return false;
        }

        $transaction->commit();
        return true;
    }

}