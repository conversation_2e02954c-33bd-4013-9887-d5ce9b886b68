<?php

namespace api\modules\common\filters;

use api\modules\common\resources\VirtualTransactionResource;
use common\widgets\ExcelExportWidget;
use Yii;

class CompanyTransactionExcelExportFilter extends CompanyTransactionFilter
{
    /**
     * @throws \Throwable
     */
    public function generateFile(): string
    {
        return ExcelExportWidget::widget([
            'models' => $this->_query()->all(),
            'labels' => [
                'id' => '№',
                'created_at' => 'Дата',
                'contract_id' => 'Номер договора',
                'procedure_type' => 'Тип процедуры',
                'payer_info' => 'Плательщик/ИНН',
                'recipient_info' => 'Получатель/ИНН',
                'debit' => 'Дебет',
                'credit' => 'Кредит',
                'description' => 'Детали платежа',
            ],
            'fields' => [
                'id',
                'created_at' => function (VirtualTransactionResource $model) {
                    if ($model->created_at)
                    {
                        return date('d/m/Y', strtotime($model->created_at)) . "\n" .  date('H:i', strtotime($model->created_at));
                    }
                    return null;
                },
                'contract_id',
                'procedure_type' => function (VirtualTransactionResource $model) {
                    return $model->getProcedureLabel();
                },
                'payer_info' => function (VirtualTransactionResource $model) {
                    return $model->debitCompany->title . "\n" . $model->debitCompany->tin;
                },
                'recipient_info' => function (VirtualTransactionResource $model) {
                    return $model->creditCompany->title . "\n" . $model->creditCompany->tin;
                },
                'debit',
                'credit',
                'description' => function (VirtualTransactionResource $model) {
                   return $model->getOperationTypeLabel();
                }
            ]
        ]);
    }
}