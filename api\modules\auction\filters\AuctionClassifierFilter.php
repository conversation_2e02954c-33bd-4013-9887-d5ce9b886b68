<?php

namespace api\modules\auction\filters;

use api\components\BaseRequest;
use api\modules\auction\resources\AuctionClassifierResource;

class AuctionClassifierFilter extends BaseRequest
{
  public $id;

  public function rules()
  {
    return [
      ['id', 'required', 'message' => t('{attribute} yuborish majburiy')],
      ['id', 'safe'],
    ];
  }

  public function getResult()
  {
    $query = AuctionClassifierResource::findOne($this->id);
    return $query;
  }
}
