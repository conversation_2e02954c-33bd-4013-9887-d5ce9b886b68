<?php


namespace api\modules\tender\resources;


use api\modules\common\resources\FileResource;
use common\models\Contract;

class ContractResource extends Contract
{
    public function fields()
    {
        return ['id', 'file', 'price']; // TODO: Change the autogenerated stub
    }

    public function getTender()
    {
        return $this->hasOne(TenderResourceForClient::class, ['id' => 'tender_id']);
    }

    public function getFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'file_id']);
    }

}