-- Test ma'lumotlari sizning query uchun
-- Bu script sizning getResult() metodidagi query ga mos keladigan ma'lumotlarni yaratadi

-- 1. Region ma'lumotlari (parent-child relationship)
INSERT INTO region (id, parent_id, type, title_ru, title_uz, created_at) VALUES
(1, NULL, 1, 'Toshkent viloyati', 'Toshkent viloyati', NOW()),
(2, 1, 2, 'Toshkent shahri', 'Toshkent shahri', NOW()),
(3, 1, 2, '<PERSON><PERSON> shahri', '<PERSON><PERSON> shahri', NOW()),
(4, NULL, 1, 'Samarqand viloyati', 'Samarqa<PERSON> viloyati', NOW()),
(5, 4, 2, 'Samarqand shahri', 'Samarqand shahri', NOW());

-- 2. Classifier ma'lumotlari (code muhim - 8 ta belgidan iborat bo'lishi kerak)
INSERT INTO classifier (id, title_ru, title_uz, code, classifier_category_id, status, type, created_at) VALUES
(1, 'Kompyuter texnikasi', 'Kompyuter texnikasi', '12345678901', 1, 1, 1, NOW()),
(2, 'Ofis mebellari', 'Ofis mebellari', '12345678902', 1, 1, 1, NOW()),
(3, 'Avtomobil ehtiyot qismlari', 'Avtomobil ehtiyot qismlari', '87654321001', 1, 1, 1, NOW()),
(4, 'Qurilish materiallari', 'Qurilish materiallari', '87654321002', 1, 1, 1, NOW()),
(5, 'Tibbiy asboblar', 'Tibbiy asboblar', '11111111001', 1, 1, 1, NOW());

-- 3. Company ma'lumotlari
INSERT INTO company (id, title, region_id, status, created_at) VALUES
(1, 'Test Company 1', 2, 300, NOW()),  -- Toshkent shahri
(2, 'Test Company 2', 3, 300, NOW()),  -- Angren shahri  
(3, 'Test Company 3', 5, 300, NOW()),  -- Samarqand shahri
(4, 'Customer Company', 2, 300, NOW()); -- Buyurtmachi kompaniya

-- 4. User ma'lumotlari
INSERT INTO user (id, company_id, username, email, status, created_at) VALUES
(1, 1, 'supplier1', '<EMAIL>', 10, NOW()),
(2, 2, 'supplier2', '<EMAIL>', 10, NOW()),
(3, 3, 'supplier3', '<EMAIL>', 10, NOW()),
(4, 4, 'customer1', '<EMAIL>', 10, NOW());

-- 5. Product ma'lumotlari (state=300 ACTIVE, platform_display muhim)
INSERT INTO product (id, classifier_id, company_id, title, state, platform_display, active_date, created_at, expiry_period) VALUES
-- E-SHOP products (company 1)
(1, 1, 1, 'Laptop Dell', 300, 'e-shop', '2024-01-01 10:00:00', '2024-01-01 10:00:00', 30),
(2, 2, 1, 'Ofis stoli', 300, 'e-shop', '2024-01-02 10:00:00', '2024-01-02 10:00:00', 30),

-- NATIONAL-SHOP products (company 2)
(3, 3, 2, 'Avtomobil shinasi', 300, 'national-shop', '2024-01-03 10:00:00', '2024-01-03 10:00:00', 30),
(4, 4, 2, 'Sement', 300, 'national-shop', '2024-01-04 10:00:00', '2024-01-04 10:00:00', 30),

-- Company 3 products (boshqa region)
(5, 5, 3, 'Tibbiy asbob', 300, 'national-shop', '2024-01-05 10:00:00', '2024-01-05 10:00:00', 30);

-- 6. Product-Region bog'lanishi (national-shop uchun muhim)
INSERT INTO product_region (product_id, region_id, created_at) VALUES
(3, 2, NOW()), -- Avtomobil shinasi - Toshkent shahri
(3, 3, NOW()), -- Avtomobil shinasi - Angren shahri
(4, 2, NOW()), -- Sement - Toshkent shahri
(5, 5, NOW()); -- Tibbiy asbob - Samarqand shahri

-- 7. Order ma'lumotlari (status=1 ACTIVE, shop_end=NULL, user_id != current user)
INSERT INTO `order` (id, product_id, classifier_id, user_id, company_id, status, shop_end, created_at, total_sum) VALUES
-- E-SHOP orders (classifier code birinchi 8 belgisi: 12345678)
(1, 1, 1, 4, 4, 1, NULL, '2024-01-01 11:00:00', 100000), -- Laptop buyurtmasi
(2, 2, 2, 4, 4, 1, NULL, '2024-01-02 11:00:00', 50000),  -- Ofis stoli buyurtmasi

-- NATIONAL-SHOP orders (classifier code birinchi 8 belgisi: 87654321)
(3, 3, 3, 4, 4, 1, NULL, '2024-01-03 11:00:00', 75000),  -- Avtomobil shinasi buyurtmasi
(4, 4, 4, 4, 4, 1, NULL, '2024-01-04 11:00:00', 120000), -- Sement buyurtmasi

-- Bu order ko'rinmasligi kerak (boshqa region)
(5, 5, 5, 4, 4, 1, NULL, '2024-01-05 11:00:00', 200000); -- Tibbiy asbob (Samarqand)

-- 8. Test uchun qo'shimcha ma'lumotlar

-- Inactive order (ko'rinmasligi kerak)
INSERT INTO `order` (id, product_id, classifier_id, user_id, company_id, status, shop_end, created_at, total_sum) VALUES
(6, 1, 1, 4, 4, 0, NULL, '2024-01-06 11:00:00', 100000); -- Inactive order

-- Shop_end bor order (ko'rinmasligi kerak)
INSERT INTO `order` (id, product_id, classifier_id, user_id, company_id, status, shop_end, created_at, total_sum) VALUES
(7, 2, 2, 4, 4, 1, '2024-01-07 12:00:00', '2024-01-07 11:00:00', 50000); -- Tugagan order

-- O'z kompaniyasining orderi (ko'rinmasligi kerak - user_id = current user bo'lganda)
INSERT INTO `order` (id, product_id, classifier_id, user_id, company_id, status, shop_end, created_at, total_sum) VALUES
(8, 1, 1, 1, 1, 1, NULL, '2024-01-08 11:00:00', 100000); -- O'z orderi

-- Active_date dan oldingi order (ko'rinmasligi kerak)
INSERT INTO `order` (id, product_id, classifier_id, user_id, company_id, status, shop_end, created_at, total_sum) VALUES
(9, 1, 1, 4, 4, 1, NULL, '2023-12-31 11:00:00', 100000); -- Active_date dan oldin

-- QUERY NATIJASI:
-- Agar siz company_id=1 (Test Company 1) va region_id=2 (Toshkent shahri) bilan login qilsangiz:
-- 
-- Ko'rinadigan orderlar:
-- 1. Order ID 1 - Laptop (e-shop, classifier: 12345678)
-- 2. Order ID 2 - Ofis stoli (e-shop, classifier: 12345678) 
-- 3. Order ID 3 - Avtomobil shinasi (national-shop, classifier: 87654321, region mos)
-- 4. Order ID 4 - Sement (national-shop, classifier: 87654321, region mos)
--
-- Ko'rinmaydigan orderlar:
-- - Order ID 5 - Tibbiy asbob (boshqa region)
-- - Order ID 6 - Inactive order
-- - Order ID 7 - Shop_end bor
-- - Order ID 8 - O'z orderi
-- - Order ID 9 - Active_date dan oldin

-- Test qilish uchun:
-- 1. User ID 1 bilan login qiling (company_id=1, region_id=2)
-- 2. DeliveryProductForm->getResult() ni chaqiring
-- 3. 4 ta order qaytishi kerak (ID: 1,2,3,4)
