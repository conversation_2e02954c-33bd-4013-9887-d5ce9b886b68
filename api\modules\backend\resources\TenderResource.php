<?php

namespace api\modules\backend\resources;

use common\models\query\NotDeletedFromCompanyQuery;
use common\models\Tender;

class TenderResource extends Tender
{
    public function fields(){
        return [
            'id',
            'type',
            'created_at',
            'updated_at',
            'tenderTotalPrice' => function($model){
                return $model->getTenderTotalPriceBase();
            },
            'title',
            'purchase_currency',
            'description',
            'amount_deposit',
            'accept_guarantee_bank',
            'method_payment',
            'advance_payment_percentage',
            'advance_payment_percentage',
            'advance_payment_period',
            'unblocking_type',
            'contact_position',
            'contact_phone',
            'address',
            'delivery_phone',
        ];
    }

    public function extraFields (){
        return [
            'contactFile',
            'technicalDocumentFile',
            'technicalTaskFile',
            'planSchedule',
            'region',
            'district',
            'tenderTotalPrice' => function($model){
                return $model->getTenderTotalPriceBase();
            },
            'tenderClassifiers',
            'tenderRequirements',
            'tenderCommissionMembers',
        ];
    }



    public function getContactFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'contact_file_id']);
    }


    public function getDistrict()
    {
        return $this->hasOne(DistrictResource::class, ['id' => 'district_id']);
    }


    public function getPlanSchedule()
    {
        return $this->hasOne(FileResource::class, ['id' => 'plan_schedule_id']);
    }


    public function getRegion()
    {
        return $this->hasOne(RegionResource::class, ['id' => 'region_id']);
    }


    public function getTechnicalDocumentFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'technical_document_file_id']);
    }

    public function getTechnicalTaskFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'technical_task_file_id']);
    }

    /**
     * Gets query for [[TenderClassifiers]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTenderClassifiers()
    {
        return $this->hasMany(TenderClassifierResource::class, ['tender_id' => 'id']);
    }

    public function getTenderRequirements()
    {
        return $this->hasMany(TenderRequirementsResource::class, ['tender_id' => 'id']);
    }

    public function getTenderCommissionMembers()
    {
        return $this->hasMany(TenderCommissionMemberResource::class, ['tender_id' => 'id']);
    }


    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

//    public function getTenderTotalPrice(){
//        $price = 0;
//        foreach ($this->tenderClassifiers as $classifier){
//            $price+=$classifier->price;
//        }
//        return $price;
//    }

}