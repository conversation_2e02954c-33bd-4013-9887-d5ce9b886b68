<?php

namespace api\modules\tender\filters;

use api\components\BaseRequest;
use api\modules\tender\resources\CommissionMemberResource;

class CommissionMemberFilter extends BaseRequest {

    public $tin;
    public $pinfl;

    public function rules (){
        return [
            [['tin', 'pinfl'], 'integer']
        ];
    }

    public function getResult()
    {
        $query = CommissionMemberResource::find()->notDeletedAndFromCompany();

        if ($this->tin) {
            $query->andWhere(['like', 'tin', $this->tin]);
        }
        if ($this->pinfl) {
            $query->andWhere(['like', 'pinfl', $this->pinfl]);
        }

        return $query->orderBy(['fullname' => SORT_ASC])->all();
    }

}