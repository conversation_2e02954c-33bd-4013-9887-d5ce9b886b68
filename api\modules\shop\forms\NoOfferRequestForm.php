<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\shop\resources\OrderRequestResource;
use common\enums\ShopEnum;
use common\models\shop\Order;
use common\models\shop\OrderRequest;
use yii\base\Exception;

class NoOfferRequestForm extends BaseRequest
{

    public OrderRequestResource $model;

    public $order_id;
    public $price;
    public $pkcs7;

    public function __construct($params = [])
    {
        $this->model = new OrderRequestResource();

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [['order_id', 'price'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['order_id'], 'exist', 'skipOnError' => true, 'targetClass' => Order::class, 'targetAttribute' => ['order_id' => 'id']],

        ];
    }


    /**
     * @throws \yii\db\Exception
     * @throws Exception
     * @throws \Exception
     */
    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        $companyId = \Yii::$app->user->identity->company_id;
        $this->price = $this->price * 100;
        $order = Order::findOne($this->order_id);
        $oldPrice = OrderRequest::find()->andWhere(['order_id' => $order->id])->orderBy(['price' => SORT_DESC])->one();
        //TODO oldin qatnashganmi yoqmi tekshiradi

        $orderRequest = OrderRequest::find()->andWhere(['company_id' => $companyId])->andWhere(['order_id' => $this->order_id]);

        if ($this->price >= $oldPrice->price) {
            throw new Exception(t("Siz taklif qilayotgan summa boshlang'ich summadan katta"));
        }

        if ($orderRequest->exists()) {
            throw new Exception(t("Siz oldin bu savdoda qatnashgansz"));
        }


        //TODO check time
//        if ($order->request_end < date('Y-m-d H:i:s')){
//            throw new Exception(t("Vaqt tugadi!!!"));
//        }

        $this->model->company_id = $oldPrice->company_id;
        $this->model->is_winner = 0;
        $this->model->created_at = date('Y-m-d H:i:s');
        $this->model->status = ShopEnum::ORDER_REQUEST_STATUS_ACTIVE;
        $this->model->type = ShopEnum::ORDER_REQUEST_TYPE_CUSTOMER;
        $att = $this->attributes;
        $this->model->setAttributes($att, false);
        if ($this->model->attributes && $this->model->validate() && $this->model->save()) {
//            $order->updateAttributes(['start_one_sided_producer'=>addDaysExcludingWeekends(date("Y-m-d H:i:s"), 1)]);
            $order->updateAttributes(['start_one_sided_producer' => date("Y-m-d H:i:s", strtotime("+10 minutes", time()))]);
            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}