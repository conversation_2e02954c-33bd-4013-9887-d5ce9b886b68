<?php


namespace api\modules\shop\forms;


use api\components\BaseModel;
use api\modules\shop\resources\ContractResource;
use common\enums\ContractEnum;
use common\models\shop\ContractCancelRequest;
use yii\base\Exception;
use yii\web\NotFoundHttpException;

class CancelForm extends BaseModel
{

    public ?ContractResource $model;
    public $id;

    public function rules()
    {
        return  [
            [['id'], 'required'],
        ];
    }


    /**
     * @throws \yii\db\Exception
     * @throws Exception
     * @throws NotFoundHttpException
     */
    public function getResult()
    {
        $this->model = ContractResource::findOne(['id' => $this->id, 'deleted_at' => null]);
        if (!$this->model)
            throw new NotFoundHttpException("Product not found");

        $transaction = \Yii::$app->db->beginTransaction();

        if ($this->model->status != ContractEnum::STATUS_CANCEL_PROCESS) {
            throw new Exception(t("Shartnoma bir tomonlama bekor qilish holatida emas"));
        }

        $request = ContractCancelRequest::find()->where('contract_id=' . $this->model->id)
            ->andWhere(['status' => ContractEnum::STATUS_REQUEST_NEW])->orderBy('created_at desc')->one();
        if (!$request) {
            throw new Exception(t("Shartnoma bir tomonlama bekor qilish so'rovi topilmadi"));
        }

        $request->status = ContractEnum::STATUS_REQUEST_CANCELED;
        if ($request->save()) {
            $this->model->status = $request->last_contract_status;
            if ($this->model->save()) {
                $transaction->commit();
                return $this->model->id;
            } else {
                $transaction->rollBack();
                $this->addErrors($this->model->errors);
                return false;
            }
        }

//        $this->model->status = ContractEnum::STATUS_RETURN_SIGNED;
//        if($this->model->attributes && $this->model->validate() && $this->model->save()){
//
//            $transaction->commit();
//            return true;
//        }

        $transaction->rollBack();
        $this->addErrors($request->errors);
        return false;
    }
}