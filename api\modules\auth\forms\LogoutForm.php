<?php

namespace api\modules\auth\forms;

use api\components\BaseRequest;
use api\modules\auth\resources\UserResource;
use common\enums\RoleEnum;
use common\enums\TenderEnum;
use common\enums\UserEnum;
use common\models\BlackList;
use common\models\CommissionMember;
use common\models\UserToken;
use Exception;
use Yii;
use yii\web\ServerErrorHttpException;
use yii\web\UnauthorizedHttpException;

class LogoutForm extends BaseRequest
{

    public function getResult()
    {
        /** @var UserResource $user */
        $user = Yii::$app->user->identity;

        if (!$user) {
            throw new UnauthorizedHttpException("Avval tizimga kiring");
        }

        UserToken::invalidateToken($user->id, UserToken::TYPE_LOGIN_DEFAULT);

        Yii::$app->user->logout();

        return true;
    }
}
