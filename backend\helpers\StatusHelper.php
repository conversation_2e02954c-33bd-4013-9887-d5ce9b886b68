<?php

namespace backend\helpers;


use common\enums\CompanyTransactionEnum;
use common\enums\ProductEnum;
use common\enums\ShopEnum;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;

class StatusHelper
{

    public static function statusList()
    {
        return [
            ProductEnum::SHOP_STATE_NEW => 'Moderator tekshiruvida',
            ProductEnum::SHOP_STATE_RETURN_MODERATOR =>  'Moderator tomondan qaytarilgan',
            ProductEnum::SHOP_STATE_ACTIVE =>  'Sotuvda',
            ProductEnum::SHOP_STATE_NO_MONEY=>  "Mablag' yetarli emas",
        ];
    }
    public static function refundsStatusList()
    {
        return [
            CompanyTransactionEnum::REFUNDS_STATUS_NEW => 'Moderator tekshiruvida',
            CompanyTransactionEnum::REFUNDS_STATUS_REJECTED =>  'Moderator tomondan qaytarilgan',
            CompanyTransactionEnum::REFUNDS_STATUS_ACCEPT =>  'Moderator tasdiqlagan',
        ];
    }
    public static function orderStatusList()
    {
        return [
            ShopEnum::ORDER_STATUS_INACTIVE => t('Aktiv holatda emas'),
            ShopEnum::ORDER_STATUS_ACTIVE =>   t('Aktiv holat'),
            ShopEnum::ORDER_STATUS_PROCESS =>  t('Jarayonda'),
            ShopEnum::ORDER_STATUS_CANCEL =>  t('Rad etilgan'),
            ShopEnum::ORDER_STATUS_WAITING =>  t('Kutish holatida'),
        ];
    }
    public static function statusLabel($state)
    {
        switch ($state) {
            case ProductEnum::SHOP_STATE_NEW:
                $class = 'label label-primary';
                break;
            case ProductEnum::SHOP_STATE_RETURN_MODERATOR:
                $class = 'label label-danger';
                break;
            case ProductEnum::SHOP_STATE_NO_MONEY:
                $class = 'label label-info';
                break;
            case ProductEnum::SHOP_STATE_ACTIVE:
                $class = 'label label-success';
                break;
            default:
                $class = 'label label-default';
        }

        return Html::tag('span', ArrayHelper::getValue(self::statusList(), $state), [
            'class' => $class,
        ]);
    }
    public static function refundsStatusLabel($state)
    {
        switch ($state) {
            case CompanyTransactionEnum::REFUNDS_STATUS_NEW:
                $class = 'label label-info';
                break;
            case CompanyTransactionEnum::REFUNDS_STATUS_REJECTED:
                $class = 'label label-danger';
                break;
            case CompanyTransactionEnum::REFUNDS_STATUS_ACCEPT:
                $class = 'label label-primary';
                break;
            default:
                $class = 'label label-default';
        }

        return Html::tag('span', ArrayHelper::getValue(self::refundsStatusList(), $state), [
            'class' => $class,
        ]);
    }
    public static function orderStatusLabel($state)
    {
        switch ($state) {
            case ShopEnum::ORDER_STATUS_INACTIVE:
                $class = 'label label-info';
                break;
            case ShopEnum::ORDER_STATUS_ACTIVE:
                $class = 'label label-primary';
                break;
            case ShopEnum::ORDER_STATUS_PROCESS:
                $class = 'label label-success';
                break;
            case ShopEnum::ORDER_STATUS_CANCEL:
                $class = 'label label-danger';
                break;
            case ShopEnum::ORDER_STATUS_WAITING:
                $class = 'label label-dark';
                break;
            default:
                $class = 'label label-default';
        }

        return Html::tag('span', ArrayHelper::getValue(self::orderStatusList(), $state), [
            'class' => $class,
        ]);
    }
}