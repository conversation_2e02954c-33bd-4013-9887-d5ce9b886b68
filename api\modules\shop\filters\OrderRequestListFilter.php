<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\shop\resources\OrderRequestResource;
use api\modules\shop\resources\OrderResource;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;
use common\models\Region;
use common\models\shop\Order;
use common\models\shop\OrderRequest;
use common\models\shop\Product;
use Yii;
use yii\data\ActiveDataProvider;
use yii\helpers\ArrayHelper;

class OrderRequestListFilter extends BaseRequest
{
    public $is_winner;
    public $order_id;
    public $product_id;
    public $company_id;

    public $pageNo = 0;
    public $pageSize = 10;
    public function rules()
    {
        return [
            [['pageNo', 'pageSize'], 'integer'],
            [['is_winner','product_id','order_id','company_id'], 'safe']
        ];
    }

    public function getResult()
    {
        $company_id = Yii::$app->user->identity->company_id;

        $orderRequest = OrderRequestResource::find()->andWhere([OrderRequestResource::tableName().'.company_id'=>$company_id])->orderBy(['id'=>SORT_DESC]);

        if ($this->is_winner){
            $orderRequest->andWhere(['is_winner'=>$this->is_winner]);
        }
        if ($this->order_id){
            $orderRequest->andWhere(['order_id'=>$this->order_id]);
        }
        if ($this->product_id){
            $orderRequest->leftJoin(OrderResource::tableName(),OrderRequestResource::tableName().'.order_id'."=".OrderResource::tableName().".id");
            $orderRequest->andWhere([OrderResource::tableName().'.product_id'=>$this->product_id]);
        }
        return paginate($orderRequest);

    }
}
