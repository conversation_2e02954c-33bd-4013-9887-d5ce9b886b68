<?php

namespace backend\resources;

use common\models\SoleSupplier;
use common\models\SoleSupplierClassifier;
use Yii;
use yii\helpers\ArrayHelper;

class SoleSupplierResource extends SoleSupplier
{

    public function _getClassifiers(): string
    {
        $title = 'title_'.Yii::$app->language;
        $classifiers = SoleSupplierClassifier::find()->with('classifier')->where(['sole_supplier_id' => $this->id])->all();
        if (empty($classifiers))
            return '';
        $data = ArrayHelper::getColumn($classifiers, 'classifier');
        if (empty($data))
            return '';
        $_data = array_column($data, $title);
        if (empty($_data))
            return '';
        return implode(', ', $_data);
    }

}