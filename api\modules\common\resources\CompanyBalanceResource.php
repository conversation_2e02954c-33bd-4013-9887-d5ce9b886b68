<?php


namespace api\modules\common\resources;


use common\models\CompanyBalance;

class CompanyBalanceResource extends CompanyBalance
{
    public function fields()
    {
        return [
            'id', 
            'balance' => function($model){
                return $model->balance / 100;
            },
            'available' => function($model){
                return $this->available / 100;
            }, 
            'blocked' => function($model){
                return $this->blocked / 100;
            }, 
            'penalty_in' => function($model){
                return $this->penalty_in / 100;
            }, 
            'penalty_out' => function($model){
                return $this->penalty_out / 100;
            }, 
            'outplay' => function($model){
                return $this->outplay / 100;
            }
        ];
    }

}