<?php

namespace api\modules\auth\forms;

use api\components\BaseModel;
use api\modules\auth\resources\UserResource;
use backend\modules\rbac\models\RbacAuthAssignment;
use common\enums\CompanyEnum;
use common\enums\OperationTypeEnum;
use common\enums\RoleEnum;
use common\enums\UserEnum;
use common\models\BlackList;
use common\models\Company;
use common\models\CompanyBalance;
use common\models\CompanyVirtualAccount;
use common\models\EconomicActivitiesType;
use common\models\OrganizationLegalForm;
use common\models\Region;
use Exception;
use Yii;

class SignupForm extends BaseModel
{
    public $pkcs7;
    public $email;
    public $company_name;
    public $director_fullname;
    public $region_id;
    public $district_id;
    public $address;
    public $phone;
    public $password;
    public $inn;
    public $confirmPassword;
    public $offer_agreement;
    public $organization_legal_form_id;
    public $economic_activities_type_id;

    public function rules()
    {
        return [
            [
                [
                    'inn',
                    'email',
                    'company_name',
                    'director_fullname',
                    'region_id',
                    'district_id',
                    'address',
                    'phone',
                    'password',
                    'confirmPassword',
                    'offer_agreement',
                    'organization_legal_form_id',
                    'economic_activities_type_id',
                ],
                'required', 'message' => t('{attribute} yuborish majburiy')
            ],
            [
                [
                    'email',
                    'company_name',
                    'director_fullname',
                    'region_id',
                    'district_id',
                    'address',
                    'phone',
                    'password',
                    'confirmPassword',
                ],
                'trim',
            ],
            [
                [
                    'email',
                    'company_name',
                    'director_fullname',
                    'address',
                    'phone',
                    'password',
                    'confirmPassword',
                ],
                'string',
            ],
            [
                [
                    'region_id',
                    'district_id',
                ],
                'integer',
            ],
            [
                ['region_id', 'district_id'],
                'exist',
                'skipOnError' => true,
                'targetClass' => Region::class,
                'targetAttribute' => ['region_id' => 'id'],
            ],
            [
                ['email'],
                'email',
            ],
            [
                ['email'],
                'unique',
                'targetClass' => UserResource::class,
                'message' => Yii::t('api', 'This email address has already been taken'),
            ],
             [
               ['phone'],
               'unique',
               'targetClass' => Company::class,
               'message' => Yii::t('api', 'This phone number has already been taken'),
             ],
            [
                ['password'],
                'string',
                'min' => 6,
            ],
            [
                ['password'],
                'compare',
                'compareAttribute' => 'confirmPassword',
            ],
            [
                ['offer_agreement'],
                'boolean',
            ],
            [
                ['offer_agreement'],
                'compare',
                'compareValue' => true,
                'message' => Yii::t('api', 'You must accept the offer agreement'),
            ],
            [
                ['phone'],
                'string',
                'min' => 9,
                'max' => 13,
            ],
            [['organization_legal_form_id'], 'exist', 'skipOnError' => true, 'targetClass' => OrganizationLegalForm::class, 'targetAttribute' => ['organization_legal_form_id' => 'id']],
            [['economic_activities_type_id'], 'exist', 'skipOnError' => true, 'targetClass' => EconomicActivitiesType::class, 'targetAttribute' => ['economic_activities_type_id' => 'id']],
        ];
    }

    public function getResult()
    {
        try {

//            $tin = $this->decodePks7($this->pkcs7);
            $tin = $this->inn;

            $transaction = Yii::$app->db->beginTransaction();

            $company = Company::find()->where(['tin' => $tin])->one();
            if ($company) {
                throw new Exception("Bu korxona avval ro'yxatdan o'tgan");
            }

            if(BlackList::find()->where(['inn' => $tin])->exists()){
                throw new Exception(Yii::t('main', "Tizimga kirish mumkin emas (insofsiz ijrochi)"));
            }

            $company = new Company();
            $company->title = $this->company_name;
            $company->tin = $tin;
            $company->resident = CompanyEnum::RESIDENT;
            $company->organization_type = CompanyEnum::NO_BYUDJET;
            $company->address = $this->address;
            $company->status = CompanyEnum::STATUS_ACTIVE;
            $company->region_id = $this->region_id;
            $company->district_id = $this->district_id;
            $company->phone = $this->phone;
            $company->director = $this->director_fullname;
            $company->organization_legal_form_id = $this->organization_legal_form_id;
            $company->economic_activities_type_id = $this->economic_activities_type_id;

            if (!$company->save(false)) {
                $transaction->rollBack();
                throw new Exception('Company save error');
            }

//            $account = new CompanyBankAccount();
//            $account->company_id = $company->id;
//            $account->mfo = $this->mfo;
//            $account->account = $this->account_number;
//            $account->is_main = 1;
//            $account->created_at = date("Y-m-d H:i:s");
//            if (!$account->save(false)) {
//                $transaction->rollBack();
//                throw new Exception('Company save error');
//            }

            foreach (OperationTypeEnum::P_K_ACCOUNTS as $account) {
                if (!CompanyVirtualAccount::saveVirtualAccount($company, $account))
                {
                    $transaction->rollBack();
                    throw new Exception('Company Virtual Account save error');
                }
            }

            $balance = new CompanyBalance();
            $balance->company_id = $company->id;
            $balance->available = 0;
            $balance->balance = 0;
            $balance->blocked = 0;
            if (!$balance->save(false)) {
                $transaction->rollBack();
                throw new Exception('Company balance save error');
            }


            $user = new UserResource();
            $user->username = $company->tin;
            $user->email = $this->email;
            $user->generateAuthKey();
            // TODO: phone column is not exists in user table
            // $user->phone = $this->phone;
            $user->setPassword($this->password);
            $user->status = UserEnum::STATUS_ACTIVE;
            $user->company_id = $company->id;
            $user->user_type = UserEnum::USER_TYPE_CUSTOMER;

            if (!$user->save(false)) {
                $transaction->rollBack();
                throw new Exception('User save error');
            }

            $role = new RbacAuthAssignment();
            $role->item_name = RoleEnum::ROLE_USER;
            $role->user_id = (string)$user->id;
            $role->created_at = time();
            if (!$role->save()) {
                $transaction->rollBack();
                throw new Exception('ROLE save error');
            }

            $transaction->commit();

            return true;
        } catch (\Throwable $th) {
            throw $th;
        }
    }

    protected function getAccessToken()
    {
        $access_token = Yii::$app->security->generateRandomString(40);
        $this->user->access_token = $access_token;

        if (!$this->user->save(false)) {
            throw new Exception('User access token save error');
        }

        return $access_token;
    }

}
