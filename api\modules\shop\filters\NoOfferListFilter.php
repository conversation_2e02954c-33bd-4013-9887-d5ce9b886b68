<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\shop\resources\OrderResource;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;
use common\enums\ShopEnum;
use common\models\shop\Order;
use Yii;
use yii\data\ActiveDataProvider;

class NoOfferListFilter extends BaseRequest
{
    public $classifier_category_id;
    public $classifier_id;
    public $plan_schedule_id;
    public $company_id;
    public $product_id;
    public $status;
    public $title;
    public $user_id;
    public $pageNo = 0;
    public $pageSize = 10;
    public function rules()
    {
        return [
            [['pageNo', 'pageSize'], 'integer'],
            [['plan_schedule_id','classifier_id','company_id','product_id','status','user_id','title'], 'safe']
        ];
    }

    public function getResult()
    {

        $model = OrderResource::find()->andWhere(['order.status'=>ShopEnum::ORDER_STATUS_PROCESS]);

        $model->andWhere(['or',
            ['order.user_id' => Yii::$app->user->identity->id],
            ['order.company_id' => Yii::$app->user->identity->company_id]
        ]);

        if ($this->product_id) {
            $model->andWhere(['order.product_id' => $this->product_id]);
        }
        if ($this->status) {
            $model->andWhere(['order.status' => $this->status]);
        }

        if ($this->user_id) {
            $model->andWhere(['order.user_id'=> $this->user_id]);
        }
        if ($this->title) {
            $model->leftJoin(ProductResource::tableName(),'`product`.`id` = `order`.`product_id`');
            $model->andWhere(['like','product.title',$this->title]);
        }
        return paginate($model);

    }
}
