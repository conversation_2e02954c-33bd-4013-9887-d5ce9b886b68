<?php

use common\enums\OperationTypeEnum;
use common\models\Company;
use common\models\CompanyVirtualAccount;
use yii\console\ExitCode;
use yii\db\Migration;

class m250622_102333_insert_birja_company extends Migration
{
    /**
     * {@inheritdoc}
     * @throws \yii\db\Exception
     * @throws Exception
     */
    public function safeUp()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $company = new Company();
            $company->title = "“Toshkent tovar xomashyo birjasi” AJ";
            $company->tin = "*********";
            if (!$company->save())
            {
                echo json_encode($company->getErrors()) . PHP_EOL;
                return ExitCode::UNSPECIFIED_ERROR;
            }
            foreach (OperationTypeEnum::ACTIVE_ACCOUNTS as $account) {
                $isSaved = CompanyVirtualAccount::saveVirtualAccount($company, $account);
                if (!$isSaved)
                {
                    $transaction->rollBack();
                    echo "Virtual account save failed" . PHP_EOL;
                    return ExitCode::UNSPECIFIED_ERROR;
                }
            }
            $transaction->commit();
            return ExitCode::OK;
        } catch (Exception $ex) {
            $transaction->rollBack();
            echo $ex->getMessage() . PHP_EOL;
            return ExitCode::UNSPECIFIED_ERROR;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $company = Company::findOne(['tin' => "*********"]);
        if ($company)
        {
            CompanyVirtualAccount::deleteAll(['company_id' => $company->id]);
            $company->delete();
        }
    }
}
