<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%product_draft_file}}`.
 */
class m250717_111755_create_product_draft_file_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%product_draft_file}}', [
            'id' => $this->primaryKey(),
            'product_id' => $this->integer(),
            'file_id' => $this->integer(),

            'sort' => $this->integer()->defaultValue(0),

            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);

        $this->createIndex("idx_product_draft_file_product_id", "product_file", "product_id");
        $this->createIndex("idx_product_draft_file_file_id", "product_file", "file_id");

        $this->addForeignKey("fk_product_draft_file_product_id", "product_draft_file", "product_id", "product_draft", "id", "cascade", "cascade");
        $this->addForeignKey("fk_product_draft_file_file_id", "product_draft_file", "file_id", "file", "id", "cascade", "cascade");
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey("fk_product_draft_file_product_id", "product_file");
        $this->dropForeignKey("fk_product_draft_file_file_id", "product_file");

        $this->dropIndex("idx_product_draft_file_product_id", "product_file");
        $this->dropIndex("idx_product_draft_file_file_id", "product_file");

        $this->dropTable('{{%product_draft_file}}');
    }
}
