<?php


namespace api\modules\tender\resources;


use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\RegionResource;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderRequestValues;

class TenderRequestValuesResource extends TenderRequestValues
{

    public function fields()
    {
        return [
            'id',
            'tender_classifier_id',
            'classifier_id',
            'tender_request_id',
            'country_id',
            'price',
            'price_qqs',
            'purchase_currency',
            'classifier',
            'tenderClassifier',
            'country'
        ];
    }

    public function extraFields()
    {
        return [
            'classifier',
            'tenderClassifier',
            'country'
        ];
    }

    public function getClassifier()
    {
        return $this->hasOne(ClassifierResource::class, ['id' => 'classifier_id']);
    }

    public function getCountry()
    {
        return $this->hasOne(RegionResource::class, ['id' => 'country_id']);
    }

    /**
     * Gets query for [[Tender]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTender()
    {
        return $this->hasOne(TenderResource::class, ['id' => 'tender_id']);
    }

    /**
     * Gets query for [[TenderClassifier]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTenderClassifier()
    {
        return $this->hasOne(TenderClassifierResource::class, ['id' => 'tender_classifier_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}