<?php


namespace api\modules\auction\resources;


use api\modules\common\resources\FileResource;
use common\models\auction\AuctionCondition;

class AuctionConditionResource extends AuctionCondition
{
    public function fields()
    {
        return ['id', 'condition', 'text' => function ($model) {
            if ($model->condition == 'conditionFile') {
                $file = FileResource::findOne($model->text);
                if ($file) {
                    return $file;
                }
            }
            return $model->text;

        }];
    }

}