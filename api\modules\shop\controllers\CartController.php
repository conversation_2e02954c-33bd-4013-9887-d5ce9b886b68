<?php

namespace api\modules\shop\controllers;

use api\components\ApiController;
use api\modules\shop\filters\CartListFilter;
use api\modules\shop\forms\CartDeleteForm;
use api\modules\shop\forms\CartForm;
use api\modules\shop\forms\OrderForm;
use api\modules\shop\resources\CartResource;
use api\modules\shop\resources\OrderResource;
use api\modules\shop\resources\ProductResource;
use common\behaviors\RoleAccessBehavior;
use Yii;

/**
 * Default controller for the `shop` module
 */
class CartController extends ApiController
{
    public function behaviors()
    {
        $parent = parent::behaviors();
        $parent['accessByRole'] = [
            'class' => RoleAccessBehavior::class,
            'rules' => [
                'index' => ['user'],
                'create' => ['user'],
                'delete' => ['user'],
            ],
        ];
        return $parent;
    }

    public function actionIndex()
    {
        return $this->sendResponse(
            new CartListFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionCreate()
    {
        return $this->sendResponse(
            new CartForm(new CartResource()),
            Yii::$app->request->bodyParams
        );
    }
    public function actionDelete($id)
    {
        return $this->sendResponse(
            new CartDeleteForm($this->findOne($id)),
            Yii::$app->request->bodyParams
        );
    }
    private function findOne($id)
    {
        $model = ProductResource::findOrFail($id);

        return $model;
    }

}
