<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\shop\resources\ContractRequestResource;
use api\modules\shop\resources\OrderRequestResource;
use api\modules\shop\resources\OrderResource;
use common\enums\ShopEnum;
use Yii;

class ContractRequestFilter extends BaseRequest
{
    public $pageNo = 0;
    public $pageSize = 10;
    public function rules()
    {
        return [
            [['pageNo', 'pageSize'], 'integer'],
        ];
    }

    public function getResult()
    {
        $user = Yii::$app->user->identity;
        $model = ContractRequestResource::find()->where(['company_id' => $user->company_id]);
        $model->orderBy('created_at desc');
        return paginate($model);
    }
}
