<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\shop\resources\ContractExternalAuctionResource;
use api\modules\shop\resources\ContractResource;
use common\enums\ContractEnum;
use common\models\Contract;
use Yii;
use yii\db\Expression;

class ExternalAuctionContractFilter extends BaseRequest
{
    public function getResult()
    {
        $model = ContractExternalAuctionResource::find()
            ->andWhere("auction_id is not null");
        return paginate($model);
    }
}
