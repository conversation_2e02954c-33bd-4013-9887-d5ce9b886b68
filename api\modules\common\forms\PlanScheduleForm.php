<?php

namespace api\modules\common\forms;

use api\components\BaseRequest;
use api\modules\common\resources\PlanScheduleClassifierCreateResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\StatusEnum;
use common\enums\TenderEnum;
use common\models\PlanScheduleClassifierUnit;
use Yii;

class PlanScheduleForm extends BaseRequest
{

    public $year;
    public $quarter;
    public $title;
    public $classifiers = [];

    public PlanScheduleResource $model;

    public function __construct($params = [])
    {
        parent::__construct($params);
    }

    /**
     * {@inheritdoc}
     * #return, year qiymatini aniqlashtirish kerak
     */
    public function rules()
    {
        return [
            [['classifiers', 'title', 'quarter', 'year'], 'required', 'message' => t('{attribute} yuborish kerak')],
            [['title'], 'string', 'max' => 255],
            ['quarter', 'integer', 'min' => 1, 'max' => 4],
            ['year', 'integer', 'min' => date("Y"), 'max' => date("Y", strtotime("+1 year"))],
        ];
    }

    public function attributeLabels()
    {
        return [
            'year' => Yii::t('main', 'Yil'),
            'quarter' => Yii::t('main', 'Chorak'),
            'title' => Yii::t('main', 'Reja-jadval nomi'),
            'classifiers' => Yii::t('main', 'Mahsulot'),
        ];
    }


    public function getResult()
    {
        $transaction = Yii::$app->db->beginTransaction();
        $model = new PlanScheduleResource([
            'title' => $this->title,
            'company_id' => Yii::$app->user->identity->company_id,
            'status' => StatusEnum::STATUS_ACTIVE,
            'year' => $this->year,
            'quarter' => $this->quarter,
            'total_product_count' => 0,
        ]);

        if (!$model->save()) {
            $this->addErrors($model->errors);
            $transaction->rollBack();
            return false;
        }

        foreach ($this->classifiers as $classifier) {
            $description = "";
            if(isset($classifier['description']) && !empty($classifier['description']) && $classifier['description'] != null) {
                $description = $classifier['description'];
            }
//            if (PlanScheduleClassifierResource::find()->notDeleted()
//                ->andWhere(['plan_schedule_id' => $model->id, 'classifier_id' => $classifier['classifier_id'], 'status' => TenderEnum::STATUS_ACTIVE])->exists()
//            ) {
//                $this->addError("classifiers", t("Bu maxsulot avval qo'shilgan"));
//                $transaction->rollBack();
//                return false;
//            }

            $product = new PlanScheduleClassifierCreateResource([
                'plan_schedule_id' => $model->id,
                'classifier_id' => $classifier['classifier_id'],
                'description' => $description,
                'year' => $model->year,
                'month' => $classifier['month'],
                'count' => $classifier['count'],
                'count_live' => $classifier['count'],
                'count_used' => 0,
                'status' => TenderEnum::STATUS_ACTIVE,
                'enabled' => 1,
            ]);

            $model->total_product_count += $product->count;

            if (!$product->save()) {
                $this->addError('classifiers', $product->errors);
                $transaction->rollBack();
                return false;
            }
            foreach ($classifier['unit'] as $unitId) {
                $pscp = new PlanScheduleClassifierUnit();
                $pscp->plan_schedule_classifier_id = $product->id;
                $pscp->classifier_property_id = $unitId;
                $pscp->status = StatusEnum::STATUS_ACTIVE;
                if (!$pscp->save()) {
                    $this->addError('unit', $pscp->errors);
                    $transaction->rollBack();
                    return false;
                }
            }

        }

        if (!$model->save()) {
            $this->addError('classifiers', $model->errors);
            $transaction->rollBack();
            return false;
        }

        $transaction->commit();
        return true;
    }
}
