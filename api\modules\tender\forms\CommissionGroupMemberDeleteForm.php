<?php


namespace api\modules\tender\forms;

use api\components\BaseRequest;
use api\modules\tender\resources\CommissionGroupResource;
use api\modules\tender\resources\CommissionMemberResource;
use api\modules\tender\resources\CommissionGroupMemberResource;
use api\modules\tender\resources\TenderCommissionMemberResource;
use common\enums\StatusEnum;
use common\enums\TenderEnum;
use Yii;

class CommissionGroupMemberDeleteForm extends BaseRequest
{

    public $commission_group_id;
    public $commission_member_id;

    public function rules()
    {
        return [
            [['commission_group_id', 'commission_member_id'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['commission_group_id', 'commission_member_id'], 'integer'],
            [['commission_group_id'], 'exist', 'skipOnError' => true, 'targetClass' => CommissionGroupResource::class, 'targetAttribute' => ['commission_group_id' => 'id']],
            [['commission_member_id'], 'exist', 'skipOnError' => true, 'targetClass' => CommissionMemberResource::class, 'targetAttribute' => ['commission_member_id' => 'id']],
        ];
    }

    public function getResult()
    {
        if (!CommissionMemberResource::find()->notDeletedAndFromCompany()->andWhere(['id' => $this->commission_member_id])->exists()) {

            $this->addError('commission_member_id', t("Komissiya azosi topilmadi"));
            return false;
        }

        if (!CommissionGroupResource::find()->notDeletedAndFromCompany()->andWhere(['id' => $this->commission_group_id])->exists()) {

            $this->addError('commission_group_id', t("Komissiya guruhi topilmadi"));
            return false;
        }
        if (!CommissionGroupMemberResource::find()->notDeleted()->andWhere('commission_member_id=' . $this->commission_member_id . ' and commission_group_id=' . $this->commission_group_id)->exists()) {

            $this->addError('commission_member_id', t("Komissiya azosi bu guruhda mavjud emas"));
            return false;
        }
        if (TenderCommissionMemberResource::find()->notDeletedAndFromCompany()->andWhere(['commission_member_id' => $this->commission_member_id, 'status' => TenderEnum::STATUS_ACTIVE])->exists()) {

            $this->addError('commission_member_id', t("Komissiya azosi tenderda ishtirok etyapti. O'chirish mumkin emas"));
            return false;
        }

        return CommissionGroupMemberResource::updateAll(['deleted_at' => date("Y-m-d H:i:s"), 'updated_by' => \Yii::$app->user->id, 'status' => StatusEnum::STATUS_DELETED],
                'commission_member_id=' . $this->commission_member_id . ' and commission_group_id=' . $this->commission_group_id . " and status = 300"
            ) > 0;
    }
}

