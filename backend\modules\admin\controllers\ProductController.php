<?php


namespace backend\modules\admin\controllers;


use api\components\ApiController;
use backend\modules\admin\filters\ProductListFilter;
use Yii;

class ProductController extends ApiController
{
    public function actionIndex($platform_display)
    {
        return $this->sendResponse(
            new ProductListFilter($platform_display),
            Yii::$app->request->queryParams
        );
    }
}