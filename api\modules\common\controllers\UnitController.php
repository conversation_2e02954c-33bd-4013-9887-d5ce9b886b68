<?php

namespace api\modules\common\controllers;

use api\components\ApiController;
use api\modules\common\filters\UnitFilter;
use api\modules\common\filters\UnitTenderConditionFilter;
use Yii;

class UnitController extends ApiController
{
    public function actionIndex()
    {
        return $this->sendResponse(
            new UnitFilter(),
            Yii::$app->request->queryParams,
        );
    }

    public function actionTenderCondition()
    {
        return $this->sendResponse(
            new UnitTenderConditionFilter(),
            Yii::$app->request->queryParams,
        );
    }
}
