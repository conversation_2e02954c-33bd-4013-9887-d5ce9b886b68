<?php

use yii\helpers\Html;
use yii\bootstrap4\ActiveForm;

/**
 * @var yii\web\View $this
 * @var common\models\auction\Auction $model
 * @var yii\bootstrap4\ActiveForm $form
 */
?>

<div class="auction-form">
    <?php $form = ActiveForm::begin(); ?>
        <div class="card">
            <div class="card-body">
                <?php echo $form->errorSummary($model); ?>

                <?php echo $form->field($model, 'id')->textInput() ?>
                <?php echo $form->field($model, 'company_id')->textInput() ?>
                <?php echo $form->field($model, 'classifier_category_id')->textInput() ?>
                <?php echo $form->field($model, 'status')->textInput() ?>
                <?php echo $form->field($model, 'total_sum')->textInput(['maxlength' => true]) ?>
                <?php echo $form->field($model, 'cancel_reason')->textInput(['maxlength' => true]) ?>
                <?php echo $form->field($model, 'auction_end')->textInput() ?>
                <?php echo $form->field($model, 'cancel_date')->textInput() ?>
                <?php echo $form->field($model, 'payment_status')->textInput() ?>
                <?php echo $form->field($model, 'payment_date')->textInput() ?>
                <?php echo $form->field($model, 'delivery_period')->textInput() ?>
                <?php echo $form->field($model, 'payment_period')->textInput() ?>
                <?php echo $form->field($model, 'receiver_email')->textInput(['maxlength' => true]) ?>
                <?php echo $form->field($model, 'receiver_phone')->textInput(['maxlength' => true]) ?>
                <?php echo $form->field($model, 'region_id')->textInput() ?>
                <?php echo $form->field($model, 'zip_code')->textInput(['maxlength' => true]) ?>
                <?php echo $form->field($model, 'address')->textInput(['maxlength' => true]) ?>
                <?php echo $form->field($model, 'created_at')->textInput() ?>
                <?php echo $form->field($model, 'updated_at')->textInput() ?>
                <?php echo $form->field($model, 'deleted_at')->textInput() ?>
                <?php echo $form->field($model, 'created_by')->textInput() ?>
                <?php echo $form->field($model, 'updated_by')->textInput() ?>
                <?php echo $form->field($model, 'account')->textInput(['maxlength' => true]) ?>
                <?php echo $form->field($model, 'plan_schedule_id')->textInput() ?>
                
            </div>
            <div class="card-footer">
                <?php echo Html::submitButton($model->isNewRecord ? 'Create' : 'Update', ['class' => $model->isNewRecord ? 'btn btn-success' : 'btn btn-primary']) ?>
            </div>
        </div>
    <?php ActiveForm::end(); ?>
</div>
