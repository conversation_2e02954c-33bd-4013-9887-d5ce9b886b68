<?php

namespace api\components;

use common\enums\CompanyEnum;
use common\models\Company;
use Yii;
use yii\base\Model;

class BaseModel extends BaseRequest
{
    public $pkcs7;
    public function rules()
    {
        return [
            [['pkcs7'], 'required', 'message' => t("Kluch bilan tasdiqlash kerak")],
            [['pkcs7'], 'checkPks7'],
        ];
    }
    public function checkPks7()
    {
        $pkcs7 = $this->pkcs7;
        if (!$pkcs7) {
            $this->addError("pkcs7", t("Kluch bilan tasdiqlanmagan"));
            return false;
        }
        return true;

        $response = checkPkcs($pkcs7);
        $response = json_decode($response);
        if (isset($response->subjectCertificateInfo) && $response->subjectCertificateInfo != null) {
            if ($response->subjectCertificateInfo->subjectName) {
                $tin = isset($response->subjectCertificateInfo->subjectName->{'1.2.860.3.16.1.1'}) && $response->subjectCertificateInfo->subjectName->{'1.2.860.3.16.1.1'} != null ? $response->subjectCertificateInfo->subjectName->{'1.2.860.3.16.1.1'} : null;
                if ($tin) {
                    $company = Company::findOne(['tin' => $tin, 'id' => Yii::$app->user->identity->company_id]);
                    if (!$company) {
                        $this->addError("pkcs7", t("Korxona topilmadi"));
                        return false;
                    }
                    if ($company->status == CompanyEnum::STATUS_ACTIVE) {
                        return true;
                    } else {
                        $this->addError("pkcs7", t("Korxona aktiv emas"));
                        return false;
                    }
                } else {
                    $this->addError("pkcs7", t("Kluch yuridik shaxsga tegishli emas"));
                    return false;
                }
            } else {
                $this->addError("pkcs7", t("Imzolangan ma'lumot yaroqsiz"));
                return false;
            }
        } else {
            $this->addError("pkcs7", t("Imzolangan ma'lumot yaroqsiz"));
            return false;
        }
    }
    public  function getResult(){

    }
}
