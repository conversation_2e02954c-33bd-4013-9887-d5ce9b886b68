<?php


namespace api\modules\tender\resources;


use api\modules\backend\resources\TenderModeratorLogResource;
use api\modules\client\resources\TenderRequestResource;
use api\modules\client\resources\TenderRequirementsAnswerResource;
use api\modules\common\resources\CompanyShortResource;
use api\modules\common\resources\FileResource;
use common\enums\TenderEnum;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\Tender;
use yii\web\NotFoundHttpException;

class TenderActiveLotsDetailResource extends Tender
{
    public function fields()
    {
        return [
            'id',
            'lot',
            'title',
            'updated_at',
            'end_date',
            'tenderTotalPrice' => function($model){
                return $model->getTenderTotalPriceBase();
            },
            'company',
            'address',
            'contact_fio',
            'contact_position',
            'contact_phone',
            'tenderClassifiers',
            'tenderRequirements',
            'method_payment',
            'purchase_currency',

            'criteria_evaluation_proposals',
            'technical_part',
            'price_part',
            'amount_deposit',
            'unblocking_type',
            'funding_source',
            'description',
            'updated_at',
            'accept_guarantee_bank',
            'advance_payment_percentage',
            'advance_payment_period',
            'end_date',
            'views',
            'contactFile',
            'preference_local_producer',

        ];
    }

    public function extraFields()
    {
        return ['myRequest', 'tenderQualificationList', 'files'];
    }

    public function getFiles()
    {
        return $this->hasMany(TenderFilesResource::class, ['tender_id' => 'id']);
    }


    /**
     * Gets query for [[TenderClassifiers]].
     */
    public function getTenderClassifiers()
    {
        return TenderClassifierActiveLotsResource::find()->where('deleted_at is null')
            ->andWhere(['tender_id' => $this->id])->andWhere('status='.TenderEnum::STATUS_ACTIVE )->all();

    }

    public function getCompany()
    {
        return $this->hasOne(CompanyShortResource::class, ['id' => 'company_id']);
    }

    public function getMyRequest()
    {
        return TenderRequestResource::find()->notDeleted()->andWhere(['tender_id' => $this->id, 'company_id' => \Yii::$app->user->identity->company_id])->exists();
    }

    public function getTenderQualificationList()
    {
        return TenderQualificationFileListResource::find()->andWhere(['tender_id' => $this->id, 'status' => TenderEnum::STATUS_ACTIVE])->all();
    }


    public function getTenderRequirements()
    {
        return $this->hasMany(TenderRequirementsActiveLotsResource::class, ['tender_id' => 'id'])->andWhere([TenderRequirementsActiveLotsResource::tableName() . '.status' => TenderEnum::STATUS_ACTIVE]);;
    }

    public function getTechnicalDocumentFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'technical_document_file_id']);
    }


    public function getTenderRequirementsAnswer()
    {
        return $this->hasMany(TenderRequirementsAnswerResource::class, ['tender_id' => 'id']);
    }

    public function getTenderRequestRating()
    {
        return $this->hasMany(TenderRequestRatingResource::class, ['tender_id' => 'id']);
    }


    public function getTenderRequest()
    {
        return TenderRequestResource::find()
            ->notDeleted()
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_id' => $this->id])
            ->all();
        //return $this->hasMany(TenderRequestResource::class, ['tender_id' => 'id']);
    }

    public function getTenderRequestCount()
    {
        return count($this->tenderRequest);
    }

    public function getQualificationSelection()
    {
        return $this->hasMany(TenderQualificationSelectionResource::class, ['tender_id' => 'id']);
    }

    public function getTenderCommissionMembers()
    {
        return $this->hasMany(TenderCommissionMemberResource::class, ['tender_id' => 'id'])->andWhere([TenderCommissionMemberResource::tableName() . '.status' => TenderEnum::STATUS_ACTIVE]);;
    }

    public function getTenderCommissionMembersCount()
    {
        return TenderCommissionMemberResource::find()->notDeletedAndFromCompany()->where(['tender_id' => $this->id, 'status' => TenderEnum::STATUS_ACTIVE])->count();
    }


    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }


//    public function getCommissionMember(int $commissionId)
//    {
//        $model = TenderCommissionMemberResource::find()->notDeletedAndFromCompany()
//            ->where(['tender_commission_member.tender_id' => $this->id, 'tender_commission_member.commission_member_id' => $commissionId, 'tender_commission_member.status' => TenderEnum::STATUS_ACTIVE])
//            ->andWhere(['tender_commission_member.status' => TenderEnum::STATUS_ACTIVE])
//            ->one();
//        if (!$model) new NotFoundHttpException("Ma'lumot topilmadi");
//        return $model;
//    }

    public function getCommissionVote(int $commissionId = null)
    {
        if ($commissionId == null) {
            \Yii::$app->user->identity->commissionMemberId;
        }
        return TenderCommissionVoteResource::find()
            ->notDeleted()
            ->andWhere(['tender_id' => $this->id, 'commission_member_id' => $commissionId, 'status' => TenderEnum::STATUS_ACTIVE])
            ->exists();
    }

    public function getCommissionVoteYesCount()
    {
        return TenderCommissionVoteResource::find()
            ->where(['tender_id' => $this->id, 'vote' => TenderEnum::VOTE_YES, 'status' => TenderEnum::STATUS_ACTIVE])
            ->count();
    }

    public function getCommissionVoteNoCount()
    {
        return TenderCommissionVoteResource::find()
            ->where(['tender_id' => $this->id, 'vote' => TenderEnum::VOTE_NO, 'status' => TenderEnum::STATUS_ACTIVE])
            ->count();
    }

    public function getTenderModeratorLog()
    {
        return TenderModeratorLogResource::find()->where(['tender_id' => $this->id])->orderBy(['id' => SORT_DESC])->one();
    }

}