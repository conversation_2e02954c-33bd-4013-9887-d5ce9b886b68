<?php


namespace api\modules\tender\forms;


use Yii;
use api\components\BaseRequest;
use api\modules\tender\resources\TenderResource;
use common\enums\OperationTypeEnum;
use common\enums\TenderEnum;
use common\models\VirtualTransaction;
use common\models\WorkdayCalendar;
use yii\db\Exception;
use yii\helpers\ArrayHelper;

class TenderReleaseForm extends BaseRequest
{
    public TenderResource $model;
    public TenderResource $id;
    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return  [];
    }


    /**
     * @throws Exception
     * @throws \Throwable
     * @throws \yii\base\Exception
     */
    public function getResult()
    {

        //$companyId = \Yii::$app->user->identity->company_id;
        // o'chirilgan bo'lmasa


        $company = \Yii::$app->user->identity->company;
        // 0.15 foiz vositachilik yigʻimi zakaz<PERSON>kdan lekin 10 ming soʻmdan oshmaydi. Zalog yoʻq zakazchikdan
        $totalPrice = $this->model->tenderTotalPrice;
        $commission_sum = $totalPrice * env('COMMISSION_PERCENT', 0.0015);
        $total_block_sum = ($commission_sum > env('TENDER_CUSTOMER_MAX_COMMISSION_SUM', 1000000) ? env('TENDER_CUSTOMER_MAX_COMMISSION_SUM', 1000000) : $commission_sum);

//        if ($company->availableBalance < $total_block_sum) {
//            $this->addError("error", t("Balansda mablag' yetarli emas"));
//            return false;
//        }
        if (!hasMoney($company, $total_block_sum)) {
            $this->addError("error", t("Balansda mablag' yetarli emas"));
            return false;
        }
        $transaction = Yii::$app->db->beginTransaction();
//        $company_transaction_commission = new CompanyTransaction([
//            'company_id' => $company->id,
//            'tender_id' => $this->model->id,
//            'amount' => $total_block_sum,
//            'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION,
//            'description' => Yii::t("main", "Tender uchun komissiya bandlandi"),
//            'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//            'transaction_date' => date("Y-m-d H:i:s"),
//        ]);
//
//        if (!$company_transaction_commission->save()) {
//
//            $this->addErrors($company_transaction_commission->errors);
//            $transaction->rollBack();
//            return false;
//        }
        try {
            VirtualTransaction::saveTransaction(
                $company,
                $company,
                OperationTypeEnum::P_K_30101,
                OperationTypeEnum::P_K_30202,
                $total_block_sum,
                Yii::t("main", "Tender uchun komissiya bandlandi"),
                OperationTypeEnum::PRODUCT_NAME_TENDER,
                $this->model->id,
                null,
                OperationTypeEnum::BLOCK_SALE_COMMISSION
            );
        } catch (Exception $ex) {
            $transaction->rollBack();
            $this->addError("error", $ex->getMessage());
            return false;
        }
        date_default_timezone_set("Asia/Tashkent");

        $holidays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::HOLIDAY]), 'local_date', 'local_date');
        $workDays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::WORKDAY]), 'local_date', 'local_date');

        $this->model->publish_days = $this->model->placement_period;
        $endDate = addDaysExcludingWeekends(date("Y-m-d H:i:s"), $this->model->placement_period, $workDays, $holidays);

        $this->model->end_date = $endDate;
        $this->model->state = TenderEnum::STATE_READY;
        $this->model->status = TenderEnum::STATUS_ACTIVE;

        if ($this->model->save()) {
            $transaction->commit();
            return true;
        } else {

            $this->addErrors($this->model->errors);
            $transaction->rollBack();
            return false;
        }

    }
}