<?php


namespace api\modules\shop\forms;


use api\components\BaseModel;
use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\VirtualTransactionResource;
use api\modules\shop\resources\OrderRequestResource;
use api\modules\shop\resources\OrderResource;
use api\modules\shop\resources\ProductResource;
use common\enums\CompanyEnum;
use common\enums\CompanyTransactionEnum;
use common\enums\OperationTypeEnum;
use common\enums\ProductEnum;
use common\enums\ShopEnum;
use common\models\Company;
use common\models\CompanyBalance;
use common\models\CompanyTransaction;
use common\models\TenderModeratorLog;
use common\models\VirtualTransaction;
use Yii;
use yii\base\Exception;
use function foo\func;

class CustomerRejectForm extends BaseModel
{

    public OrderRequestResource $model;
    public $id;

    public function rules()
    {
        return  [['id'],'required'];
    }

    /**
     * @throws \yii\db\Exception
     * @throws Exception
     */
    public function getResult()
    {
        $this->model = OrderRequestResource::findOne(['id' => $this->id]);
        if (!$this->model) {
            throw new Exception(t("Requested order not found"));
        }
        $transaction = \Yii::$app->db->beginTransaction();

        $this->model->status = ShopEnum::ORDER_REQUEST_STATUS_CANCEL;
        if ($this->model->save()){
            $order = OrderResource::findOne($this->model->order_id);

            if (!$order){
                throw new Exception(t("Order not found"));
            }
            $order->updateAttributes([
                'status'=>ShopEnum::ORDER_STATUS_CANCEL
            ]);

           $companyID = Yii::$app->user->identity->company_id;
           $planSchedule = $order->planSchedule;
           if($planSchedule->company_id != $companyID){
               $this->addError("error", "Sizga tegishli emas");
           }


            //TODO zaloglarni qaytarish
//            $zalogs = CompanyTransaction::find()
//                ->where(['order_id' => $this->model->order_id, 'type' => CompanyTransactionEnum::TYPE_ZALOG, 'reverted_id' => null])
//                ->andWhere(['in', 'company_id', [$order->company_id, $companyID]])
//                ->all();
//            foreach ($zalogs as $company_transaction) {
//                $revert = new CompanyTransaction([
//                    'company_id' =>  $company_transaction->company_id,
//                    'contract_id' => null,
//                    'order_id' => $company_transaction->order_id,
//                    'amount' => $company_transaction->amount,
//                    'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
//                    'description' => Yii::t("main", "Narx so'rovi kelishilmaganligi uchun zalog blokdan chiqarildi"),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => date("Y-m-d H:i:s"),
//                ]);
//                if ($revert->save()){
//                    $company_transaction->reverted_id = $revert->id;
//                    $company_transaction->save(false);
//                } else {
//                    $transaction->rollBack();
//                    $this->addErrors($revert->errors);
//                    return false;
//                }
//            }
           $deposits = VirtualTransactionResource::find()
               ->where(['order_id' => $this->model->order_id,'operation_type' => OperationTypeEnum::BLOCK_SALE_DEPOSIT,'parent_id' => null])
               ->andWhere(['in', 'debit_company_id', [$order->company_id, $companyID]])
               ->andWhere(['>', 'credit', 0])->all();
            foreach ($deposits as $deposit) {
                /** @var VirtualTransactionResource $deposit */
                $revertID = VirtualTransaction::saveTransaction(
                    $deposit->creditCompany,
                    $deposit->debitCompany,
                    $deposit->creditAccount->prefix,
                    $deposit->debitAccount->prefix,
                    $deposit->credit,
                    Yii::t("main", "Narx so'rovi kelishilmaganligi uchun zalog blokdan chiqarildi"),
                    OperationTypeEnum::PRODUCT_NAME_ORDER,
                    $deposit->order_id,
                    null,
                    OperationTypeEnum::UNBLOCK_SALE_DEPOSIT,
                );
                $deposit->parent_id = $revertID;
                if (!$deposit->save()){
                    $transaction->rollBack();
                    $this->addErrors($deposit->errors);
                    return false;
                }
            }
            //TODO komissiyani qaytarish
//            $commissions = CompanyTransaction::find()
//                ->where(['order_id' => $this->model->order_id, 'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION, 'reverted_id' => null])
//                ->andWhere(['in', 'company_id', [$order->company_id, $companyID]])->all();
//
//            foreach ($commissions as $commission) {
//                $revert = new CompanyTransaction([
//                    'company_id' =>  $commission->company_id,
//                    'contract_id' => null,
//                    'order_id' => $commission->order_id,
//                    'amount' => $commission->amount,
//                    'type' => CompanyTransactionEnum::TYPE_REVERT_BLOCK_COMMISION,
//                    'description' => Yii::t("main", "Narx sorovi kelishilmaganligi uchun komissiya summasi blokdan chiqarildi"),
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'transaction_date' => date("Y-m-d H:i:s"),
//                ]);
//                if ($revert->save()){
//                    $commission->reverted_id = $revert->id;
//                    $commission->save(false);
//                } else {
//                    $transaction->rollBack();
//                    $this->addErrors($revert->errors);
//                    return false;
//                }
//            }

            $commissions = VirtualTransactionResource::find()
                ->where(['order_id' => $this->model->order_id,'operation_type' => OperationTypeEnum::BLOCK_SALE_COMMISSION,'parent_id' => null])
                ->andWhere(['in', 'debit_company_id', [$order->company_id, $companyID]])
                ->andWhere(['>', 'credit', 0])->all();
            foreach ($commissions as $commission) {
                /** @var VirtualTransactionResource $commission */
                $revertID = VirtualTransaction::saveTransaction(
                    $commission->creditCompany,
                    $commission->debitCompany,
                    $commission->creditAccount->prefix,
                    $commission->debitAccount->prefix,
                    $commission->credit,
                    Yii::t("main", "Narx sorovi kelishilmaganligi uchun komissiya summasi blokdan chiqarildi"),
                    OperationTypeEnum::PRODUCT_NAME_ORDER,
                    $commission->order_id,
                    null,
                    OperationTypeEnum::UNBLOCK_SALE_COMMISSION,
                );
                $commission->parent_id = $revertID;
                if (!$commission->save()){
                    $transaction->rollBack();
                    $this->addErrors($commission->errors);
                    return false;
                }
            }

            $transaction->commit();
            return true;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}