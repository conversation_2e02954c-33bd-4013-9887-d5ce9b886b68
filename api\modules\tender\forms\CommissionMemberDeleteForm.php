<?php

namespace api\modules\tender\forms;

use api\components\BaseRequest;
use api\modules\tender\resources\CommissionMemberResource;
use api\modules\tender\resources\TenderCommissionMemberResource;
use common\enums\TenderEnum;
use Yii;
use yii\base\Exception;

class CommissionMemberDeleteForm extends BaseRequest
{
    public CommissionMemberResource $model;

    public function __construct(CommissionMemberResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function getResult()
    {
        if (TenderCommissionMemberResource::find()->where(['commission_member_id' => $this->model->id, 'status' => TenderEnum::STATUS_ACTIVE])->exists()) {
            throw new Exception(t("Komisiya a'zosi tenderlarda qatnashgan, o'chirish mumkin emas"));
        }
        $this->model->deleted_at = date("Y-m-d H:i:s");
        $this->model->updated_by = Yii::$app->user->id;
        $this->model->status = TenderEnum::STATUS_DELETED;
        if ($this->model->save()) {
            return true;
        } else {
            $this->addErrors($this->model->errors);
            return false;
        }
    }
}