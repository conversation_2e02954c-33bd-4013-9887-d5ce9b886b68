<?php

namespace api\modules\shop\controllers;

use api\components\ApiController;
use api\modules\shop\filters\ClassifierCategoryFilter;
use api\modules\shop\filters\ClassifierFilter;
use common\behaviors\RoleAccessBehavior;
use Yii;

class ClassifierController extends ApiController
{
    public function behaviors()
    {
        $parent = parent::behaviors();
        $parent['accessByRole'] = [
            'class' => RoleAccessBehavior::class,
            'rules' => [
                'category' => ['user'],
                'classifier' => ['user'],
            ],
        ];
        return $parent;
    }

    public function actionCategory()
    {
        return $this->sendResponse(
            new ClassifierCategoryFilter(),
            Yii::$app->request->queryParams
        );
    }
    public function actionClassifier()
    {
        return $this->sendResponse(
            new ClassifierFilter(),
            Yii::$app->request->queryParams
        );
    }
}
