# Framework
# ---------
YII_DEBUG=true
YII_ENV=dev
APP_MAINTENANCE=0

# Application
# -----------
LINK_ASSETS=true

# Databases
# ---------
DB_DSN=mysql:host=db;port=3306;dbname=yii2-starter-kit
DB_USERNAME=ysk_dbu
DB_PASSWORD=ysk_pass
DB_TABLE_PREFIX=
#DB_CHARSET=utf8mb4

TEST_DB_DSN=mysql:host=db;port=3306;dbname=yii2-starter-kit-test
TEST_DB_USERNAME=root
TEST_DB_PASSWORD=root

# Urls
# ----
API_HOST_INFO=http://api.yii2-starter-kit.localhost
FRONTEND_HOST_INFO=http://yii2-starter-kit.localhost
BACKEND_HOST_INFO=http://backend.yii2-starter-kit.localhost
STORAGE_HOST_INFO=http://storage.yii2-starter-kit.localhost

# Single domain example
# ----
#FRONTEND_HOST_INFO=http://yii2-starter-kit.localhost
#FRONTEND_BASE_URL=/
#BACKEND_HOST_INFO=http://yii2-starter-kit.localhost
#BACKEND_BASE_URL=/backend/web
#STORAGE_HOST_INFO=http://yii2-starter-kit.localhost
#STORAGE_BASE_URL=/storage/web


# Other
# -----
SMTP_HOST=mailcatcher
SMTP_PORT=1025

FRONTEND_COOKIE_VALIDATION_KEY=<generated_key>
BACKEND_COOKIE_VALIDATION_KEY=<generated_key>

ADMIN_EMAIL=<EMAIL>
ROBOT_EMAIL=<EMAIL>

GITHUB_CLIENT_ID=your-client-id
GITHUB_CLIENT_SECRET=your-client-secret

GLIDE_SIGN_KEY=<generated_key>
GLIDE_MAX_IMAGE_SIZE=4000000

# To resolve "Invalid volumes",For Toolbox. (https://github.com/docker/toolbox/issues/607)
COMPOSE_CONVERT_WINDOWS_PATHS=1

# Config for Docker for Mac
#XDEBUG_CONFIG = "remote_connect_back=0 remote_host=host.docker.internal"

COMMISSION_PERCENT=0.0015
SHOP_ZALOG_PERSENT=0.03
SHOP_CUSTOMER_MAX_COMMISSION_SUM=1000000
SHOP_DEMPING_PERSENT=80
LOT_OPERATOR_NUMBER=5
LOT_CUSTOMER_TYPE=2

TENDER_CUSTOMER_MAX_COMMISSION_SUM=1000000
TENDER_SUPPLIER_MAX_COMMISSION_SUM=4000000

SELECTION_MAX_ZALOG_PERCET=3
TENDER_MAX_ZALOG_PERCET=5

TENDER_MIN_DAYS=12
TENDER_MAX_DAYS=30
TENDER_DIFFERENCIAL_PERCENT=0.85# 15 % for differencial organizations