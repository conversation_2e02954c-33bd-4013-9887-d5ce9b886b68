<?php


namespace api\modules\shop\forms;


use api\components\BaseModel;
use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\shop\resources\OrderRequestResource;
use api\modules\shop\resources\OrderResource;
use api\modules\shop\resources\ProductResource;
use common\enums\CompanyEnum;
use common\enums\ProductEnum;
use common\enums\ShopEnum;
use common\models\Company;
use common\models\CompanyBalance;
use common\models\TenderModeratorLog;
use Yii;
use yii\base\Exception;
use function foo\func;

class AcceptDmbatForm extends BaseModel
{

    public OrderResource $model;
    public $pkcs7;

    public function __construct(OrderResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }
    public function rules()
    {
        $parent =parent::rules();
        $child =  [

        ];
        return  array_merge($parent,$child);
    }

    /**
     * @throws \yii\db\Exception
     * @throws Exception
     */
    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        $this->model->status = ShopEnum::ORDER_STATUS_ACTIVE;
        if ($this->model->save()){
            $this->model->updateAttributes([
                'request_end'=>date("Y-m-d H:i:s", strtotime("+30 minutes", strtotime($this->model->created_at))),
            ]);
            $transaction->commit();
            return true;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}