<?php

namespace api\modules\auth\forms;

use api\components\BaseRequest;
use api\modules\auth\resources\CompanyProfileResource;
use api\modules\auth\resources\UserResource;
use common\enums\RoleEnum;
use common\enums\StatusEnum;
use common\enums\TenderEnum;
use common\enums\UserEnum;
use common\models\BlackList;
use common\models\CommissionMember;
use common\models\Company;
use Exception;
use Yii;

class SetActiveProfileForm extends BaseRequest
{
    public $profileId;
    public $organ;

    public function rules()
    {
        return [
            [['profileId', 'organ'], 'required', 'message' => t('{attribute} yuborish majburiy')],
        ];
    }

    public function getResult()
    {
        $user = Yii::$app->user->identity;
        $company = $user->company;
        if ($company->organization_type == Company::BUDJET) {
            $companyProfile = CompanyProfileResource::findOne(['id' => $this->profileId, 'organ' => $this->organ, 'company_id' => $company->id]);
            if ($companyProfile) {
                $companyProfile->live = 1;
                $companyProfile->save();
                CompanyProfileResource::updateAll(
                    ['live' => 0],
                    ['and', ['company_id' => $company->id], ['!=', 'id', $companyProfile->id], ['status' => StatusEnum::STATUS_ACTIVE]]
                );
            }
        }

        return true;
    }

}
