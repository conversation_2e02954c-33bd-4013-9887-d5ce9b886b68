<?php

namespace api\modules\tender\resources;

use common\models\CommissionMember;
use common\models\query\NotDeletedFromCompanyQuery;

class CommissionMemberShortResource extends CommissionMember {

    public function fields (){
        return ['id', 'fullname', 'pinfl', 'position'];
    }

    public function extraFields() {
        return [
            'commissionGroupMembers',
            'tenderCommissionMembers',
        ];
    }

    public function getCommissionGroupMembers (){
        return $this->hasMany(CommissionGroupMemberResource::class, ['commission_member_id' => 'id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}