<?php

namespace backend\controllers;

use backend\services\ContractExcelService;
use Yii;
use common\models\Contract;
use common\models\ContractSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * ContractController implements the CRUD actions for Contract model.
 */
class ContractController extends BackendController
{
    /**
     * Lists all Contract models.
     * @return mixed
     */
    public function actionIndex()
    {

        $year = Yii::$app->request->get('year') ? Yii::$app->request->get('year') : null;
        $month = Yii::$app->request->get('month') ? Yii::$app->request->get('month') : null;

        $searchModel = new ContractSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, $year, $month);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
            'year' => $year,
            'month' => $month,
        ]);
    }

    /**
     * Displays a single Contract model.
     * @param int $id ID
     * @return mixed
     */
    public function actionExcel($year = null, $month = null)
    {
        /**
         * @var $contractExcelService ContractExcelService
         */
        $contractExcelService = Yii::$container->get(ContractExcelService::class);

        $path = $contractExcelService->contractExcel($year, $month);


        return Yii::$app->response->sendFile($path);
    }

    /**
     * Displays a single Contract model.
     * @param int $id ID
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }
    /**
     * Finds the Contract model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id ID
     * @return Contract the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Contract::findOne($id)) !== null) {
            return $model;
        }
        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
