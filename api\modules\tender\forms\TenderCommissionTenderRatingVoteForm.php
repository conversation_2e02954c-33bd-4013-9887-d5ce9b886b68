<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderQualificationSelectionResource;
use api\modules\tender\resources\TenderRequestRatingCommissionResource;
use api\modules\tender\resources\TenderRequestRatingResource;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;
use Yii;
use yii\web\ForbiddenHttpException;

class TenderCommissionTenderRatingVoteForm extends BaseRequest
{
    public $vote;
    public $description;
    public TenderResource $model;

    public function rules()
    {
        return [
            ['vote', 'required', 'message' => t('{attribute} yuborish majburiy')],
            ['description', 'required', 'when' => function ($model) {
                return ($model->vote == TenderEnum::VOTE_NO);
            }],
            [['vote'], 'integer'],
            [['description'], 'string', "max" => 512],
            ['vote', 'checkVote'],
        ];
    }


    public function checkVote()
    {
        if ($this->vote == TenderEnum::VOTE_YES || $this->vote == TenderEnum::VOTE_NO || $this->vote == TenderEnum::VOTE_NEUTRAL) {
            return true;
        } else {
            $this->addError("vote", t("Ovoz berish qiymatlari 1 yoki 0, -1 bo'lishi kerak"));
            return false;
        }
    }

    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function getResult()
    {
        if (!in_array($this->model->state, [TenderEnum::STATE_READY_TO_VOTE, TenderEnum::STATE_READY_TO_MAKE_PROTOCOL])) {
            $this->addError("vote", t("Baholash bosqichida emas"));

            return false;
        }
        $commissionId = \Yii::$app->user->identity->commissionMemberId;

        $tenderComm = $this->model->getCommissionMember($commissionId);
        if (!$tenderComm) {
            $this->addError("vote", t("Komissiya azosi tenderga a'zo emas"));

            return false;
        }
        if (!in_array($tenderComm->role, [TenderEnum::ROLE_COMMISSION, TenderEnum::ROLE_CHAIRMAN])) {

            return new ForbiddenHttpException(t("Komisiya roli uchun ruxsat mavjud"));
        }

        $md = TenderRequestRatingCommissionResource::find()
            ->notDeleted()
            ->andWhere(['tender_id' => $this->model->id])
            ->andWhere(['tender_commission_member_id' => $tenderComm->id])
            ->andWhere(['commission_member_id' => $commissionId])
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->one();

        if (!$md) {
            $md = new TenderRequestRatingCommissionResource();
            $md->tender_id = $this->model->id;
            $md->tender_commission_member_id = $tenderComm->id;
            $md->commission_member_id = $commissionId;
            $md->status = TenderEnum::STATUS_ACTIVE;
        }

        $transaction = \Yii::$app->db->beginTransaction();

        $md->description = $this->description;
        $md->vote = $this->vote;

        if (!$md->save()) {
            $transaction->rollBack();
            $this->addErrors($md->errors);
            return false;
        }

        $tenderCommCount = $this->model->getTenderCommissionMembersCount();
        $ratingCount = TenderRequestRatingCommissionResource::find()
            ->notDeleted()
            ->andWhere(['tender_id' => $this->model->id])
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->count();
        $ratingCount = (int)$ratingCount;

        if ((ceil(($tenderCommCount - 1) * 2 / 3)) <= $ratingCount) {

            $sqlPlus = " SELECT COUNT(id) as countPlus FROM tender_request_rating_commission where tender_id=" . $this->model->id . " and status = 300 and vote = 1";
            $countPlus = Yii::$app->db->createCommand($sqlPlus)->queryAll();
            $countPlus = (int)$countPlus[0]['countPlus'];

            $sqlMinus = " SELECT COUNT(id) as countMinus FROM tender_request_rating_commission where tender_id=" . $this->model->id . " and status = 300 and vote = -1 ";
            $countMinus = Yii::$app->db->createCommand($sqlMinus)->queryAll();
            $countMinus = isset($countMinus[0]) && isset($countMinus[0]['countMinus']) ? (int)$countMinus[0]['countMinus'] : 0;

            if ($countPlus > $countMinus) {
                $this->model->state = TenderEnum::STATE_READY_TO_MAKE_PROTOCOL;
                if (!$this->model->save()) {
                    $transaction->rollBack();
                    $this->addErrors($this->model->errors);
                    return false;
                }

            } else {
                $userId = Yii::$app->user->id;
                $this->model->state = TenderEnum::STATE_READY_TO_RATING;
                if ($this->model->save()) {
                    TenderRequestRatingCommissionResource::updateAll([
                        'status' => TenderEnum::STATUS_NOT_ACTIVE,
                        'updated_by' => $userId,
                        'updated_at' => date("Y-m-d H:i:s")
                    ], [
                        'tender_id' => $this->model->id,
                        'status' => TenderEnum::STATUS_ACTIVE
                    ]);
                    TenderQualificationSelectionResource::updateAll([
                        'state' => TenderEnum::QUALIFIER_STATE_NEW,
                        'updated_by' => $userId,
                        'updated_at' => date("Y-m-d H:i:s")
                    ], [
                        'tender_id' => $this->model->id,
                        'status' => TenderEnum::STATUS_ACTIVE
                    ]);

                    TenderRequestRatingResource::updateAll([
                        'status' => TenderEnum::STATUS_NOT_ACTIVE,
                        'updated_by' => $userId,
                        'updated_at' => date("Y-m-d H:i:s")
                    ], [
                        'tender_id' => $this->model->id,
                        'status' => TenderEnum::STATUS_ACTIVE
                    ]);

                } else {
                    $transaction->rollBack();
                    $this->addErrors($this->model->errors);
                    return false;
                }
            }
        }

        $transaction->commit();
        return true;

    }
}