<?php

namespace api\modules\common\forms;

use api\components\BaseRequest;
use api\modules\common\resources\CompanyBalanceResource;
use common\enums\CompanyTransactionEnum;
use common\models\Bank;
use common\models\bank\BankWithDrawMoney;
use common\models\CompanyBankAccount;
use common\models\CompanyTransaction;
use Yii;

class BalanceWithdrawToAccountForm extends BaseRequest
{
    public $company_account_id;
    public $purpose;
    public $amount;

    public function rules()
    {
        return [
            [['purpose', 'amount', 'company_account_id'], 'required', 'message' => t('{attribute} yuborish majburiy')],
        ];
    }

    public function getResult()
    {
        $companyId = \Yii::$app->user->identity->company_id;
        $company_balance = CompanyBalanceResource::findOne(['company_id' => $companyId]);

        if ($company_balance) {
            $company_balance->calculateBalance();
        } else {
            $this->addError('balance', t("Balans bilan ishlash uchun siz bank hisobingizni ko'rsatishingiz kerak."));
            return false;
        }
        $companyAccount = CompanyBankAccount::findOne($this->company_account_id);
        if (!$companyAccount) {
            $this->addError('company_account_id', t("Hisob raqam sizga tegishli emas"));
            return false;
        }
        if ($company_balance->available >= $this->amount) {
            $transaction = Yii::$app->db->beginTransaction();
            $model = new BankWithDrawMoney();
            $model->amount = $this->amount * 100;
            $model->company_account_id = $this->company_account_id;
            $model->purpose = $this->purpose;
            $model->company_id = \Yii::$app->user->identity->company_id;
            $model->company_tin = \Yii::$app->user->identity->company->tin;
            $model->status = BankWithDrawMoney::STATUS_NEW;

            if (!$model->save()) {
                $this->addErrors($model->getErrors());
                $transaction->rollBack();
                return false;
            }

            $company_transaction2 = new CompanyTransaction([
                'company_id' => $companyId,
                'amount' => $this->amount * 100,
                'type' => CompanyTransactionEnum::TYPE_WITHDRAW,
//                'auction_id' => $model->id,
                'description' => t("Erkin balansdan pul yechildi"),
                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                'transaction_date' => date("Y-m-d H:i:s"),
            ]);

            if (!$company_transaction2->save()) {
                $this->addError("amount", t("Erkin balansdan pul yechishda xatolik"));
                $transaction->rollBack();
                return false;
            }
            $transaction->commit();

            return true;
        } else {
            $this->addError('amount', t("Hisobda yetarli mablag' mavjud emas"));
            return false;
        }


    }
}