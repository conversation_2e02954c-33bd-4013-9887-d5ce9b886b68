<?php

namespace api\modules\tender\forms;

use api\components\BaseRequest;
use api\modules\auth\resources\UserResource;
use api\modules\tender\resources\CommissionMemberResource;
use backend\modules\rbac\models\RbacAuthAssignment;
use common\enums\RoleEnum;
use common\enums\StatusEnum;
use common\enums\TenderEnum;
use common\enums\UserEnum;
use common\models\User;
use Yii;
use yii\base\Exception;

class CommissionMemberForm extends BaseRequest
{

    //public $tin;
    public $pinfl;
    public $fullname;
    public $passport_serie;
    public $passport_number;
    public $mail;
    public $phone;
    public $birthday;
    public $position;
    public $company_name;

    public CommissionMemberResource $model;

    public function __construct(CommissionMemberResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [['pinfl', 'fullname', 'passport_serie', 'passport_number', 'mail', 'phone', 'birthday', 'position', 'company_name'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['fullname', 'mail', 'phone', 'birthday', 'position', 'company_name'], 'safe'],
            ['mail', 'email', 'message' => "The email isn't correct"],
            ['pinfl', 'isPinfl'],
            ['passport_number', 'isPassportNumber'],
            ['pinfl', 'string', 'length' => 14],
            ['passport_serie', 'string', 'min' => 2, 'max' => 2, 'message' => t("{attribute} uzunligi 2 ta belgi bo'lishi kerak"), 'tooLong' => t("{attribute} uzunligi 2 ta belgi bo'lishi kerak"), 'tooShort' => t("{attribute} uzunligi 2 ta belgi bo'lishi kerak")],
            ['passport_number', 'string', 'length' => 7, 'message' => t("{attribute} uzunligi 7 ta butun sondan iborat bo'lishi kerak")]
        ];
    }

    public function attributeLabels()
    {
        return [
            'id' => Yii::t('main', 'ID'),
            'company_id' => Yii::t('main', 'Company ID'),
            'status' => Yii::t('main', 'Status'),
            'pinfl' => Yii::t('main', 'JSHSHIR'),
            'fullname' => Yii::t('main', 'F.I.SH'),
            'passport_serie' => Yii::t('main', 'Pasport seriyasi'),
            'passport_number' => Yii::t('main', 'Pasport raqami'),
            'mail' => Yii::t('main', 'Elektron manzil'),
            'phone' => Yii::t('main', 'Telefon'),
            'birthday' => Yii::t('main', 'Tug’ilgan yili'),
            'position' => Yii::t('main', 'Lavozim'),
            'company_name' => Yii::t('main', 'Kompaniya'),
        ];
    }

    public function isPassportNumber($attribute)
    {
        if (!preg_match('/^[0-9]{7}$/', $this->$attribute)) {
            $this->addError($attribute, t("Pasport nomeri raqamlari 7 ta butun sondan iborat bo'lishi kerak"));
            return false;
        }
        return true;
    }

    public function isPinfl()
    {
        if (!preg_match('/^[0-9]{14}$/', $this->pinfl)) {
            $this->addError('pinfl', t("JSHSHIR uzunligi 14 ta butun sondan iborat bo'lishi kerak"));
            return false;
        } else {
            return true;
        }
    }

    /**
     * @throws \yii\db\Exception
     * @throws Exception
     */
    public function getResult()
    {
        $check = UserResource::find()
            ->where(['username' => $this->pinfl])
            ->one();
        if ($check) {
            throw new Exception(t("Foydalanuvchi avval ro'yxatdan o'tgan"));
        }

        $transaction = Yii::$app->db->beginTransaction();
        $this->model->pinfl = $this->pinfl;
        $this->model->fullname = $this->fullname;
        $this->model->passport_serie = $this->passport_serie;
        $this->model->passport_number = $this->passport_number;
        $this->model->mail = $this->mail;
        $this->model->phone = $this->phone;
        $this->model->birthday = date("Y-m-d", strtotime($this->birthday));
        $this->model->position = $this->position;
        $this->model->company_name = $this->company_name;
        $this->model->status = TenderEnum::STATUS_ACTIVE;
        $this->model->company_id = Yii::$app->user->identity->company_id;

        if ($this->model->save()) {
            $user = new UserResource();
            $user->username = $this->pinfl;
            $user->email = $this->mail;
            $user->generateAuthKey();
            $user->setPassword($this->pinfl);
            $user->status = UserEnum::STATUS_ACTIVE;
            $user->company_id = $this->model->company_id;

            if (!$user->save()) {
                $transaction->rollBack();
                throw new Exception(t("Foydalanuvchi saqlashda xatolik"));
            }

            $role = new RbacAuthAssignment();
            $role->item_name = RoleEnum::ROLE_COMMISSION;
            $role->user_id = (string)$user->id;
            $role->created_at = time();
            if (!$role->save()) {
                $transaction->rollBack();
                throw new Exception(t("Foydalanuvchi ro'lini saqlashda xatolik"));
            }

            $this->model->user_id = $user->id;
            $this->model->save(false);
            $transaction->commit();
            return true;
        } else {
            $transaction->rollBack();
            throw new Exception(t("Komisiya a'zosini saqlashda xatolik"));
        }
    }
}