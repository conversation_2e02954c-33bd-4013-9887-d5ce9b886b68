<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderRealizedResource;
use common\enums\TenderEnum;

class TenderRealizedFilter extends BaseRequest
{

    public $lot;

    public function __construct($params = [])
    {
        parent::__construct($params);
    }

    public function rules()
    {
        return [
            ['lot', 'number']
        ];
    }

    public function getResult()
    {
        $model = TenderRealizedResource::find()->notDeletedAndFromCompany();
        $model->andWhere(['in',
            'state',
            [
                TenderEnum::STATE_MADE_PROTOCOL,
                TenderEnum::STATE_MADE_DISCUSSION_PROTOCOL,
                TenderEnum::STATE_DISCUSSION_END_MADE_PROTOCOL,
                TenderEnum::STATE_SEND_CONTRACT,
                TenderEnum::STATE_REJECT_WINNER,
                TenderEnum::STATE_SEND_CONTRACT_SECOND_WINNER,
                TenderEnum::STATE_REJECT_CONTRACT_SECOND_WINNER,
                TenderEnum::STATE_ACCEPT_CONTRACT,
                TenderEnum::STATE_RECEIVE_PRODUCT,
            ]]);
        if ($this->lot && $this->lot > 0) {
            $model->andWhere(['lot' => $this->lot]);
        }
        $model->orderBy(['updated_at' => SORT_DESC]);
        return paginate($model);

    }
}