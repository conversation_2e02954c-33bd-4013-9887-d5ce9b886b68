body {
    font-size: 11px;
}
.contract-header, .section-title {
    text-align: center;
    font-weight: bold;
}
table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 20px;
}
table, th, td {
    border: 1px solid #000;
}
th, td {
    padding: 8px;
    text-align: center;
}
.no-border {
    border: none;
}
.highlight {
    font-weight: bold;
}

.no-inner-border {
    border: 1px solid black; /* Tashqi chiziqlar */
    border-collapse: collapse; /* Chegaralarni birlashtiradi */
}

.no-inner-border td {
    border: none; /* Ichki kataklarning chiziqlarini olib tashlaydi */
    padding: 5px; /* Ichki masofa */
}
.custom-table {
    border: 1px solid black; /* Tashqi chiziqlar */
    border-collapse: collapse;
}

.custom-table td {
    border: 1px solid black; /* Ichki chiziqlar */
    padding: 5px;
}

/* Aniq ichki chiziqni yo'q qilish */
.custom-table tr:nth-child(1) td:nth-child(2) {
    border-left: none; /* Chap tarafdagi chiziqni o'chiradi */
}
.table-container {
    width: 100%;
    overflow: hidden; /* Float muammosini hal qilish */
}
.table-left, .table-right {
    width: 48%; /* Yonma-yon turishi uchun */
    float: left; /* Chap va o\'ngga joylashtirish */
    margin-right: 2%;
}
.table-right {
    margin-right: 0;
}
table {
    border-collapse: collapse;
    width: 100%;
}
table, th, td {
    border: 1px solid black;
}
th, td {
    padding: 8px;
    text-align: center;
}