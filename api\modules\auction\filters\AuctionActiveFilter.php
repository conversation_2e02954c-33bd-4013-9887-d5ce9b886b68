<?php

namespace api\modules\auction\filters;

use api\components\BaseRequest;
use api\modules\auction\resources\AuctionActiveResource;
use api\modules\auction\resources\AuctionResource;
use common\enums\AuctionEnum;
use Yii;

class AuctionActiveFilter extends BaseRequest
{
    public $lot;
    public $regionId;
    public $districtId;
    public $tinOrPinfl;
    public $classifierCategoryId;
    public $classifierId;
    public $fromDate;
    public $toDate;
    public $fromPrice;
    public $toPrice;
    public $companyType;

  public function rules()
  {
      // rules regarding all props from /common/migrations/db/m231115_193117_create_auction_table.php
      return [
          [['lot', 'regionId', 'districtId', 'classifierCategoryId', 'classifierId'], 'integer'],
          [['fromPrice', 'toPrice'], 'number'],
          [['fromDate', 'toDate'], 'safe']
      ];
  }


  public function getResult()
  {
      $query = AuctionActiveResource::find()->joinWith('company')->where(['auction.status' => AuctionEnum::STATUS_ACTIVE]);
      if($this->lot){
        $query->andWhere(['auction.lot' => $this->lot]);
      }


     if ($this->fromPrice) {
       $query->andWhere(['>=', 'auction.total_sum', $this->fromPrice]);
     }

     if ($this->toPrice) {
       $query->andWhere(['<=', 'auction.total_sum', $this->toPrice]);
     }

     if ($this->regionId) {
       $query->andWhere(['company.region_id' => $this->regionId]);
     }

     if ($this->districtId) {
       $query->andWhere(['company.district_id' => $this->districtId]);
     }

     if ($this->tinOrPinfl) {
       $query->andWhere([
           'or',
           ['company.tin' => $this->tinOrPinfl],
           ['company.pinfl' => $this->tinOrPinfl]
       ]);
     }

     if ($this->classifierCategoryId) {
       $query->andWhere(['auction.classifier_category_id' => $this->classifierCategoryId]);
     }

      if ($this->classifierId) {
          $query->leftJoin('auction_classifier','`auction`.`id` = `auction_classifier`.`auction_id`');
          $query->andWhere(['auction_classifier.classifier_id' => $this->classifierId]);
      }
     if ($this->fromDate) {
       $query->andWhere(['>=', 'DATE(auction.auction_end)', date("Y-m-d H:i:s", strtotime($this->fromDate))]);
     }

     if ($this->toDate) {
       $query->andWhere(['<=', 'DATE(auction.auction_end)', date("Y-m-d H:i:s", strtotime($this->toDate))]);
     }

    $query = $query->orderBy("auction.auction_end asc");
    return paginate($query);
  }
}
