<?php


namespace api\modules\tender\resources;


use api\modules\common\resources\UnitResource;
use common\models\TenderRequirements;

class TenderRequirementsActiveLotsResource extends TenderRequirements
{
    public function fields()
    {
        return [
            'id', 'type', 'evaluation_method', 'obligation', 'file_name',
            'max_ball', 'min_ball', 'comparative_weight', 'title', 'description',
            'file_uploading_necessary', 'file_is_required', 'value', 'value_condition', 'value_from', 'value_to', 'unit' => function ($model) {
                return UnitResource::findOne($model->unit);
            }
        ];
    }

}