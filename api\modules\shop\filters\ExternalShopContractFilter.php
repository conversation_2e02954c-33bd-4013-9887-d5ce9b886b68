<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\shop\resources\ContractResource;
use common\enums\ContractEnum;
use common\models\Contract;
use Yii;
use yii\db\Expression;

class ExternalShopContractFilter extends BaseRequest
{
    public $type;

    public function rules()
    {
        return [
            [['type'], 'required', 'message' => t('{attribute} yuborish majburiy')],
        ];
    }

    public function getResult()
    {
        $model = Contract::find()
            ->select([
                'order.lot_number',
                'contract.id',
                'contract.price',
                'contract.created_at',
                'classifier.title_uz as product_name',
                'product.title as product_title',
                'product.description',
                'customer.title as customer_title',
                'customer.tin as customer_tin',
                'producer.tin as producer_tin',
                'order.count',
                'region_customer.title_uz as customer_region_title',
                'producer.title as producer_title',
                'region_producer.title_uz as producer_region_title',
                'contract.status',
                new Expression('COUNT(order_request.id) AS order_request_count'),
                new Expression("
            CASE 
                WHEN contract.customer_pay_date IS NULL THEN 'Не оплачен'
                ELSE 'Оплачен'
            END AS payment_status
        "),
            ])
            ->leftJoin('order', 'order.id=contract.order_id')
            ->leftJoin('order_request', 'order_request.order_id=order.id')
            ->leftJoin('product', 'product.id=order.product_id')
            ->leftJoin('classifier', 'classifier.id=product.classifier_id')
            ->leftJoin('company customer', 'customer.id=contract.customer_id')
            ->leftJoin('region region_customer', 'region_customer.id=customer.region_id')
            ->leftJoin('company producer', 'producer.id=contract.producer_id')
            ->leftJoin('region region_producer', 'region_producer.id=producer.region_id');
        $model->andWhere('contract.order_id is not null');
        $model->andWhere(['product.platform_display' => $this->type]);
        $model->orderBy(['created_at' => SORT_DESC]);
        $model->groupBy([
            'order.lot_number',
            'contract.id',
            'contract.price',
            'contract.created_at',
            'product_name',
            'product_title',
            'product.description',
            'customer_title',
            'customer_tin',
            'producer_tin',
            'order.count',
            'customer_region_title',
            'producer_title',
            'producer_region_title',
            'contract.status',
        ])->asArray();
        return paginate($model);
    }
}
