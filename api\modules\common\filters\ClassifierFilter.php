<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierResource;
use common\enums\TenderEnum;

class ClassifierFilter extends BaseRequest
{
    public $title;
    public $categoryId;
    public $type;

    public function rules()
    {
        return [
            [['title'], 'safe'],
            [['categoryId', 'type'], 'integer'],
        ];
    }

    public function getResult()
    {
        $model = ClassifierResource::find();

        if ($this->type) {
            $model->andWhere(['type' => $this->type]);
        }

        if ($this->title) {
            $model->andWhere([
                'or',
                ['like', 'title_ru', $this->title],
                ['like', 'title_uz', $this->title],
                ['like', 'title_uzk', $this->title],
                ['like', 'code', $this->title],
            ]);
        }

        if ($this->categoryId) {
            $model->andWhere(['classifier_category_id' => $this->categoryId]);
        }

        return $model->all();
    }
}
