<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%product_draft}}`.
 */
class m250717_082646_create_product_draft_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%product_draft}}', [
            'id' => $this->primaryKey(),

            'classifier_category_id' => $this->integer(),
            'classifier_id' => $this->integer(),
            'company_id' => $this->integer(),
            'account_number' => $this->string(255),
            'organ' => $this->string(11),
            'title' => $this->string(),
            'brand_title' => $this->string(),
            'description' => $this->text(),

            'year' => $this->integer(),
            'quantity' => $this->integer(),
            'unit_id' => $this->integer(),
            'price' => $this->decimal(20,2)->unsigned(),
            'min_order' => $this->integer(),
            'max_order' => $this->integer(),
            'type' => $this->string(255),
            'country_id' => $this->integer(),
            'unit_price' => $this->string(255),
            'made_in' => $this->string(255),
            'platform_display' => $this->string(255),
            'state' => $this->integer(),
            'delivery_period' => $this->integer(),
            'delivery_period_type' => $this->integer(),
            'warranty_period' => $this->integer(),
            'warranty_period_type' => $this->integer(),
            'expiry_period' => $this->integer(),
            'expiry_period_type' => $this->integer(),

            'status' => $this->integer(),
            'active_date' => $this->dateTime(),
            'inactive_date' => $this->dateTime(),
            'is_have_license' => $this->boolean(),

            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),

        ]);

        $this->createIndex("idx_product_draft_classifier_category_id", "product_draft", "classifier_category_id");
        $this->createIndex("idx_product_draft_classifier_id", "product_draft", "classifier_id");
        $this->createIndex("idx_product_draft_company_id", "product_draft", "company_id");
        $this->createIndex("idx_product_draft_unit_id", "product_draft", "unit_id");

        $this->addForeignKey("fk_product_draft_classifier_category_id", "product_draft", "classifier_category_id", "classifier_category", "id", "cascade", "cascade");
        $this->addForeignKey("fk_product_draft_classifier_id", "product_draft", "classifier_id", "classifier", "id", "cascade", "cascade");
        $this->addForeignKey("fk_product_draft_company_id", "product_draft", "company_id", "company", "id", "cascade", "cascade");
        $this->addForeignKey("fk_product_draft_unit_id", "product_draft", "unit_id", "unit", "id", "cascade", "cascade");

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropForeignKey("fk_product_draft_classifier_category_id", "product_draft");
        $this->dropForeignKey("fk_product_draft_classifier_id", "product_draft");
        $this->dropForeignKey("fk_product_draft_company_id", "product_draft");
        $this->dropForeignKey("fk_product_draft_unit_id", "product_draft");

        $this->dropIndex("idx_product_draft_classifier_category_id", "product_draft");
        $this->dropIndex("idx_product_draft_classifier_id", "product_draft");
        $this->dropIndex("idx_product_draft_company_id", "product_draft");
        $this->dropIndex("idx_product_draft_unit_id", "product_draft");

        $this->dropTable('{{%product_draft}}');
    }
}
