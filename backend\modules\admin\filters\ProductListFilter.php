<?php
namespace backend\modules\admin\filters;

use api\components\BaseRequest;
use backend\modules\admin\resources\ProductResource;
use common\enums\ProductEnum;

class ProductListFilter extends BaseRequest
{
    public $platform_display;

    public function rules()
    {
        return [
            [['platform_display'], 'string', 'max' => 15],
            [['platform_display'], 'in', 'range' => [
                ProductEnum::PLATFORM_DISPLAY_E_SHOP,
                ProductEnum::PLATFORM_DISPLAY_NATIONAL
            ]],
        ];
    }


    public function __construct($platform_display, $config = [])
    {
        $this->platform_display = $platform_display;
        parent::__construct($config);
    }

    public function getResult()
    {
        $model = ProductResource::find()->where(['platform_display' => $this->platform_display]);
        $model->orderBy(['id'=>SORT_DESC]);
        return paginate($model);

    }
}