<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\EconomicActivitiesTypeResource;

class EconomicActivitiesTypeFilter extends BaseRequest
{
    public $title;

    public function rules()
    {
        return [
            [['title'], 'safe'],
        ];
    }

    public function getResult()
    {
        $query = EconomicActivitiesTypeResource::find();

        if ($this->title) {
            $query->where(['or',
                ['like', 'title_ru', $this->title],
                ['like', 'title_uz', $this->title],
                ['like', 'title_uzk', $this->title],
                ['like', 'code', $this->title]
            ]);
        }
        return $query->all();
    }
}
