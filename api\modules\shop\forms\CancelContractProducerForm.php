<?php


namespace api\modules\shop\forms;


use Yii;
use api\components\BaseRequest;
use api\modules\shop\resources\ContractResource;
use common\enums\CompanyTransactionEnum;
use common\enums\ContractEnum;
use common\models\CompanyTransaction;
use common\models\ContractRequest;
use common\models\shop\Order;
use yii\httpclient\Exception;

class CancelContractProducerForm extends BaseRequest
{
    public $customer_id;
    public $producer_id;
    public ContractResource $model;

    public $pkcs7;
    public $id;


    public function rules()
    {
        return [
            [['id','pkcs7'], 'required']
        ];
    }

    public function getResult()
    {
        $this->model = ContractResource::findOrFail($this->id);
        $user = Yii::$app->user->identity;
        $company_id = $user->company_id;

        if ($this->model->producer_id == $company_id) {
            $this->producer_id = $company_id;
        } else {
            $this->addError("error", t("Shartnomadan bosh tortish uchun yetkazib beruvchi bo'lishingiz kerak"));
            return false;
        }
        if($this->id != $this->model->id){
            $this->addError("error", t("Imzolangan ma'lumot mos emas"));
            return false;
        }
        /**
         * @var $order Order
         */
        $order = $this->model->order;
        if (!$order) {
            throw new Exception(t("Order topilmadi"));
        }

        if (!in_array($this->model->status, [ContractEnum::STATUS_SIGNED])) {
            $this->addError("error", t("Shartnomadan bosh tortish uchun muqobil holatda emas"));
            return false;
        }

        $date = date("Y-m-d H:i:s");
        $transaction = \Yii::$app->db->beginTransaction();

        $this->model->status = ContractEnum::STATUS_DISCARD;
        $this->model->producer_cancel_date = date("Y-m-d H:i:s");

        if ($this->model->attributes && $this->model->validate() && $this->model->save()) {


            $reserveOffer = $order->reserve;
            $currentOffer = $order->winner;
            if ($currentOffer->company_id == $this->producer_id) {


                $company_out_id = $this->model->producer_id;
                $company_in_id = $this->model->customer_id;

                $transactionOut = null;

                $transactionOutReverted = CompanyTransaction::find()->where(['order_id' => $order->id, 'type' => CompanyTransactionEnum::TYPE_ZALOG, 'status' => CompanyTransactionEnum::STATUS_SUCCESS, 'reverted_id' => null])
                    ->andWhere(['in', 'company_id', [$company_out_id]])->all();

                foreach ($transactionOutReverted as $company_transaction1) {
                    $revert = new CompanyTransaction([
                        'company_id' => $company_transaction1->company_id,
                        'contract_id' => $this->model->id,
                        'order_id' => $company_transaction1->order_id,
                        'amount' => $company_transaction1->amount,
                        'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
                        'description' => Yii::t("main", "Garov bandlashdan chiqarildi."),
                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                        'transaction_date' => $date,
                    ]);
                    if ($revert->save()) {
                        $company_transaction1->reverted_id = $revert->id;
                        if (!$company_transaction1->save()) {
                            $transaction->rollBack();
                            $this->addErrors($company_transaction1->errors);
                            return false;
                        }
                    } else {
                        $transaction->rollBack();
                        $this->addErrors($revert->errors);
                        return false;
                    }
                    $transactionOut = $revert;
                }

                if ($transactionOut) {
                    $company_transaction_penalty_out = new CompanyTransaction([
                        'company_id' => $company_out_id,
                        'contract_id' => $this->model->id,
                        'order_id' => $this->model->order_id,
                        'amount' => $transactionOut->amount,
                        'type' => CompanyTransactionEnum::TYPE_PENALTY_OUT,
                        'description' => \Yii::t("main", "Shartnomadan bosh tortilgani uchun jarima to'ladi"),
                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                        'transaction_date' => $date,
                    ]);
//
                    if (!$company_transaction_penalty_out->save()) {
                        $this->addErrors($company_transaction_penalty_out->errors);
                        $transaction->rollBack();
                        return false;
                    }

                    $company_transaction_penalty_in = new CompanyTransaction([
                        'company_id' => $company_in_id,
                        'contract_id' => $this->model->id,
                        'order_id' => $this->model->order_id,
                        'amount' => $company_transaction_penalty_out->amount,
                        'type' => CompanyTransactionEnum::TYPE_PENALTY_IN,
                        'description' => \Yii::t("main", "Shartnomadan bosh tortilgani uchun yetkazib beruvchining garov summa(jarima)sini qabul qilib oldi"),
                        'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                        'transaction_date' => $date,
                    ]);
//
                    if (!$company_transaction_penalty_in->save()) {
                        $this->addErrors($company_transaction_penalty_in->errors);
                        $transaction->rollBack();
                        return false;
                    }
                }


                if ($reserveOffer) {
                    ContractRequest::createRequest($transaction, $order->id, $this->model->id, $reserveOffer->company_id);
                }

            }

            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }

}