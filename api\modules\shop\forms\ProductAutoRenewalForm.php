<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\shop\resources\ProductResource;
use common\models\WorkdayCalendar;
use yii\helpers\ArrayHelper;

class ProductAutoRenewalForm extends BaseRequest
{

    public ProductResource $model;
    public $auto_renewal;
    public $expiry_period;

    public function __construct(ProductResource $model, $params = [])
    {
        $this->model = $model;
        parent::__construct($params);
    }

    public function rules()
    {
        return [
            ['auto_renewal', 'boolean'],
            ['expiry_period', 'integer', 'min' => 1],
            [['auto_renewal', 'expiry_period'], 'required']
        ];
    }

    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();

        try {
            // auto_renewal ni true qilish
            $this->model->auto_renewal = $this->auto_renewal;

            if ($this->auto_renewal) {
                // active_date ni hozirgi vaqtga set qilish
                $this->model->active_date = date('Y-m-d H:i:s');

                // Dam olish kunlari va ish kunlarini olish
                $holidays = ArrayHelper::map(
                    WorkdayCalendar::findAll(['type' => WorkdayCalendar::HOLIDAY]),
                    'local_date',
                    'local_date'
                );
                $workDays = ArrayHelper::map(
                    WorkdayCalendar::findAll(['type' => WorkdayCalendar::WORKDAY]),
                    'local_date',
                    'local_date'
                );

                // expiry_period kunni dam olish kunlarini hisobga olmagan holatda qo'shish
                $this->model->inactive_date = addDaysExcludingWeekends(
                    $this->model->active_date,
                    $this->expiry_period,
                    $workDays,
                    $holidays
                );
            }

            if ($this->model->save()) {
                $transaction->commit();
                return true;
            } else {
                $transaction->rollBack();
                $this->addErrors($this->model->getErrors());
                return false;
            }

        } catch (\Exception $e) {
            $transaction->rollBack();
            $this->addError('error', $e->getMessage());
            return false;
        }
    }

}