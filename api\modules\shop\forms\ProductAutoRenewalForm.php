<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\shop\resources\ProductResource;
use common\models\WorkdayCalendar;
use yii\helpers\ArrayHelper;

class ProductAutoRenewalForm extends BaseRequest
{

    public ProductResource $model;
    public $auto_renewal;

    public function __construct(ProductResource $model, $params = [])
    {
        $this->model = $model;
        parent::__construct($params);
    }

    public function rules()
    {
        return [
            ['auto_renewal', 'boolean'],
//            ['auto_renewal', 'required']
        ];
    }

    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();

        try {
            $this->model->auto_renewal = $this->auto_renewal;
            if ($this->auto_renewal) {

                $this->model->active_date = $this->model->inactive_date;

                // Dam olish kunlari va ish kunlarini olish
                $holidays = ArrayHelper::map(
                    WorkdayCalendar::findAll(['type' => WorkdayCalendar::HOLIDAY]),
                    'local_date',
                    'local_date'
                );
                $workDays = ArrayHelper::map(
                    WorkdayCalendar::findAll(['type' => WorkdayCalendar::WORKDAY]),
                    'local_date',
                    'local_date'
                );

                // expiry_period kunni dam olish kunlarini hisobga olmagan holatda qo'shish
                $this->model->inactive_date = addDaysExcludingWeekends(
                    $this->model->active_date,
                    $this->model->expiry_period,
                    $workDays,
                    $holidays
                );
            }

            if ($this->model->save()) {
                $transaction->commit();
                return true;
            } else {
                $transaction->rollBack();
                $this->addErrors($this->model->getErrors());
                return false;
            }

        } catch (\Exception $e) {
            $transaction->rollBack();
            $this->addError('error', $e->getMessage());
            return false;
        }
    }

}