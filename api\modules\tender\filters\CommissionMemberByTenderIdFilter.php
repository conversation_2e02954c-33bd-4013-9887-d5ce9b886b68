<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\CommissionMemberResource;
use api\modules\tender\resources\CommissionMemberShortResource;
use common\enums\TenderEnum;

class CommissionMemberByTenderIdFilter extends BaseRequest {

    public $tenderId;
    public $status;

    public function rules (){
        return [
            [['tenderId'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['tenderId', 'status'], 'integer'],
        ];
    }

    public function getResult()
    {
        $query = CommissionMemberShortResource::find()->notDeletedAndFromCompany()
            ->join('inner join', 'tender_commission_member', 'tender_commission_member.commission_member_id=commission_member.id')
            ->where(['commission_member.status' => TenderEnum::STATUS_ACTIVE])
            ->andWhere(['tender_commission_member.tender_id' => $this->tenderId]);
        if(!$this->status){
            return $query->andWhere(['tender_commission_member.status' => TenderEnum::STATUS_ACTIVE])->all();
        }
         return $query->all();

    }
}