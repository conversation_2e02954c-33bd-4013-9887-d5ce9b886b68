<?php


namespace api\modules\shop\forms;


use api\components\BaseModel;
use api\components\BaseRequest;
use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\PlanScheduleResource;
use api\modules\shop\resources\OrderResource;
use common\enums\CompanyEnum;
use common\enums\CompanyTransactionEnum;
use common\enums\ProductEnum;
use common\enums\ShopEnum;
use common\enums\TenderEnum;
use common\models\Bmh;
use common\models\Classifier;
use common\models\Company;
use common\models\CompanyBalance;
use common\models\CompanyBankAccount;
use common\models\CompanyTransaction;
use common\models\PlanSchedule;
use common\models\shop\OrderList;
use common\models\shop\OrderRegion;
use common\models\shop\OrderRequest;
use common\models\shop\Product;
use common\models\shop\ProductFile;
use common\models\shop\ProductRegion;
use Yii;
use yii\base\Exception;
use yii\helpers\ArrayHelper;

class OrderUpdateForm extends BaseModel
{

    public OrderResource $model;

    public $product_id;
    public $classifier_id;
    public $type;
    public $plan_schedule_id;
    public $customer_account_treasury;
    public $expense_item;
    public $receiver_fio;
    public $receiver_phone;
    public $delivery_type;
    public $payment_type;
    public $shipping_sum;
    public $payment_status;
    public $payment_date;
    public $cancel_reason;
    public $cancel_date;
    public $shop_end;
    public $count;
    public $account_number_id;

    public $regions = [];
    public $pkcs7;

    public function __construct(OrderResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        $parent = parent::rules();
        $child = [
            [['product_id', 'classifier_id', 'plan_schedule_id',
                'count'
            ], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['regions', 'lot_number'], 'safe'],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],
            [['classifier_id'], 'exist', 'skipOnError' => true, 'targetClass' => ClassifierResource::class, 'targetAttribute' => ['classifier_id' => 'id']],
            [['plan_schedule_id'], 'exist', 'skipOnError' => true, 'targetClass' => PlanSchedule::class, 'targetAttribute' => ['plan_schedule_id' => 'id']],
            [['account_number_id'], 'exist', 'skipOnError' => true, 'targetClass' => CompanyBankAccount::class, 'targetAttribute' => ['account_number_id' => 'id']],

            [['account_number_id', 'expense_item'], 'required', 'when' => function ($model) {
                return \Yii::$app->user->identity->isBudget;
            }],
            [['account_number_id'], 'required', 'when' => function ($model) {
                return !\Yii::$app->user->identity->isBudget;
            }],
        ];
        return array_merge($parent, $child);
    }

    /**
     * @throws Exception
     * @throws \yii\db\Exception
     */
    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        $product = Product::findOne($this->product_id);
        $this->model->total_sum = $product->unit_price * $this->count;
        $this->model->company_id = $product->company_id;
        $this->model->user_id = \Yii::$app->user->id;
        $this->model->status = ShopEnum::ORDER_STATUS_WAITING;
        $this->model->created_at = date("Y-m-d H:i:s");
        $this->model->request_end = null;
        $att = $this->attributes;
        $this->model->setAttributes($att, false);
        if ($this->model->attributes && $this->model->validate() && $this->model->save()) {
            $orderId = $this->model->id;
            OrderRegion::deleteAll(['order_id' => $this->model->id]);
            foreach ($this->regions as $region) {
                $productRegion = new OrderRegion();
                $productRegion->region_id = $region;
                $productRegion->order_id = $orderId;
                if (!($productRegion->validate() && $productRegion->save())) {
                    $transaction->rollBack();
                    $this->addError('regions', $productRegion->errors);

                    return false;
                }
            }

            $transaction->commit();
            return true;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}