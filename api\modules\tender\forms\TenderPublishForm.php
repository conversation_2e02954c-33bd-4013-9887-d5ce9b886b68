<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderResource;
use common\enums\CompanyTransactionEnum;
use common\enums\OperationTypeEnum;
use common\enums\TenderEnum;
use common\models\CompanyTransaction;
use common\models\VirtualTransaction;
use common\models\WorkdayCalendar;
use Yii;
use yii\base\Exception;
use yii\helpers\ArrayHelper;
use yii\web\NotFoundHttpException;

class TenderPublishForm extends BaseRequest
{

    public TenderResource $model;
    public $id;

    public function rules()
    {
        $parent = parent::rules();
        $child = [
            ['id', 'required'],
            ['id', 'integer']
        ];
        return array_merge($parent, $child);
    }


    /**
     * @throws Exception
     * @throws \yii\db\Exception
     * @throws NotFoundHttpException
     */
    public function getResult()
    {
        date_default_timezone_set("Asia/Tashkent");
        $model = TenderResource::find()->notDeletedAndFromCompany()->andWhere(['id' => $this->id])->one();

        if (!$model) throw new NotFoundHttpException("Tender is not found");
        $this->model = $model;

        if ($this->model->state != TenderEnum::STATE_READY_TO_PRESENT) {
            $this->addError("error", t("Tender e'lon qilish holatida emas"));
            return false;
        }

        $user = \Yii::$app->user->identity;
        $company = $user->company;
        $isBudget = $user->isBudget;
        // 0.15 foiz vositachilik yigʻimi zakazchikdan lekin 10 ming soʻmdan oshmaydi. Zalog yoʻq zakazchikdan
        $totalPrice = $this->model->tenderTotalPrice;
        $commission_sum = $totalPrice * env('COMMISSION_PERCENT', 0.0015);
        $total_block_sum = ($commission_sum > env('TENDER_CUSTOMER_MAX_COMMISSION_SUM', 1000000) ? env('TENDER_CUSTOMER_MAX_COMMISSION_SUM', 1000000) : $commission_sum);



//        if ($company->availableBalance < $total_block_sum) {
//            $this->addError("error", t("Balansda mablag' yetarli emas"));
//            return false;
//        }
//        $company_transaction_commission = new CompanyTransaction([
//            'company_id' => $company->id,
//            'tender_id' => $this->model->id,
//            'amount' => $total_block_sum,
//            'type' => CompanyTransactionEnum::TYPE_BLOCK_COMMISION,
//            'description' => Yii::t("main", "Tender uchun komissiya bandlandi"),
//            'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//            'transaction_date' => date("Y-m-d H:i:s"),
//        ]);
//
//        if (!$company_transaction_commission->save()) {
//
//            $this->addErrors($company_transaction_commission->errors);
//            $transaction->rollBack();
//            return false;
//        }
        $transaction = Yii::$app->db->beginTransaction();
        if (!hasMoney($company, $total_block_sum)) {
            $this->addError("error", t("Balansda mablag' yetarli emas"));
            return false;
        }
        try {
            VirtualTransaction::saveTransaction(
                $company,
                $company,
                $company->isBudget() ? OperationTypeEnum::P_B_31101 : OperationTypeEnum::P_K_30101,
                $company->isBudget() ? OperationTypeEnum::P_B_31202 : OperationTypeEnum::P_K_30202,
                $total_block_sum,
                Yii::t("main", "Tender uchun komissiya bandlandi"),
                OperationTypeEnum::PRODUCT_NAME_TENDER,
                $this->model->id,
                null,
                OperationTypeEnum::BLOCK_SALE_COMMISSION
            );
        } catch (Exception $ex) {
            $transaction->rollBack();
            throw $ex;
        }
        if (!$isBudget) {
            $holidays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::HOLIDAY]), 'local_date', 'local_date');
            $workDays = ArrayHelper::map(WorkdayCalendar::findAll(['type' => WorkdayCalendar::WORKDAY]), 'local_date', 'local_date');

            $this->model->publish_days = $this->model->placement_period;
            $endDate = addDaysExcludingWeekends(date("Y-m-d H:i:s"), $this->model->placement_period, $workDays, $holidays);
            $this->model->end_date = $endDate;
            $this->model->state = TenderEnum::STATE_READY;
        } else {
            $this->model->state = TenderEnum::STATE_DMBAT;
        }
        $this->model->status = TenderEnum::STATUS_ACTIVE;

        if ($this->model->save()) {
            $transaction->commit();
            return $this->model->id;
        } else {
            $this->addErrors($this->model->errors);
            $transaction->rollBack();
            return false;
        }

    }
}