<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use app\modules\shop\resources\ContractExternalTenderResource;
use common\enums\TenderEnum;

class ExternalTenderContractFilter extends BaseRequest
{
    public ?string $type = null;
    public function rules()
    {
        return [
            [['type'], 'required'],
            [['type'], 'integer'],
            ['type','in','range' => [TenderEnum::TYPE_TENDER,TenderEnum::TYPE_INVITE]]
        ];
    }

    public function getResult()
    {
        $model = ContractExternalTenderResource::find()
            ->andWhere("auction_id is not null");
        if ($this->type) {
            $model->joinWith("tender")
                ->andWhere("tender.type = {$this->type}");
        } else {
            $model->andWhere("0=1");
        }

        return paginate($model);
    }
}
