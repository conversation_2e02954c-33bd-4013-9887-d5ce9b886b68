<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\shop\resources\OrderResource;
use common\enums\ShopEnum;
use yii\base\Exception;
use yii\web\NotFoundHttpException;

class OneSidedAcceptForm extends BaseRequest
{

    public ?OrderResource $model;
    public $id;

    public function rules()
    {
        return [
            ['id', 'required']
        ];
    }

    /**
     * @throws \yii\db\Exception
     * @throws Exception
     */
    public function getResult()
    {
        $this->model = OrderResource::findOne(['id' => $this->id]);
        if (!$this->model) {
            throw new NotFoundHttpException(t("Requested order not found"));
        }
        $transaction = \Yii::$app->db->beginTransaction();

        if ($this->model->status != ShopEnum::ORDER_STATUS_PROCESS) {
            throw new Exception(t("Hujjat tasdiqlanadigan holatda emas !!!"));
        }

        $model = OrderResource::findOne($this->id);
        if(!$model){
            throw new NotFoundHttpException(t("Hujjat emas !!!"));
        }

        $this->model->status = ShopEnum::ORDER_STATUS_ACCEPT_ONE_SIDED;
        if ($this->model->save()) {
            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}