<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\shop\resources\OrderRequestResource;
use api\modules\shop\resources\OrderResource;
use common\enums\ShopEnum;
use yii\base\Exception;

class NoOfferRejectForm extends BaseRequest
{

    public OrderRequestResource $model;
    public $id;

    public function rules()
    {
        return [
            [['id'], 'required'],
            [['id'], 'number'],
        ];
    }

    /**
     * @throws \yii\db\Exception
     * @throws Exception
     */
    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        $this->model = OrderRequestResource::findOne(['id' => $this->id]);
        if (!$this->model) {
            $transaction->rollBack();
            throw new Exception(t("OrderRequest not found."));
        }
        $this->model->status = ShopEnum::ORDER_REQUEST_STATUS_CANCEL;
        if ($this->model->save()) {

            $order = OrderResource::findOne($this->model->order_id);

            if (!$order) {
                throw new Exception(t("Order not found"));
            }
            $order->updateAttributes([
                'one_sided_customer' => date("Y-m-d H:i:s", strtotime("+10 minutes", time())),
            ]);
            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}