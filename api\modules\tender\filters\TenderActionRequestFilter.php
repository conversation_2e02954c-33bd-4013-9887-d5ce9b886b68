<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderActionRequestResource;

class TenderActionRequestFilter extends BaseRequest
{
    public $type;
    public $lot;

    public function rules(): array
    {
        return [
            ['type', 'integer'],
            ['lot', 'string']
        ];
    }

    public function getResult()
    {
        $user = \Yii::$app->user->identity;
        $query = TenderActionRequestResource::find()
            ->innerJoin('tender', 'tender_action_request.tender_id = tender.id')
            ->where(['tender.company_id' => $user->company_id]);

        if ($this->lot) {
            $query->andWhere(['like', 'tender.lot' => $this->lot]);
        }

        if ($this->type !== null) {
            $query->andWhere(['tender_action_request.type' => $this->type]);
        }

        return paginate($query);
    }
}