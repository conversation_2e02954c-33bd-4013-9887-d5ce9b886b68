<?php

use common\models\bank\search\BankTransactionInSearch;
use yii\helpers\Html;
use yii\grid\GridView;
use yii\helpers\Url;

/**
 * @var yii\web\View $this
 * @var common\models\bank\search\BankTransactionInSearch $searchModel
 * @var yii\data\ActiveDataProvider $dataProvider
 */

$this->title = t("Bank transaction IN");
$this->params['breadcrumbs'][] = $this->title;


?>


<div class="black-list-index">
    <div class="card">

        <div class="card-body p-0">
            <?php // echo $this->render('_search', ['model' => $searchModel]); ?>

            <?php echo GridView::widget([
                'layout' => "{items}\n{pager}",
                'options' => [
                    'class' => ['gridview', 'table-responsive'],
                ],
                'tableOptions' => [
                    'class' => ['table', 'text-nowrap', 'table-striped', 'table-bordered', 'mb-0'],
                ],
                'dataProvider' => $dataProvider,
                'filterModel' => $searchModel,
                'rowOptions' => function (
                    $model,
                    $key,
                    $index,
                    $grid
                ) {
                    return [
                        'id' => $key,
                        'ondblclick' => 'location.href="'
                            . Url::to(['in-view'])
                            . '?id="+(this.id);',
                    ];
                },
                'columns' => [
                    ['class' => 'yii\grid\SerialColumn'],
                    [
                        'label'=>'name_payer',
                        'format' => 'raw',
                        'value'=>function ($model) {
                            return Html::a($model->name_payer,['bank-transaction/in-view','id'=>$model->id]);
                        },
                    ],
                    'account_payer',
                    'payment_date',
                    'name_payer',
                    [
                        'attribute' => 'amount',
                        'value' => function($model){
                            return number_format($model->amount / 100);
                        }
                    ],
                    'status',
                    'created_at',

                ],
            ]); ?>

        </div>
        <div class="card-footer">
            <?php echo getDataProviderSummary($dataProvider) ?>
        </div>
    </div>

</div>
