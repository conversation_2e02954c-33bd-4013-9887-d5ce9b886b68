<?php

namespace api\modules\tender\forms;

use api\components\BaseModel;
use api\components\BaseRequest;
use api\modules\tender\resources\ContractResource;
use api\modules\tender\resources\TenderResource;
use common\enums\CompanyEnum;
use common\enums\CompanyTransactionEnum;
use common\enums\ContractEnum;
use common\enums\TenderEnum;
use common\models\Company;
use common\models\CompanyTransaction;
use common\models\Contract;
use common\models\TenderRequest;
use yii\base\Exception;

class TenderReceiveProductForm extends BaseModel
{
    public TenderResource $model;
    public $pkcs7;

    public function rules()
    {
        $parent = parent::rules();
        $child =  [

        ];
        return  array_merge($parent,$child);
    }


    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;


        parent::__construct($params);
    }

    public function getResult()
    {
        /**
         * @var $win TenderRequest
         */

        $transaction = \Yii::$app->db->beginTransaction();

        $this->model->state = TenderEnum::STATE_RECEIVE_PRODUCT;
        if (!$this->model->save()) {
            throw new Exception(t("Saqlashda xatolik"));
        }

        /**
         * @var $contract Contract
         */

        $contract = ContractResource::find()->where(['status' => ContractEnum::STATUS_ACCEPT])->andWhere(['tender_id' => $this->model->id])->one();
        if (!$contract) {
            $transaction->rollBack();
            throw new Exception(t("Shartnoma topilmadi"));
        }
        $contract->status = ContractEnum::STATUS_RECEIVED_PRODUCT;
        if (!$contract->save()) {
            $transaction->rollBack();
            throw new Exception(t("Shartnoma xolatini o'zgartirishda xatolik"));
        }


        // tender shartida ish tugaganidan kn deyilgan bo'lsa, zalogni qaytarish kerak

        if ($this->model->unblocking_type == TenderEnum::AFTER_END_WORK) {
            $company_transaction_zalog = CompanyTransaction::find()->where([
                'company_id' => $contract->producer_id,
                'tender_id' => $this->model->id,
                'type' => CompanyTransactionEnum::TYPE_ZALOG,
                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                'reverted_id' => null
            ])->one();

            $revert = new CompanyTransaction([
                'company_id' => $company_transaction_zalog->company_id,
                'amount' => $company_transaction_zalog->amount,
                'contract_id' => $contract->id,
                'tender_id' => $company_transaction_zalog->tender_id,
                'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                'description' => \Yii::t("main", "Garov qaytarildi"),
                'transaction_date' => date("Y-m-d H:i:s"),
            ]);

            if ($revert->save()) {
                $company_transaction_zalog->reverted_id = $revert->id;
                $company_transaction_zalog->save(false);
            } else {
                $this->addErrors($revert->errors);
                $transaction->rollBack();
                return false;
            }
        }

        $transaction->commit();
        return true;

    }

}