<?php

namespace api\modules\tender\forms;

use api\components\BaseRequest;
use api\modules\tender\resources\ContractResource;
use api\modules\tender\resources\TenderResource;
use common\enums\ContractEnum;
use common\enums\OperationTypeEnum;
use common\enums\TenderEnum;
use common\models\Contract;
use common\models\TenderRequest;
use common\models\VirtualTransaction;
use yii\base\Exception;

class TenderReceiveProductForm extends BaseRequest
{
    public TenderResource $model;

    public function rules()
    {
        return [];
    }


    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;


        parent::__construct($params);
    }

    /**
     * @throws \yii\db\Exception
     * @throws Exception
     * @throws \Throwable
     */
    public function getResult()
    {
        /**
         * @var $win TenderRequest
         */

        $transaction = \Yii::$app->db->beginTransaction();

        $this->model->state = TenderEnum::STATE_RECEIVE_PRODUCT;
        if (!$this->model->save()) {
            throw new Exception(t("Saqlashda xatolik"));
        }

        /**
         * @var $contract Contract
         */

        $contract = ContractResource::find()->where(['status' => ContractEnum::STATUS_ACCEPT])->andWhere(['tender_id' => $this->model->id])->one();
        if (!$contract) {
            $transaction->rollBack();
            throw new Exception(t("Shartnoma topilmadi"));
        }
        $contract->status = ContractEnum::STATUS_RECEIVED_PRODUCT;
        if (!$contract->save()) {
            $transaction->rollBack();
            throw new Exception(t("Shartnoma xolatini o'zgartirishda xatolik"));
        }


        // tender shartida ish tugaganidan kn deyilgan bo'lsa, zalogni qaytarish kerak

        if ($this->model->unblocking_type == TenderEnum::AFTER_END_WORK) {
//            $company_transaction_zalog = CompanyTransaction::find()->where([
//                'company_id' => $contract->producer_id,
//                'tender_id' => $this->model->id,
//                'type' => CompanyTransactionEnum::TYPE_ZALOG,
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                'reverted_id' => null
//            ])->one();
//
//            $revert = new CompanyTransaction([
//                'company_id' => $company_transaction_zalog->company_id,
//                'amount' => $company_transaction_zalog->amount,
//                'contract_id' => $contract->id,
//                'tender_id' => $company_transaction_zalog->tender_id,
//                'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                'description' => \Yii::t("main", "Garov qaytarildi"),
//                'transaction_date' => date("Y-m-d H:i:s"),
//            ]);
//
//            if ($revert->save()) {
//                $company_transaction_zalog->reverted_id = $revert->id;
//                $company_transaction_zalog->save(false);
//            } else {
//                $this->addErrors($revert->errors);
//                $transaction->rollBack();
//                return false;
//            }
            /** @var VirtualTransaction $deposit */
            $deposit = VirtualTransaction::find()->where([
                'credit_company_id' => $contract->producer_id,
                'tender_id' => $this->model->id,
                'type' => OperationTypeEnum::BLOCK_TRANSACTION_DEPOSIT_AGAIN,
                'contract_id' => $contract->id,
                'parent_id' => null,
            ])->andWhere(['>', 'credit', 0])->one();
            if (!$deposit) {
                $transaction->rollBack();
                throw new Exception(t("Xatolik yuz berdi"));
            }
            try {
                $revertID = VirtualTransaction::saveTransaction(
                    $deposit->creditCompany,
                    $deposit->debitCompany,
                    $deposit->debitAccount->prefix_account,
                    $deposit->creditAccount->prefix_account,
                    $deposit->credit,
                    "Buyurtmachi productni qabul qildi, yetkazib beruvchiga deposit qaytarildi.",
                    OperationTypeEnum::PRODUCT_NAME_TENDER,
                    $this->model->id,
                    $contract->id,
                    OperationTypeEnum::CANCEL_BLOCK_TRANSACTION_DEPOSIT,
                );
                $deposit->parent_id = $revertID;
                if (!$deposit->save()) {
                    $transaction->rollBack();
                    $this->addErrors($deposit->errors);
                    return false;
                }
            } catch (Exception $e) {
                $transaction->rollBack();
                $this->addError("error", $e->getMessage());
                return false;
            }
        }

        $transaction->commit();
        return true;

    }

}