<?php

namespace backend\forms;

use common\models\SoleSupplier;
use common\models\SoleSupplierClassifier;
use Yii;
use yii\base\Model;
use yii\db\Exception;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\web\ServerErrorHttpException;

class SoleSupplierForm extends Model
{
    public ?SoleSupplier $_model = null;
    public ?string $name_uz = null;
    public ?string $name_ru = null;
    public ?string $name_en = null;
    public ?string $name_uzk = null;
    public ?string $address_uz = null;
    public ?string $address_en = null;
    public ?string $address_ru = null;
    public ?string $address_uzk = null;
    public ?string $tin = null;
    public ?string $phone = null;
    public $classifiers;
    public function __construct(SoleSupplier $_model,$config = [])
    {
        $this->_model = $_model;
        $this->_setDefaultValues();
        parent::__construct($config);
    }

    public function _setDefaultValues()
    {
        if (!$this->_model->isNewRecord)
        {
            $this->attributes = $this->_model->attributes;
            $_classifiers = (new Query())
                ->select('classifier.*')
                ->from('sole_supplier_classifier')
                ->leftJoin('sole_supplier', 'sole_supplier.id = sole_supplier_classifier.sole_supplier_id')
                ->leftJoin('classifier', 'classifier.id = sole_supplier_classifier.classifier_id')
                ->where(['sole_supplier_classifier.sole_supplier_id' => $this->_model->id])->groupBy('classifier.id')->all();
            if (!empty($_classifiers))
                $this->classifiers = ArrayHelper::map($_classifiers, 'id', 'title_'.Yii::$app->language);
        }
    }

    public function rules(): array
    {
        return [
            [['name_uz','address_uz','tin',], 'required',],
            ['classifiers', 'required','when' => function($model) {
                return $this->_model->isNewRecord;
            },
                'whenClient' => "function (attribute, value) {
                return " . ($this->_model->isNewRecord ? 'true' : 'false') . ";
            }",],
            [
                ['name_uz','name_ru','name_en','name_uzk','address_uz','address_ru','address_en','address_uzk','phone'],
                'string', 'max' => 255
            ],
            ['tin', 'match', 'pattern' => '/^\d{9}$/', 'message' => t("Tin number must be 9 digit")],
        ];
    }

    /**
     * @throws Exception
     */
    public function save(): bool
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $isNewRecord = $this->_model->isNewRecord;
            $this->_model->attributes = [
                'name_uz' => $this->name_uz,
                'name_ru' => $this->name_ru,
                'name_en' => $this->name_en,
                'name_uzk' => $this->name_uzk,
                'address_uz' => $this->address_uz,
                'address_ru' => $this->address_ru,
                'address_uzk' => $this->address_uzk,
                'tin' => $this->tin,
                'phone' => $this->phone
            ];
            $isSaved = $this->_model->save();
            if ($isSaved) {
                if (!$this->_model->id)
                    $this->_model->refresh();
                if (!$isNewRecord && !empty($this->classifiers)) {
                    SoleSupplierClassifier::deleteAll(['sole_supplier_id' => $this->_model->id]);
                }
                if (is_array($this->classifiers)) {
                    foreach ($this->classifiers as $classifier) {
                        $data = new SoleSupplierClassifier();
                        $data->classifier_id = $classifier;
                        $data->sole_supplier_id = $this->_model->id;
                        if (!$data->save())
                            throw new ServerErrorHttpException("Classifier saving error!");
                    }
                }
                $transaction->commit();
                return true;
            }
            throw new ServerErrorHttpException("Supplier not saved!");
        } catch (ServerErrorHttpException $ex) {
            $transaction->rollBack();
            $message = $ex->getMessage();
            Yii::$app->session->setFlash('error', $message);
            return false;
        }
    }

    public function getID()
    {
        if (!empty($this->_model) && isset($this->_model->id))
            return $this->_model->id;
        return null;
    }
}