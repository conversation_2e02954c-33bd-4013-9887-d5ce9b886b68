<?php

namespace api\modules\auction\forms;

use api\components\BaseModel;
use api\components\BaseRequest;
use api\modules\auction\resources\AuctionResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\CompanyBankAccountBudgetResource;
use api\modules\common\resources\PlanScheduleClassifierCreateResource;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\AuctionEnum;
use common\enums\CompanyEnum;
use common\enums\CompanyTransactionEnum;
use common\enums\StatusEnum;
use common\models\auction\AuctionClassifier;
use common\models\auction\AuctionCondition;
use common\models\auction\AuctionFile;
use common\models\auction\AuctionHistory;
use common\models\Bmh;
use common\models\ClassifierCategory;
use common\models\Company;
use common\models\CompanyTransaction;
use common\models\User;
use Yii;

class AuctionCreateBudgetForm extends BaseModel
{
    public AuctionResource $model;

    public $planSchedules = [];
    public $deliveryPeriod;
    public $address;
    public $accountId;
    public $pkcs7;
    public $delivery_basis;
    public $auctionFiles = [];
    public $auctionConditions = [];


    public function __construct($params = [])
    {
        $this->model = new AuctionResource();

        parent::__construct($params);
    }

    public function rules()
    {
        $parent = parent::rules();
        $child = [
            [['planSchedules', 'auctionConditions', 'deliveryPeriod', 'address', 'auctionFiles', 'accountId', 'delivery_basis'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['deliveryPeriod', 'accountId'], 'integer'],
            [['address'], 'string', 'max' => 255],
            [['deliveryPeriod',], 'integer', 'min' => 7, 'message' => t("{attribute} eng kichik qiymati 7")],
        ];
        return array_merge($parent, $child);
    }


    public function attributeLabels()
    {
        return [
            'plan_schedule_id' => Yii::t('main', 'Reja jadvali'),
            'classifier_category_id' => Yii::t('main', 'Mahsulotlar toifasi'),
            'auction_classifiers' => Yii::t('main', 'Mahsulotlar'),
            'auction_conditions' => Yii::t('main', 'Ahohida shartlar'),
            'address' => Yii::t('main', 'Yetkazib berish manzili'),
            'auction_files' => Yii::t('main', "Qo'shimcha hujjatlar"),
            'accountId' => Yii::t('main', 'Hisob raqamni'),
            'delivery_period' => Yii::t('main', 'Yetkazib berish muddati'),
        ];
    }

    public function getResult()
    {
        /**
         * @var $company Company
         */
        $transaction = \Yii::$app->db->beginTransaction();
        $user = Yii::$app->user->identity;
        $company = $user->company;
        $model = $this->model;
        $totalSum = 0;
        if (!$user->isBudget) {
            $this->addError("error", t("Budjet mijozlar foydalanishi mumkin"));
            return false;
        }
        $organ = $user->organ;
        $companyBankAccount = CompanyBankAccountBudgetResource::findOne(['company_id' => $company->id, 'organ' => $organ, 'id' => $this->accountId]);
        if (!$companyBankAccount) {
            $this->addError("accountId", t("Xisob raqami topilmadi"));
            return false;
        }

        $model->organ = $organ;
        $model->company_id = $company->id;
        $model->status = AuctionEnum::STATUS_MODERATING;
        $model->receiver_email = $user->email;
        $model->receiver_phone = $company->phone;

        $model->delivery_period = $this->deliveryPeriod;
        $model->address = $this->address;
        $model->account = $companyBankAccount->account;
        $model->delivery_basis = $this->delivery_basis;

        if (!$model->save()) {
            $this->addErrors($model->errors);
            $transaction->rollBack();
            return false;
        }

        foreach ($this->planSchedules as $classifier) {

            if (!isset($classifier['order']) || $classifier['order'] == null) {
                $transaction->rollBack();
                $this->addError("order", t("Tartib yuborish kerak"));
                return false;
            }

            if (!isset($classifier['count']) || $classifier['count'] == null || $classifier['count'] <= 0) {
                $transaction->rollBack();
                $this->addError("count", t("Maxsulot soni yuborish kerak"));
                return false;
            }
            if (!isset($classifier['price']) || $classifier['price'] == null) {
                $transaction->rollBack();
                $this->addError("price", t("Maxsulot narxi yuborish kerak"));
                return false;
            }
            if (!isset($classifier['classifierId']) || $classifier['classifierId'] == null) {
                $transaction->rollBack();
                $this->addError("classifierId", t("Maxsulot id yuborish kerak"));
                return false;
            }

            if (!isset($classifier['planScheduleClassifierId']) || $classifier['planScheduleClassifierId'] == null) {
                $transaction->rollBack();
                $this->addError("planScheduleClassifierId", t("Maxsulot kategoriya id yuborish kerak"));
                return false;
            }

            if (!isset($classifier['planScheduleId']) || $classifier['planScheduleId'] == null) {
                $transaction->rollBack();
                $this->addError("planScheduleId", t("Reja jadvali idsi yuborish kerak"));
                return false;
            }
            if (!isset($classifier['description']) || $classifier['description'] == null) {
                $transaction->rollBack();
                $this->addError("description", t("Reja jadvali tavsifi yuborish kerak"));
                return false;
            }

            $cls = ClassifierResource::findOne($classifier['classifierId']);
            if (!$cls) {

                $this->addError('classifier_id', t("Maxsulot guruhi topilmadi"));
                $transaction->rollBack();
                return false;
            }

            $planScheduleClassifier = PlanScheduleClassifierCreateResource::find()->where([
                'plan_schedule_id' => $classifier['planScheduleId'],
                'id' => $classifier['planScheduleClassifierId'],
                'classifier_id' => $cls->id,
                'status' => StatusEnum::STATUS_ACTIVE
            ])->one();
            if (!$planScheduleClassifier) {

                $this->addError('classifier_id', t("Mahsulot reja jadvali topilmadi"));
                $transaction->rollBack();
                return false;
            }

            if ($planScheduleClassifier && $planScheduleClassifier->count_live >= $classifier['count']) {
                $planScheduleClassifier->description = $classifier['description'];
                $planScheduleClassifier->count_used = $planScheduleClassifier->count_used + $classifier['count'];
                $planScheduleClassifier->count_live = $planScheduleClassifier->count - $planScheduleClassifier->count_used;
                if (!$planScheduleClassifier->save()) {
                    $this->addErrors($planScheduleClassifier->errors);
                    $transaction->rollBack();
                    return false;
                }

                if ($planScheduleClassifier->count_live == 0) {
                    $planScheduleClassifier->update([
                        'status' => 400, // not active
                    ]);
                }
            } else {

                $this->addError("error", t("Reja jadvalida maxsulot soni yetarli emas"));
                $transaction->rollBack();
                return false;
            }

            $auction_classifier = new AuctionClassifier();

            $auction_classifier->classifier_id = $cls->id;
            $auction_classifier->unit_id = $planScheduleClassifier->unit_id;
            $auction_classifier->description = $planScheduleClassifier->description;
            $auction_classifier->auction_id = $model->id;
            $auction_classifier->plan_schedule_id = $planScheduleClassifier->plan_schedule_id;
            $auction_classifier->plan_schedule_classifier_id = $planScheduleClassifier->id;

            $auction_classifier->order = $classifier['order'];
            $auction_classifier->quantity = $classifier['count'];
            $auction_classifier->price = $classifier['price'] * 100;
            $auction_classifier->total_sum = $classifier['count'] * $auction_classifier->price;

            $auction_classifier->status = StatusEnum::STATUS_ACTIVE;

            if (!$auction_classifier->save()) {

                $this->addErrors($auction_classifier->errors);
                $transaction->rollBack();
                return false;
            }

            $totalSum += $auction_classifier->total_sum;
        }

        $bmh = Bmh::getAmount();
        $price = $totalSum;
        if ($company->organization_type == CompanyEnum::NO_BYUDJET) {
            $bmh = $bmh * 25000;

            if ($price > $bmh) {
                $transaction->rollBack();
                $this->addError("error", t("Tovarlarning qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining yigirma besh ming baravarigacha bo‘lgan miqdorni tashkil etadi"));
                return false;
            }

        } else {
            $bmh = $bmh * 6000;
            if ($price > $bmh) {
                $transaction->rollBack();
                $this->addError("error", t("Tovarlarning qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining olti ming baravarigacha bo‘lgan miqdorni tashkil etadi"));
                return false;
            }
        }

        foreach ($this->auctionFiles as $fileId) {

            $auctionFile = new AuctionFile();
            $auctionFile->auction_id = $model->id;
            $auctionFile->file_id = $fileId;
            if (!$auctionFile->save()) {
                $this->addErrors($auctionFile->errors);
                $transaction->rollBack();
                return false;
            }

        }

        if ($this->auctionConditions && count($this->auctionConditions) > 0) {
            foreach ($this->auctionConditions as $condition) {
                $auction_condition = new AuctionCondition();
                $auction_condition->auction_id = $model->id;
                $auction_condition->condition = $condition['condition'];
                $auction_condition->text = $condition['text'];
                $auction_condition->status = StatusEnum::STATUS_ACTIVE;
                if (!$auction_condition->save()) {
                    $this->addErrors($auction_condition->errors);
                    $transaction->rollBack();
                    return false;
                }
            }
        }

        $model->total_sum = $totalSum;
        $model->lot = $model->generateLotNumber($model->id);
        if (!$model->save()) {
            $this->addErrors($model->errors);
            $transaction->rollBack();
            return false;
        }

        $commission_sum = $model->total_sum * env('COMMISSION_PERCENT', 0.0015);
        $commission_sum = $commission_sum > 1000000 ? 1000000 : $commission_sum;

        $company->lastCompanyBalance->calculateBalance();

        if ($company->availableBalance < $commission_sum) {
            $transaction->rollBack();
            $this->addError('error', t("Mablag' yetarli emas"));
            return false;
        }

        $history = new AuctionHistory();
        $history->auction_id = $model->id;
        $history->user_id = $user->id;
        $history->status = $this->model->status;
        $history->comment = t("Yaratildi. Moderatsiyaga yuborildi.");
        $history->created_at = date("Y-m-d H:i:s");
        if (!$history->save()) {
            $this->addErrors($history->errors);
            $transaction->rollBack();
            return false;
        }

        $transaction->commit();
        return true;
    }
}
