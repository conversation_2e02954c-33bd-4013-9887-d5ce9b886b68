<?php

namespace common\models;

use common\enums\SystemEnum;
use common\models\auction\Auction;
use Yii;
use common\enums\OperationTypeEnum;
use common\models\shop\Order;
use common\components\ActiveRecordMeta;
use yii\db\ActiveQuery;
use yii\base\Exception;

/**
 * This is the model class for table "virtual_transaction".
 *
 * @property int $id
 * @property int|null $debit_company_id
 * @property int|null $credit_company_id
 * @property int|null $order_id
 * @property int|null $product_id
 * @property int|null $tender_id
 * @property int|null $auction_id
 * @property int|null $contract_id
 * @property string|null $debit_company_tin
 * @property string|null $credit_company_tin
 * @property int|null $debit_account_id
 * @property string|null $debit_account
 * @property int|null $credit_account_id
 * @property string|null $credit_account
 * @property string|null $prefix_account
 * @property string|null $description
 * @property string|null $operation_type
 * @property float|null $credit
 * @property float|null $debit
 * @property int|null $parent_id
 * @property int|null $debit_id
 * @property int|null $bank_transaction_id
 * @property int|null $bank_transaction_out_id
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $created_by
 * @property int|null $updated_by
 *
 * @property CompanyVirtualAccount $creditAccount
 * @property CompanyVirtualAccount $debitAccount
 *
 * @property Company $debitCompany
 * @property Company $creditCompany
 * @property Tender $tender
 * @property Auction $auction
 * @property Order $order
 * @property Contract $contract
 */
class VirtualTransaction extends ActiveRecordMeta
{


    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'virtual_transaction';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['order_id', 'product_id', 'tender_id', 'auction_id', 'contract_id', 'debit_company_tin', 'credit_company_tin', 'debit_account_id', 'debit_account', 'credit_account_id', 'credit_account', 'prefix_account', 'description', 'operation_type', 'credit', 'debit', 'parent_id', 'debit_id', 'bank_transaction_id', 'bank_transaction_out_id', 'created_at', 'updated_at', 'deleted_at', 'created_by', 'updated_by'], 'default', 'value' => null],
            [['debit_company_id', 'credit_company_id', 'order_id', 'product_id', 'tender_id', 'auction_id', 'contract_id', 'debit_account_id',  'credit_account_id', 'parent_id', 'debit_id', 'bank_transaction_id', 'bank_transaction_out_id', 'created_by', 'updated_by'], 'integer'],
            [['credit', 'debit'], 'number'],
            [['created_at', 'updated_at', 'deleted_at'], 'safe'],
            [['debit_company_tin', 'credit_company_tin'], 'string', 'max' => 14],
            [['debit_account', 'credit_account'], 'string', 'max' => 25],
            [['prefix_account', 'description', 'operation_type'], 'string', 'max' => 255],
            [['bank_transaction_id'], 'unique'],
            [['bank_transaction_out_id'], 'unique'],
            [['credit_account_id'], 'exist', 'skipOnError' => true, 'targetClass' => CompanyVirtualAccount::class, 'targetAttribute' => ['credit_account_id' => 'id']],
            [['debit_account_id'], 'exist', 'skipOnError' => true, 'targetClass' => CompanyVirtualAccount::class, 'targetAttribute' => ['debit_account_id' => 'id']],
            [['debit_company_id'], 'exist', 'skipOnError' => true, 'targetClass' => Company::class, 'targetAttribute' => ['debit_company_id' => 'id']],
            [['credit_company_id'], 'exist', 'skipOnError' => true, 'targetClass' => Company::class, 'targetAttribute' => ['credit_company_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'debit_company_id' => 'Debit Company ID',
            'credit_company_id' => 'Credit Company ID',
            'order_id' => 'Order ID',
            'product_id' => 'Product ID',
            'tender_id' => 'Tender ID',
            'auction_id' => 'Auction ID',
            'contract_id' => 'Contract ID',
            'debit_company_tin' => 'Debit Company Tin',
            'credit_company_tin' => 'Credit Company Tin',
            'debit_account_id' => 'Debit Account ID',
            'debit_account' => 'Debit Account',
            'credit_account_id' => 'Credit Account ID',
            'credit_account' => 'Credit Account',
            'prefix_account' => 'Prefix Account',
            'description' => 'Description',
            'operation_type' => 'Operation Type',
            'credit' => 'Credit',
            'debit' => 'Debit',
            'parent_id' => 'Parent ID',
            'debit_id' => 'Debit ID',
            'bank_transaction_id' => 'Bank Transaction ID',
            'bank_transaction_out_id' => 'Bank Transaction Out ID',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'deleted_at' => 'Deleted At',
            'created_by' => 'Created By',
            'updated_by' => 'Updated By',
        ];
    }

    /**
     * Gets query for [[CreditAccount]].
     *
     * @return ActiveQuery
     */
    public function getCreditAccount()
    {
        return $this->hasOne(CompanyVirtualAccount::class, ['id' => 'credit_account_id']);
    }

    /**
     * Gets query for [[DebitAccount]].
     *
     * @return ActiveQuery
     */
    public function getDebitAccount()
    {
        return $this->hasOne(CompanyVirtualAccount::class, ['id' => 'debit_account_id']);
    }

    public function getDebitCompany()
    {
        return $this->hasOne(Company::class, ['id' => 'debit_company_id']);
    }

    public function getCreditCompany()
    {
        return $this->hasOne(Company::class, ['id' => 'credit_company_id']);
    }

    /**
     * Saves a pair of debit and credit transactions for a company.
     *
     * @param Company $debitCompany
     * @param Company $creditCompany
     * @param string $debit The debit account prefix.
     * @param string $credit The credit account prefix.
     * @param int $price The transaction amount (must be non-negative).
     * @param string $description The transaction description.
     * @param string $productName The dynamic attribute name for the product ID.
     * @param int $productID The product ID value.
     * @param int|null $contractID The contract ID.
     * @param string|null $operation_type The operation type.
     * @return int The ID of the credit transaction.
     * @throws Exception
     * @throws \Throwable
     */
    public static function saveTransaction(
        Company $debitCompany,
        Company $creditCompany,
        string $debit, // debit prefix account
        string $credit, // credit prefix account
        int $price, // price in tiyin
        string $description,
        ?string $productName = null, // order_id, tender_id, auction_id
        ?int $productID = null,
        ?int $contractID = null,
        ?string $operation_type = null
    ): int
    {

        if ($price < 0) {
            throw new Exception("Transaction amount cannot be negative.");
        }

        // Find debit and credit accounts
        $debitAccount = CompanyVirtualAccount::findOneByPrefixAndCompany($debit, $debitCompany->id);
        if (!$debitAccount) {
            throw new Exception("Hisob raqam topilmadi.");
        }
        if ($debitAccount->price < $price) {
            throw new Exception("Hisobda mablag' yetarli emas.");
        }

        $creditAccount = CompanyVirtualAccount::findOneByPrefixAndCompany($credit, $creditCompany->id);
        if (!$creditAccount) {
            throw new Exception("Hisob raqam topilmadi.");
        }

        // Debit transaction object
        $debitTransaction = new self([
            'debit_company_id' => $debitCompany->id,
            'credit_company_id' => $creditCompany->id,
            'debit_company_tin' => $debitCompany->tin,
            'credit_company_tin' => $creditCompany->tin,
            'debit_account_id' => $debitAccount->id,
            'debit_account' => $debitAccount->account,
            'credit_account_id' => $creditAccount->id,
            'credit_account' => $creditAccount->account,
            'contract_id' => $contractID,
            'prefix_account' => $debit,
            'operation_type' => $operation_type,
            'description' => $description,
            'debit' => $price,
            'credit' => 0,
        ]);

        // Credit transaction object
        $creditTransaction = new self([
            'debit_company_id' => $debitCompany->id,
            'credit_company_id' => $creditCompany->id,
            'debit_company_tin' => $debitCompany->tin,
            'credit_company_tin' => $creditCompany->tin,
            'debit_account_id' => $debitAccount->id,
            'debit_account' => $debitAccount->account,
            'credit_account_id' => $creditAccount->id,
            'credit_account' => $creditAccount->account,
            'contract_id' => $contractID,
            'prefix_account' => $credit,
            'operation_type' => $operation_type,
            'description' => $description,
            'debit' => 0,
            'credit' => $price,
        ]);
        if (null !== $productName) {
            $debitTransaction->{$productName} = $productID;
            $creditTransaction->{$productName} = $productID;
        }
        if ($productName == OperationTypeEnum::PRODUCT_NAME_ORDER)
        {
            $order = Order::findOne(['id' => $productID]);
            $debitTransaction->product_id = $order->product_id;
            $creditTransaction->product_id = $order->product_id;
        }
        // Execute transaction
        $transaction = Yii::$app->db->beginTransaction();
        try {
            if (!$debitTransaction->save()) {
                throw new Exception('Failed to save debit transaction: ' . implode(', ', $debitTransaction->getFirstErrors()));
            }

            $creditTransaction->debit_id = $debitTransaction->id;
            if (!$creditTransaction->save()) {
                throw new Exception('Failed to save credit transaction: ' . implode(', ', $creditTransaction->getFirstErrors()));
            }
            $transaction->commit();
            return $creditTransaction->id;
        } catch (\Throwable $e) {
            $transaction->rollBack();
            throw new Exception($e->getMessage());
        }
    }

    public static function credit30101(
        Company $creditCompany,
        string $credit, // credit prefix account
        int $price, // price in tiyin
        string $description,
        ?string $operation_type = null,
        $debit
    ): int
    {
        $debitCompany = Company::findOne(['tin' => SystemEnum::STOCK_TIN]);

        if ($price < 0) {
            throw new Exception("Transaction amount cannot be negative.");
        }

        $creditAccount = CompanyVirtualAccount::findOneByPrefixAndCompany($credit, $creditCompany->id);
        if (!$creditAccount) {
            throw new Exception("Hisob raqam topilmadi.");
        }

        // Credit transaction object
        $creditTransaction = new self([
            'debit_company_id' => $debitCompany->id,
            'credit_company_id' => $creditCompany->id,
            'debit_company_tin' => $debitCompany->tin,
            'credit_company_tin' => $creditCompany->tin,
            'debit_account_id' => $debit->debit_account_id,
            'debit_account' => $debit->debit_account,
            'credit_account_id' => $creditAccount->id,
            'credit_account' => $creditAccount->account,
            'prefix_account' => (string)$credit,
            'operation_type' => $operation_type,
            'description' => $description,
            'debit' => 0,
            'credit' => $price,
            'debit_id' => $debit->id,
        ]);

        // Execute transaction
        $transaction = Yii::$app->db->beginTransaction();
        try {
            if (!$creditTransaction->save()) {
                throw new Exception('Failed to save credit transaction: ' . implode(', ', $creditTransaction->getFirstErrors()));
            }
            $transaction->commit();
            return $creditTransaction->id;
        } catch (\Throwable $e) {
            $transaction->rollBack();
            throw new Exception($e->getMessage());
        }
    }

    public static function credit30901($debit): int
    {
        $stockCompany = Company::findOne(['tin' => SystemEnum::STOCK_TIN]);
        if (!$stockCompany) {
            throw new Exception("Birja kompaniyasi topilmadi.");
        }

        $creditAccount = CompanyVirtualAccount::findOneByPrefixAndCompany(OperationTypeEnum::P_K_30901, $stockCompany->id);
        if (!$creditAccount) {
            throw new Exception("30901 hisob raqam topilmadi.");
        }

        // Credit aniqlanmagan tranzaksiya
        $creditTransaction = new self([
            'debit_company_id' => null,
            'credit_company_id' => $stockCompany->id,
            'debit_company_tin' => $stockCompany->tin,
            'credit_company_tin' => $debit->credit_company_tin,
            'debit_account_id' => $debit->debit_account_id,
            'debit_account' => $debit->debit_account,
            'credit_account_id' => $creditAccount->id,
            'credit_account' => $creditAccount->account,
            'prefix_account' => (string)OperationTypeEnum::P_K_30901,
            'operation_type' => OperationTypeEnum::CLARIFY_TRANSACTION,
            'description' => "Aniqlash jarayonidagi tranzaksiya - " . $debit->description,
            'debit' => 0,
            'credit' => $debit->debit,
            'debit_id' => $debit->id
        ]);
            $transaction = Yii::$app->db->beginTransaction();
        try {
            if (!$creditTransaction->save()) {
                $errors = $creditTransaction->getFirstErrors();
                $errorMessage = 'Failed to save credit30901 transaction: ' . implode(', ', $errors);
                throw new Exception($errorMessage);
            }
            $transaction->commit();
            return $creditTransaction->id;
        } catch (\Throwable $e) {
            $transaction->rollBack();
            throw new Exception($e->getMessage());
        }
    }

    public static function debit50111($transactionIn)
    {
        $debitCompany = Company::findOne(['tin' => SystemEnum::STOCK_TIN]);
        if (!$debitCompany) {
            throw new Exception("Birja kompaniyasi topilmadi. TIN: " . SystemEnum::STOCK_TIN);
        }

        $debitAccount = CompanyVirtualAccount::findOneByPrefixAndCompany(OperationTypeEnum::A_50111, $debitCompany->id);
        if (!$debitAccount) {
            throw new Exception("50111 hisob raqam topilmadi. Company ID: " . $debitCompany->id);
        }

        // Debit transaction object
        $debitTransaction = new self([
            'debit_company_id' => $debitCompany->id,
            'credit_company_id' => null,
            'debit_company_tin' => $debitCompany->tin,
            'credit_company_tin' => $transactionIn->inn_payer,
            'debit_account_id' => $debitAccount->id,
            'debit_account' => $debitAccount->account,
            'credit_account_id' => null,
            'credit_account' => $transactionIn->account_payer,
            'prefix_account' => (string)OperationTypeEnum::A_50111,
            'operation_type' => OperationTypeEnum::FILL_ACCOUNT,
            'description' => $transactionIn->purpose,
            'debit' => $transactionIn->amount,
            'credit' => 0,
        ]);

        // Execute transaction
        $transaction = Yii::$app->db->beginTransaction();
        try {
            if (!$debitTransaction->save()) {
                $errors = $debitTransaction->getFirstErrors();
                $errorMessage = 'Failed to save debit transaction: ' . implode(', ', $errors);
                throw new Exception($errorMessage);
            }
            $transaction->commit();
            return $debitTransaction;
        } catch (\Throwable $e) {
            $transaction->rollBack();
            throw new Exception($e->getMessage());
        }
    }

    public static function getSumDebitByCompanyTinAndPrefix(Company $company, $prefix_account)
    {
        return self::find()
                ->where(['not', ['debit' => null]])
                ->andWhere(['debit_company_id' => $company->id,'debit_company_tin' => $company->tin, 'prefix_account' => $prefix_account,])
                ->sum('debit') + 0;
    }

    public static function getSumCreditByCompanyTinAndPrefix(Company $company, $prefix_account)
    {
        return self::find()
                ->where(['not', ['credit' => null]])
                ->andWhere(['credit_company_id' => $company->id,'credit_company_tin' => $company->tin, 'prefix_account' => $prefix_account,])
                ->sum('credit') + 0;
    }

    public function getTender()
    {
        return $this->hasOne(Tender::class, ['id' => 'tender_id']);
    }

    public function getAuction()
    {
        return $this->hasOne(Auction::class, ['id' => 'auction_id']);
    }

    public function getOrder()
    {
        return $this->hasOne(Order::class, ['id' => 'order_id']);
    }

    public function getContract()
    {
        return $this->hasOne(Contract::class, ['id' => 'contract_id']);
    }

}
