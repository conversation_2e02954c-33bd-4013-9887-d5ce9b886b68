<?php


namespace api\modules\common\resources;


use common\models\Classifier;
use common\models\query\NotDeletedFromCompanyQuery;

class ClassifierResource extends Classifier
{
    public function fields()
    {
        return [
            'id',
            'title_ru',
            'title_en',
            'title_uz',
            'title_uzk',
            'code',
            'type',
            'classifier_category_id',
        ];
    }
    
    public function extraFields()
    {
        return [
            'classifierCategory',
            'planScheduleClassifiers'
        ];
    }


    public function getClassifierCategory()
    {
        return $this->hasOne(ClassifierCategoryResource::class, ['id' => 'classifier_category_id']);
    }

    public function getPlanScheduleClassifiers()
    {
        return $this->hasMany(PlanScheduleClassifierResource::class, ['classifier_id' => 'id']);
    }

    public function getProperties()
    {
        return $this->hasMany(ClassifierPropertiesResource::class, ['classifier_id' => 'id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}