<?php


namespace api\modules\auction\resources;


use common\enums\TenderEnum;
use common\models\TenderModeratorLog;

class TenderModeratorLogResource extends TenderModeratorLog
{
    public function fields()
    {
        return [
            'created_at', 'description', 'moderator_pinfl'
        ];
    }

    public static function create($id, $state, $description){

        $log = new self();
        $log->auction_id = $id;
        $log->state = $state;
        $log->description = $description;
        $log->moderator_pinfl = \Yii::$app->user->identity->username;

    }

}