<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;
use common\models\Classifier;
use common\models\ClassifierCategory;

class ProductFilter extends BaseRequest
{
    public $search;
    public $classifier_id;
    public $classifier_category_id;
    public $price_from;
    public $price_to;

    public function rules()
    {
        return [
            [['search'], 'safe'],
            [['price_from', 'price_to'], 'number'],
            [['classifier_id', 'classifier_category_id'], 'integer'],

            ['classifer_id', 'exist', 'targetClass' => Classifier::class, 'targetAttribute' => 'id'],
            ['classifer_category_id', 'exist', 'targetClass' => ClassifierCategory::class, 'targetAttribute' => 'id'],
        ];
    }

    public function getResult()
    {
        $query = ProductResource::find()
            ->where(['product.state' => ProductEnum::SHOP_STATE_ACTIVE])
            ->orderBy("product.id desc");

        $query->andFilterWhere(['product.classifier_id' => $this->classifier_id]);
        $query->andFilterWhere(['product.classifier_category_id' => $this->classifier_category_id]);
        $query->andFilterWhere(['product.title' => $this->search]);

        if ($this->price_from) {
            $query->andWhere(['>=', 'product.price', $this->price_from]);
        }

        if ($this->price_to) {
            $query->andWhere(['<=', 'product.price', $this->price_to]);
        }

        return paginate($query);
    }
}
