<?php

namespace api\modules\backend\filters;

use api\components\BaseRequest;
use api\modules\auction\resources\AuctionResource;
use common\enums\AuctionEnum;
use common\traits\SoftDelete;
use Yii;

class AuctionFilter extends BaseRequest
{
  use SoftDelete;

  public $id;

  public function rules()
  {
    // rules regarding all props from /common/migrations/db/m231115_193117_create_auction_table.php
    return [
      ['id', 'safe'],
      ['company_id', 'safe'],
      ['classifier_id', 'safe'],
      ['status', 'safe'],
      ['total_sum', 'safe'],
      ['cancel_reason', 'safe'],
      ['auction_end', 'safe'],
      ['cancel_date', 'safe'],
      ['payment_status', 'safe'],
      ['payment_date', 'safe'],
      ['delivery_period', 'safe'],
      ['payment_period', 'safe'],
      ['receiver_email', 'safe'],
      ['receiver_phone', 'safe'],
      ['region_id', 'safe'],
      ['zip_code', 'safe'],
      ['address', 'safe'],
      ['created_at', 'safe'],
      ['updated_at', 'safe'],
      ['deleted_at', 'safe'],
      ['created_by', 'safe'],
      ['updated_by', 'safe'],
    ];
  }

  public function getResult()
  {
    $params = Yii::$app->request->queryParams;
    $query = AuctionResource::find()
      ->with(['classifierCategory'])
      ->where(['status' => AuctionEnum::STATUS_MODERATING])
      ->orderBy("auction.id desc");

    // if ($params['name']) {
    //   $query->andWhere([
    //     'or',
    //     ['auction.id' => $params['name']],
    //     ['like', 'category_translate.title', $params['name']]
    //   ]);
    // }

    // if ($params['summa_from']) {
    //   $query->andWhere(['>=', 'auction.total_sum', $params['summa_from']]);
    // }

    // if ($params['summa_to']) {
    //   $query->andWhere(['<=', 'auction.total_sum', $params['summa_to']]);
    // }

    // if ($params['region_id']) {
    //   $query->andWhere(['auction.region_id' => $params['region_id']]);
    // }

    // if ($params['district_id']) {
    //   $query->andWhere(['auction.region_id' => $params['district_id']]);
    // }

    // if ($params['customer_tin']) {
    //   $query->andWhere(['company.tin' => $params['customer_tin']]);
    // }

    // if ($params['classifier_category_id']) {
    //   $query->andWhere(['auction.classifier_category_id' => $params['classifier_category_id']]);
    // }

    // if ($params['start_date']) {
    //   $query->andWhere(['>=', 'DATE(auction.auction_end)', $params['start_date']]);
    // }

    // if ($params['end_date']) {
    //   $query->andWhere(['<=', 'DATE(auction.auction_end)', $params['end_date']]);
    // }

    return paginate($query);
  }
}
