<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderCommissionVoteResource;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;
use yii\web\ForbiddenHttpException;

class TenderUpdateChairmanForm extends BaseRequest
{
    public TenderResource $model;
    public TenderCommissionVoteResource $tenderVoting;
    public $vote;
    public $description;

    public function rules()
    {
        return [
            ['vote','required', 'message' => t('{attribute} yuborish majburiy')],
            ['vote','integer'],
            ['vote','checkVote'],
            ['description','required','when'=>function($model) {
                return $model->vote === TenderEnum::VOTE_NO || $model->vote === "0";
            }],
            ['description', 'string', 'max' => 255]
        ];
    }
    public function checkVote(){
        if($this->vote == TenderEnum::VOTE_YES || $this->vote == TenderEnum::VOTE_NO){
            return true;
        } else {
            $this->addError("vote", t("Ovoz berish qiymatlari 1 yoki 0 bo'lishi kerak"));
            return false;
        }
    }

    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;
        $this->tenderVoting = new TenderCommissionVoteResource();

        parent::__construct($params);
    }

    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        if($this->model->state != TenderEnum::STATE_READY_FOR_CHAIRMAN){
            $this->addError("description", t("Tender Rais uchun tasdiqlash holatida emas"));
            return false;
        }

        $commissionId = \Yii::$app->user->identity->commissionMemberId;
        $tenderCom = $this->model->getCommissionMember($commissionId);
        if($tenderCom->role != TenderEnum::ROLE_CHAIRMAN){
            $this->addError("description", t("Rais ro'li uchun ruxsat mavjud"));
            return false;
        }

        if($this->vote == TenderEnum::VOTE_YES){
            $this->model->state = TenderEnum::STATE_READY_TO_PRESENT;
            if(!$this->model->save()){
                $transaction->rollBack();
                $this->addErrors($this->model->errors);
                return false;
            }
        } else {
            $this->model->state = TenderEnum::STATE_REJECT_COMMISSIONS;
            if(!$this->model->save()){
                $transaction->rollBack();
                $this->addErrors($this->model->errors);
                return false;
            }
        }

        $this->tenderVoting->tender_id = $this->model->id;
        $this->tenderVoting->status = TenderEnum::STATUS_ACTIVE;
        $this->tenderVoting->commission_member_id = $commissionId;
        $this->tenderVoting->role = $tenderCom->role;
        $this->tenderVoting->vote = $this->vote;
        $this->tenderVoting->description = $this->description;

        if($this->tenderVoting->save()){
            if($this->vote == TenderEnum::VOTE_NO){
                TenderCommissionVoteResource::updateAll(
                    ['status' => TenderEnum::STATUS_DELETED],
                    'tender_id =' . $this->model->id . " AND status=".TenderEnum::STATUS_ACTIVE
                );
            }
            $transaction->commit();
            return true;
        } else {
            $transaction->rollBack();
            $this->addErrors($this->tenderVoting->errors);
            return false;
        }
    }

}