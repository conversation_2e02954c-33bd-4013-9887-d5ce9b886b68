<?php
$days  =array_keys($data);
$items  =array_values($data);

$labels = json_encode($days);
$items = json_encode($items);


?>

<div class="ibox float-e-margins">
    <style>

        canvas {
            width: 100%;
        }
    </style>
    <div class="ibox-title">
        <h5><?=t("Kun bo'yicha shartnomalar narxi")?></h5>
        <div class="ibox-tools">

            <div class="row" style="margin: 0!important;display:inline !important;">
                <?php use yii\helpers\Html;
                use yii\widgets\ActiveForm;

                $form = ActiveForm::begin([
                    'method' => 'get',
                    'action' => ['chart'], // Ensure this matches your route
                ]); ?>
                <div class="row" style="margin-left: 10px;">
                    <div class="col-md-6">
                        <div class="row">
                            <div class="col-md-6">

                                 <?= Html::dropDownList('year', $year,
                                    \backend\helpers\DateHelper::yearList(),
                                    [
                                        'class' => 'form-control',
                                        'prompt' => t('Yil')
                                    ]
                                ); ?>
                            </div>
                            <div class="col-md-6">
                                  <?= Html::dropDownList('month', $month,
                                    \backend\helpers\DateHelper::monthList(),
                                    [
                                        'class' => 'form-control',
                                        'prompt' => t('Oy')
                                    ]
                                ); ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2"  >
                        <?= Html::submitButton(t('Search'), ['class' => 'btn btn-primary', 'style' => 'width: 100%']) ?>
                    </div>

                </div>

                <?php ActiveForm::end(); ?>
            </div>

        </div>
    </div>
    <div class="row">
        <canvas id="myChart"></canvas>
    </div>

</div>

<script>
    const ctx = document.getElementById('myChart').getContext('2d');
    const myChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels:
                <?php echo $labels; ?>
             ,
            datasets: [{
                label: 'Kunlik Ma\'lumotlar',
                data:  <?php echo $items; ?> , // O'zingizning ma'lumotlaringiz
                borderColor: 'rgba(75, 192, 192, 1)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderWidth: 2,
                fill: true,
            }]
        },
        options: {
            responsive: true,
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'day',
                        tooltipFormat: 'yyyy-MM-dd',
                        displayFormats: {
                            day: 'yyyy-MM-dd'
                        }
                    }
                },
                y: {
                    beginAtZero: true
                }
            }
        }
    });
</script>
