<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;
use common\models\Region;
use Yii;
use yii\data\ActiveDataProvider;

class ProductListFilter extends BaseRequest
{
    public $classifier_category_id;
    public $classifier_id;
    public $company_id;
    public $state;
    public $no_quantity;
    public $status;
    public $title;
    public $platform_display;
    public $country_id;
    public $from_price;
    public $to_price;
    public $expensive;
    public $cheap;
    public $recently_updated;
    public $popular;
    public $regions=[];
    public $pageNo = 0;
    public $pageSize = 10;
    public function rules()
    {
        return [
            [['pageNo', 'pageSize'], 'integer'],
            [['classifier_category_id','classifier_id','company_id','state','status','no_quantity','platform_display','title','country_id','from_price','to_price','regions','expensive','cheap','recently_updated','popular'], 'safe']
        ];
    }

    public function getResult()
    {

        $model = ProductResource::find();
        if ($this->classifier_category_id) {
            $model->andWhere(['classifier_category_id' => $this->classifier_category_id]);
        }
        if ($this->classifier_id) {
            $model->andWhere(['classifier_id' => $this->classifier_id]);
        }
        if ($this->company_id) {
            $model->andWhere(['company_id' => $this->company_id]);
        }
        if ($this->state) {
            $model->andWhere(['state' => $this->state]);
        }
        if ($this->status) {
            $model->andWhere(['status' => $this->status]);
        }
        if ($this->platform_display) {
            $model->andWhere(['platform_display' => $this->platform_display]);
        }
        if ($this->title) {
            $model->andWhere(['like', 'title', $this->title]);
        }
        if ($this->no_quantity) {
            $model->andWhere(['=','quantity',0]);
        }
        if ($this->expensive) {
            $model->orderBy(['unit_price' =>SORT_DESC]);
        }
        if ($this->cheap) {
            $model->orderBy(['unit_price' =>SORT_ASC]);
        }
        if ($this->recently_updated) {
            $model->orderBy(['updated_at' =>SORT_DESC]);
        }
        if ($this->popular) {
            $model->leftJoin('classifier', '`classifier`.`id` = `product`.`classifier_id`');
            $model->orderBy(['classifier.saled' =>SORT_DESC]);
        }
        if ($this->country_id) {
            $model->andWhere(['country_id' => $this->country_id]);
        }
        if ($this->from_price) {
            $model->andWhere(['>=','unit_price' ,$this->from_price]);
        }
        if ($this->to_price) {
            $model->andWhere(['=<','unit_price' ,$this->to_price]);
        }
        if ($this->regions) {
            $model->leftJoin('product_region', '`product_region`.`product_id` = `product`.`id`');
            $model->andWhere(['in', 'product_region.region_id' ,$this->regions]);
        }

        $model->andWhere(['company_id' =>Yii::$app->user->identity->company_id]);

//        if (!$this->state){
//            $model->andWhere(['>=','inactive_date' , date("Y-m-d H:i:s")]);
//        }

        $model->orderBy(['id'=>SORT_DESC]);
        return paginate($model);

    }
}
