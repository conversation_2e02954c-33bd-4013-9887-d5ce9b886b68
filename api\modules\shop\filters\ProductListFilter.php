<?php

namespace api\modules\shop\filters;

use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\shop\resources\ProductResource;
use api\modules\shop\resources\ProductDraftResource;
use common\enums\ProductEnum;
use common\models\Region;
use Yii;
use yii\data\ActiveDataProvider;

class ProductListFilter extends BaseRequest
{
    public $classifier_category_id;
    public $classifier_id;
    public $company_id;
    public $state;
    public $no_quantity;
    public $status;
    public $title;
    public $platform_display;
    public $country_id;
    public $from_price;
    public $to_price;
    public $expensive;
    public $cheap;
    public $recently_updated;
    public $popular;
    public $regions=[];
    public $pageNo = 0;
    public $pageSize = 10;
    public function rules()
    {
        return [
            [['pageNo', 'pageSize'], 'integer'],
            [['classifier_category_id','classifier_id','company_id','state','status','no_quantity','platform_display','title','country_id','from_price','to_price','regions','expensive','cheap','recently_updated','popular'], 'safe']
        ];
    }

    public function getResult()
    {
        // Product ma'lumotlarini olish
        $productQuery = $this->buildProductQuery();

        // ProductDraft ma'lumotlarini olish
        $productDraftQuery = $this->buildProductDraftQuery();

        // Ikki query natijalarini birlashtirish
        $products = $productQuery->all();
        $productDrafts = $productDraftQuery->all();

        // Ma'lumotlarni birlashtirish va type qo'shish
        $combinedResults = [];

        foreach ($products as $product) {
            $productArray = $product->toArray();
            $productArray['source_type'] = 'product';
            $combinedResults[] = $productArray;
        }

        foreach ($productDrafts as $draft) {
            $draftArray = $draft->toArray();
            $draftArray['source_type'] = 'product_draft';
            $combinedResults[] = $draftArray;
        }

//        // Tartiblash (id bo'yicha kamayish tartibida)
//        usort($combinedResults, function($a, $b) {
//            return $b['id'] - $a['id'];
//        });


//        paginate($combinedResults);

        // Pagination qo'llash
        $offset = $this->pageNo * $this->pageSize;
        $paginatedResults = array_slice($combinedResults, $offset, $this->pageSize);

        return [
            'data' => $paginatedResults,
            'pagination' => [
                'page' => $this->pageNo,
                'pageSize' => $this->pageSize,
                'totalCount' => count($combinedResults),
                'totalPages' => ceil(count($combinedResults) / $this->pageSize)
            ]
        ];
    }

    private function buildProductQuery()
    {
        $model = ProductResource::find();
        $this->applyFilters($model, 'product');
        return $model;
    }

    private function buildProductDraftQuery()
    {
        $model = ProductDraftResource::find();
        $this->applyFilters($model, 'product_draft');
        return $model;
    }

    private function applyFilters($query, $tableName)
    {
        $query->andWhere(["{$tableName}.deleted_at" => null]);

        if ($this->classifier_category_id) {
            $query->andWhere(['classifier_category_id' => $this->classifier_category_id]);
        }
        if ($this->classifier_id) {
            $query->andWhere(['classifier_id' => $this->classifier_id]);
        }
        if ($this->company_id) {
            $query->andWhere(['company_id' => $this->company_id]);
        }
        if ($this->state) {
            $query->andWhere(['state' => $this->state]);
        }
        if ($this->status) {
            $query->andWhere(['status' => $this->status]);
        }
        if ($this->platform_display) {
            $query->andWhere(['platform_display' => $this->platform_display]);
        }
        if ($this->title) {
            $query->andWhere(['like', 'title', $this->title]);
        }
        if ($this->no_quantity) {
            $query->andWhere(['=','quantity',0]);
        }
        if ($this->country_id) {
            $query->andWhere(['country_id' => $this->country_id]);
        }
        if ($this->from_price) {
            $query->andWhere(['>=','unit_price' ,$this->from_price]);
        }
        if ($this->to_price) {
            $query->andWhere(['=<','unit_price' ,$this->to_price]);
        }
        if ($this->regions) {
            $regionTable = $tableName === 'product' ? 'product_region' : 'product_region'; // Hozircha ikkalasi ham product_region ishlatadi
            $query->leftJoin($regionTable, "`{$regionTable}`.`product_id` = `{$tableName}`.`id`");
            $query->andWhere(['in', "{$regionTable}.region_id" ,$this->regions]);
        }

        $query->andWhere(['company_id' => Yii::$app->user->identity->company_id]);

        if ($this->expensive) {
            $query->orderBy(['unit_price' => SORT_DESC]);
        } elseif ($this->cheap) {
            $query->orderBy(['unit_price' => SORT_ASC]);
        } elseif ($this->recently_updated) {
            $query->orderBy(['updated_at' => SORT_DESC]);
        } elseif ($this->popular && $tableName === 'product') {
            $query->leftJoin('classifier', '"classifier"."id" = "product"."classifier_id"');
            $query->orderBy(['classifier.saled' => SORT_DESC]);
        } else {
            $query->orderBy(['id' => SORT_DESC]);
        }

        return $query;
    }
}
