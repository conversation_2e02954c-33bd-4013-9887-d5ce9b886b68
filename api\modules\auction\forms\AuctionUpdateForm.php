<?php

namespace api\modules\auction\forms;

use api\components\BaseRequest;
use api\modules\auction\resources\AuctionClassifierResource;
use api\modules\auction\resources\AuctionConditionResource;
use api\modules\auction\resources\AuctionDetailResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\common\resources\PlanScheduleClassifierCreateResource;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\AuctionEnum;
use common\enums\CompanyEnum;
use common\enums\StatusEnum;
use common\models\auction\AuctionCondition;
use common\models\auction\AuctionFile;
use common\models\auction\AuctionHistory;
use common\models\Bmh;
use common\models\Company;
use Yii;

class AuctionUpdateForm extends BaseRequest
{
    public AuctionDetailResource $model;

    public $plan_schedule_id;
    public $delivery_period;
    public $address;
    public $account;
    public $pkcs7;
    public $delivery_basis;
    public $responsible_person;
    public $responsible_person_phone;
    public $auction_classifiers = [];
    public $auction_files = [];
    public $auction_conditions = [];


    public function __construct(AuctionDetailResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return  [
            [['plan_schedule_id', 'auction_classifiers', 'auction_conditions', 'delivery_period', 'address', 'account', 'delivery_basis', 'responsible_person', 'responsible_person_phone'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['plan_schedule_id'], 'integer'],
            [['auction_classifiers', 'auction_files', 'auction_conditions'], 'safe'],
            [['address', 'responsible_person', 'responsible_person_phone'], 'string', 'max' => 255],
            [['account'], 'string', 'min' => 20, 'max' => 20],
            [['delivery_period',], 'integer', 'min' => 7],
            [['plan_schedule_id'], 'exist', 'skipOnError' => true, 'targetClass' => PlanScheduleResource::class, 'targetAttribute' => ['plan_schedule_id' => 'id']],
        ];
    }


    public function getResult()
    {
        /**
         * @var $company Company
         */
        if (!in_array($this->model->status, [AuctionEnum::STATUS_MODERATING, AuctionEnum::STATUS_REJECTED, AuctionEnum::STATUS_NOT_HELD])) {
            $this->addError("id", t("Auksion holati aktiv yoki tugatilgan bo'lsa tahrirlash mumkin emas"));
            return false;
        }

        $transaction = \Yii::$app->db->beginTransaction();
        $user = Yii::$app->user->identity;
        $company = $user->company;
        $model = $this->model;
        $totalSum = 0;

        $isBudget = $user->isBudget;
        if ($isBudget) {
            $this->addError("error", t("Ruxsat mavjud emas"));
            return false;
        }

        $model->status = AuctionEnum::STATUS_MODERATING;
        $model->receiver_email = $user->email;
        $model->receiver_phone = $company->phone;

        $model->delivery_period = $this->delivery_period;
        $model->address = $this->address;
        $model->account = $this->account;
        $model->auction_end = null;
        $model->delivery_basis = $this->delivery_basis;
        $model->responsible_person = $this->responsible_person;
        $model->responsible_person_phone = $this->responsible_person_phone;

        if (!$model->save()) {
            throw new \yii\web\HttpException(400, t("Auksionni saqlashda xatolik"));
        }

        AuctionClassifierResource::updateAll(['status' => StatusEnum::STATUS_DELETED], ['auction_id' => $this->model->id, 'status' => StatusEnum::STATUS_ACTIVE]);
        foreach ($this->auction_classifiers as $classifier) {

            if (!isset($classifier['description']) || $classifier['description'] == null) {
                $transaction->rollBack();
                $this->addError("description", t("Reja jadvali tavsifi yuborish kerak"));
                return false;
            }
            if (!isset($classifier['id']) || $classifier['id'] == null) {
                $transaction->rollBack();
                $this->addError("id", t("Reja jadvali id yuborish kerak"));
                return false;
            }

            $clsObject = $classifier['classifier_id'];
            $cls = null;
            if (isset($clsObject) && $clsObject != null && isset($clsObject)) {
                $cls = ClassifierResource::findOne($clsObject);
            }

            if ($cls === null) {
                $this->addError('classifier_id', t("Maxsulot guruhi topilmadi"));
                $transaction->rollBack();
                return false;
            }

            $planScheduleClassifier = PlanScheduleClassifierCreateResource::find()->where([
                'plan_schedule_id' => $this->plan_schedule_id,
                'classifier_id' => $cls->id,
                'status' => StatusEnum::STATUS_ACTIVE,
                'id' => $classifier['id']
            ])->one();
            if (!$planScheduleClassifier) {
                $this->addError('classifier_id', t("Maxsulot reja jadvali topilmadi"));
                $transaction->rollBack();
                return false;
            }

            if (isset($classifier['auction_classifier_id']) && $classifier['auction_classifier_id'] > 0) {
                $auction_classifier = AuctionClassifierResource::findOne(['auction_classifier_id' => $classifier['auction_classifier_id']]);
            } else {
                $auction_classifier = new AuctionClassifierResource();
                $auction_classifier->auction_id = $model->id;
                $auction_classifier->plan_schedule_id = $planScheduleClassifier->plan_schedule_id;
                $auction_classifier->plan_schedule_classifier_id = $planScheduleClassifier->id;
                $auction_classifier->order = isset($classifier['order']) ? $classifier['order'] : 0;
            }
            $auction_classifier->status = StatusEnum::STATUS_ACTIVE;
            $auction_classifier->classifier_id = $cls->id;
            $auction_classifier->description = $planScheduleClassifier->description;
            $auction_classifier->quantity = $classifier['count'];
            $auction_classifier->price = $classifier['price'];
            $auction_classifier->total_sum = $classifier['count'] * $classifier['price'];

            if (!$auction_classifier->save()) {
                $this->addErrors($auction_classifier->errors);
                $transaction->rollBack();
                return false;
            }

            if ($planScheduleClassifier && $planScheduleClassifier->count >= $classifier['count']) {
                $planScheduleClassifier->update([
                    'count_used' => $classifier['count'],
                    'count_live' => $planScheduleClassifier->count - $classifier['count'],
                ]);

                if ($planScheduleClassifier->count_live == 0) {
                    $planScheduleClassifier->update([
                        'status' => 400, // not active
                    ]);
                }
            }

            $totalSum += $auction_classifier->total_sum;
        }

        AuctionFile::updateAll(['deleted_at' => date("Y-m-d H:i:s"), 'updated_by' => $user->id], ['auction_id' => $this->model->id]);
        foreach ($this->auction_files as $fileId) {

            $auctionFile = new AuctionFile();
            $auctionFile->auction_id = $model->id;
            $auctionFile->file_id = $fileId;
            if (!$auctionFile->save()) {
                $this->addErrors($auctionFile->errors);
                $transaction->rollBack();
                return false;
            }

        }

        AuctionCondition::updateAll(['status' => StatusEnum::STATUS_DELETED], ['auction_id' => $this->model->id, 'status' => StatusEnum::STATUS_ACTIVE]);
        if ($this->auction_conditions && count($this->auction_conditions) > 0) {
            foreach ($this->auction_conditions as $condition) {
                if (isset($condition['id']) && $condition['id'] > 0) {
                    $auction_condition = AuctionConditionResource::findOne(['id' => $condition['id'], 'auction_id' => $model->id]);
                    if (!$auction_condition) {
                        $this->addError('auction_conditions', t("Shart topilmadi"));
                        $transaction->rollBack();
                        return false;
                    }
                } else {
                    $auction_condition = new AuctionConditionResource();
                    $auction_condition->auction_id = $model->id;
                }
                $auction_condition->status = StatusEnum::STATUS_ACTIVE;
                $auction_condition->condition = $condition['condition'];
                $auction_condition->text = $condition['text'];
                if (!$auction_condition->save()) {
                    $this->addErrors($auction_condition->errors);
                    $transaction->rollBack();
                    return false;
                }
            }
        }

        $model->total_sum = $totalSum;
        if (!$model->save()) {
            $this->addErrors($model->errors);
            $transaction->rollBack();
            return false;
        }

        $bmh = Bmh::getAmount();
        if ($company->organization_type == CompanyEnum::NO_BYUDJET) {
            $bmh = $bmh * 25000;

            if ($totalSum >= $bmh) {
                $transaction->rollBack();
                $this->addError("error", t("Tovarlarning qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining yigirma besh ming baravarigacha bo‘lgan miqdorni tashkil etadi"));
                return false;
            }

        } else {
            $bmh = $bmh * 6000;
            if ($totalSum >= $bmh) {
                $transaction->rollBack();
                $this->addError("error", t("Tovarlarning qiymati bir shartnoma bo‘yicha bazaviy hisoblash miqdorining olti ming baravarigacha bo‘lgan miqdorni tashkil etadi"));
                return false;
            }
        }

        $zalog_sum = $model->total_sum * env('ZALOG_PERCENT', 0.03);
        $commission_sum = $model->total_sum * env('COMMISSION_PERCENT', 0.0015);
        $commission_sum = $commission_sum > 1000000 ? 1000000 : $commission_sum;
        $total_block_sum = $zalog_sum + $commission_sum;

        $company->lastCompanyBalance->calculateBalance();

        if ($company->availableBalance < $total_block_sum) {
            $transaction->rollBack();
            $this->addError('error', t('"Недостаточно средств на балансе покупателя."'));
            return false;
        }


        $history = new AuctionHistory();
        $history->auction_id = $this->model->id;
        $history->user_id = $user->id;
        $history->status = $this->model->status;
        $history->comment = t("Tahrirlandi. Moderatsiyaga yuborildi.");
        $history->created_at = date("Y-m-d H:i:s");
        if (!$history->save()) {
            $this->addErrors($history->errors);
            $transaction->rollBack();
            return false;
        }

        $transaction->commit();
        return true;
    }
}
