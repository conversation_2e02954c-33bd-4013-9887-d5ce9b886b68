<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\shop\resources\FavoriteResource;
use api\modules\shop\resources\ProductCommentResource;
use common\enums\ContractEnum;
use common\models\Contract;
use common\models\shop\Product;
use yii\base\Exception;

class ProductCommentForm extends BaseRequest
{

    public ProductCommentResource $model;

    public $product_id;
    public $comment;


    public function __construct(ProductCommentResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [ ['product_id'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [ ['comment'], 'safe'],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],

        ];
    }


    /**
     * @throws Exception
     * @throws \yii\db\Exception
     */
    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();


        $contract  = Contract::find()->leftJoin('order','order.id=contract.order_id')
            ->andWhere(['order.user_id'=>\Yii::$app->user->id])
            ->andWhere(['order.product_id'=>$this->product_id])
            ->andWhere(['contract.status'=>ContractEnum::STATUS_DONE])->all();

        if (!$contract){
            throw new Exception(t("Siz ushbu mahsulotdan sotib olmagansiz"));
        }

        $this->model->user_id = \Yii::$app->user->id;
        $this->model->created_at = date("Y-m-d H:i:s");
        $att = $this->attributes;
        $this->model->setAttributes($att,false);

        if($this->model->attributes && $this->model->validate() && $this->model->save()){
            $transaction->commit();
            return true;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}