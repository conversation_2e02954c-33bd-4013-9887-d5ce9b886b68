<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\client\resources\TenderDiscussionResource;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;

class TenderProtestFilter extends BaseRequest
{

    public TenderResource $tender;

    public function __construct(TenderResource $model, $params = [])
    {
        $this->tender = $model;

        parent::__construct($params);
    }

    public function getResult()
    {
        if(!in_array($this->tender->state, [TenderEnum::STATE_MADE_PROTOCOL, TenderEnum::STATE_DISCUSSION_END_MADE_PROTOCOL])){
            $this->addError("error", t("Tender muxokama xolatida emas"));
            return false;
        }
        if($this->tender->commissionMemberRole != TenderEnum::ROLE_CHAIRMAN){
            $this->addError("error", t("Rais uchun ruxsat mavjud"));
            return false;
        }
        return paginate(TenderDiscussionResource::find()->where(['tender_id' => $this->tender->id, 'status' => TenderEnum::STATUS_ACTIVE]));

    }
}