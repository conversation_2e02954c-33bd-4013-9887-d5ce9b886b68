<?php

namespace api\modules\tender\forms;

use api\modules\tender\resources\CommissionGroupResource;
use api\modules\tender\resources\CommissionMemberResource;
use common\enums\TenderEnum;
use Yii;
use api\components\BaseRequest;
use api\modules\tender\resources\CommissionGroupMemberResource;
use common\enums\StatusEnum;
use yii\base\Exception;


class CommissionGroupMemberForm extends BaseRequest {

    public $commission_group_id;
    public $commission_member_id;
    public $role;

    public CommissionGroupMemberResource $model;

    public function __construct(CommissionGroupMemberResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules(){
        return [
            //[['commission_group_id', 'commission_member_id'], 'unique', 'targetAttribute' => ['commission_group_id', 'commission_member_id']],
            [['commission_group_id', 'commission_member_id', 'role'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['commission_group_id', 'commission_member_id', 'role'], 'safe'],
            [['commission_group_id'], 'exist', 'skipOnError' => true, 'targetClass' => CommissionGroupResource::class, 'targetAttribute' => ['commission_group_id' => 'id']],
            [['commission_member_id'], 'exist', 'skipOnError' => true, 'targetClass' => CommissionMemberResource::class, 'targetAttribute' => ['commission_member_id' => 'id']],
        ];
    }


    public function getResult()
    {
        $companyId = \Yii::$app->user->identity->company_id;
        if(!CommissionMemberResource::find()->where(['id' => $this->commission_member_id, 'status' => TenderEnum::STATUS_ACTIVE])->exists()){
//            throw new Exception(t("Komissiya azosi topilmadi yoki aktivlashtirilmagan"));
            $this->addError('commission_member_id',t("Komissiya azosi topilmadi yoki aktivlashtirilmagan"));
            return false;
        }

        if(!CommissionGroupResource::find()->where(['id' => $this->commission_group_id, 'company_id' => $companyId])->exists()){
            $this->addError('commission_member_id',t("Komissiya guruhi topilmadi"));
            return false;
//            throw new Exception(t(""));
        }
        if(CommissionGroupMemberResource::find()->notDeleted()->andWhere(['commission_group_id' => $this->commission_group_id, 'commission_member_id' => $this->commission_member_id, 'role' => $this->role, 'status' => StatusEnum::STATUS_ACTIVE])->exists()){
//            throw new Exception(t("Komissiya a'zosi avval bu guruhga biriktirilgan"));
            $this->addError('commission_member_id',t("Komissiya a'zosi avval bu guruhga biriktirilgan"));
            return false;
        }

        $this->model->commission_group_id = $this->commission_group_id;
        $this->model->commission_member_id = $this->commission_member_id;
        $this->model->role = $this->role;
        $this->model->status = StatusEnum::STATUS_ACTIVE;

        if($this->model->save()){
            return true;
        } else {
            $this->addErrors($this->model->errors);
            return false;
        }
    }
}
?>