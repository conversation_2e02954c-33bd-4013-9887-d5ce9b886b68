<?php

namespace backend\controllers;

use common\enums\BankServiceEnum;
use common\enums\CompanyTransactionEnum;
use common\models\bank\BankTransactionOut;
use common\models\Company;
use common\models\CompanyBankAccount;
use common\models\CompanyTransaction;
use Yii;
use common\models\CompanyTransactionRefunds;
use common\models\CompanyTransactionRefundsSearch;
use yii\helpers\ArrayHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * CompanyTransactionRefundsController implements the CRUD actions for CompanyTransactionRefunds model.
 */
class CompanyTransactionRefundsController extends BackendController
{
    /**
     * Lists all CompanyTransactionRefunds models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new CompanyTransactionRefundsSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionModerator($status = CompanyTransactionEnum::REFUNDS_STATUS_NEW)
    {
        $searchModel = new CompanyTransactionRefundsSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, $status);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionModeratorRejected($status = CompanyTransactionEnum::REFUNDS_STATUS_REJECTED)
    {
        $searchModel = new CompanyTransactionRefundsSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, $status);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionModeratorDone($status = CompanyTransactionEnum::REFUNDS_STATUS_ACCEPT)
    {
        $searchModel = new CompanyTransactionRefundsSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, $status);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionModeratorAccept($id)
    {

        $model = $this->findModel($id);
        if ($model->status != CompanyTransactionEnum::REFUNDS_STATUS_NEW) {
            \Yii::$app->session->setFlash("error", t("Holati yangi emas!!!"));
            return $this->render('view', [
                'model' => $model,
            ]);
        }
        $transaction = \Yii::$app->db->beginTransaction();

        $model->status = CompanyTransactionEnum::REFUNDS_STATUS_ACCEPT;

        $company = Company::findOne($model->company_id);
        $account = CompanyBankAccount::find()->where(['company_id' => $company->id, 'account' => $model->account_number])->one();

        if ($model->save()) {

            $transactionOut = new BankTransactionOut();
            $transactionOut->amount = $model->sum;
            $transactionOut->purpose = $model->comment;
            $transactionOut->type = BankServiceEnum::BANK_TRANSACTION_TYPE_WITHDRAW;
            $transactionOut->company_transaction_id = $model->transaction_id;
            $transactionOut->service_id = (string)BankServiceEnum::SERVICE_ID;
            $transactionOut->receiver_account = $model->account_number;
            $transactionOut->receiver_mfo = $account->mfo;
            $transactionOut->company_id = $model->company_id;
            $transactionOut->company_tin = $company->pinfl ? $company->pinfl : $company->tin;
            $transactionOut->state = BankServiceEnum::BANK_TRANSACTION_TYPE_STATE_NEW;
            $transactionOut->created_at = date("Y-m-d H:i:s");

            if (!$transactionOut->save()) {
                $transaction->rollback();
                var_dump($transactionOut->errors);
                die;
                \Yii::$app->session->setFlash("error", t("Saqlashda xatolik!!!"));
                return $this->render('view', [
                    'model' => $model,
                ]);
            }

            $transaction->commit();
            \Yii::$app->session->setFlash("success", "Успешно moderating");
            return $this->render('view', [
                'model' => $model,
            ]);
        } else {
            \Yii::$app->session->setFlash("error", t("Saqlashda xatolik!!!"));
        }

        $transaction->rollBack();
        return $this->render('view', [
            'model' => $model,
        ]);


    }

    public function actionModeratorReject($id)
    {

        $model = $this->findModel($id);
        if ($model->status != CompanyTransactionEnum::REFUNDS_STATUS_NEW) {
            \Yii::$app->session->setFlash("error", t("Holati yangi emas!!!"));
            return $this->render('view', [
                'model' => $model,
            ]);
        }
        $transaction = \Yii::$app->db->beginTransaction();

        $model->status = CompanyTransactionEnum::REFUNDS_STATUS_REJECTED;

        if ($model->save()) {

            $old_transaction = CompanyTransaction::findOne($model->transaction_id);
            if (!$old_transaction) {
                \Yii::$app->session->setFlash("error", "Transaction topilmad !!!");
                return $this->render('view', [
                    'model' => $model,
                ]);
            }
            $company_transaction = new CompanyTransaction([
                'company_id' => $old_transaction->company_id,
                'amount' => $old_transaction->amount,
                'type' => CompanyTransactionEnum::TYPE_REFILL,
                'description' => Yii::t("main", "Moderator tomonidan bekor qilindi"),
                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
                'transaction_date' => date("Y-m-d H:i:s"),
            ]);

            if (!$company_transaction->save()) {
                \Yii::$app->session->setFlash("error", "Saqlashda xatolik!!!");
                return $this->render('view', [
                    'model' => $model,
                ]);
            }
            $old_transaction->reverted_id = $company_transaction->id;

            $transaction->commit();
            \Yii::$app->session->setFlash("success", "Успешно no moderating");
            return $this->render('view', [
                'model' => $model,
            ]);
        } else {
            \Yii::$app->session->setFlash("error", "Saqlashda xatolik!!!");
        }

        $transaction->rollBack();
        return $this->render('view', [
            'model' => $model,
        ]);

    }

    /**
     * Displays a single CompanyTransactionRefunds model.
     * @param int $id ID
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Finds the CompanyTransactionRefunds model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id ID
     * @return CompanyTransactionRefunds the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = CompanyTransactionRefunds::findOne($id)) !== null) {
            return $model;
        }
        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
