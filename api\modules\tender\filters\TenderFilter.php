<?php


namespace api\modules\tender\filters;


use api\components\BaseRequest;
use api\modules\tender\resources\TenderResource;

class TenderFilter extends BaseRequest
{

    public $lot;
    public $state;
    public $subState;

    public function rules()
    {
        return [
            [['state'], 'integer'],
            [['lot'], 'safe'],
        ];
    }

    public function __construct($state, $subState = [], $params = [])
    {
        $this->state = $state;
        $this->subState = $subState;
        parent::__construct($params);
    }

    public function getResult()
    {
        $model = TenderResource::find()->notDeletedAndFromCompany();
        if (count($this->subState) == 0) {
            $model->andWhere('state=' . $this->state);
        } else {
            $states = array_merge([$this->state], $this->subState);
            $model->andWhere(['in', 'state', $states]);
        }

        if ($this->lot) {
            $model->andWhere(['like', 'lot', $this->lot]);
        }

        $model->orderBy(['updated_at' => SORT_DESC]);

        return paginate($model);

    }
}