<?php

namespace app\modules\shop\resources;

use api\modules\common\resources\CompanyShortResource;
use api\modules\common\resources\FileResource;
use common\models\Contract;

class ContractExternalTenderResource extends Contract
{
    public function fields()
    {
        return [
            'created_at',
            'price' => function ($model) {
                return $model->price / 100;
            },
            'id',
            'number',
            'lot' => function ($model) {
                return $model->auction ? $model->auction->lot : null;
            },
            'customer',
            'producer',
            'request_count' => function ($model) {
                $auctionRequests = $model->auction ? $model->auction->auctionRequests : [];
                return count($auctionRequests);
            },
            'tender',
            'file',
        ];
    }


    public function getTender()
    {
        return $this->hasOne(TenderExternalResource::class, ['id' => 'tender_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'file_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getCustomer()
    {
        return $this->hasOne(CompanyShortResource::class, ['id' => 'customer_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProducer()
    {
        return $this->hasOne(CompanyShortResource::class, ['id' => 'producer_id']);
    }

}
