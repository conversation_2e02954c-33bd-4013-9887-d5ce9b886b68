<?php

namespace api\modules\shop\controllers;

use api\components\ApiController;
use api\modules\shop\filters\OrderRequestListFilter;
use api\modules\shop\forms\CustomerAcceptForm;
use api\modules\shop\forms\CustomerRejectForm;
use api\modules\shop\forms\NoOfferAcceptForm;
use api\modules\shop\forms\NoOfferRejectForm;
use api\modules\shop\forms\NoOfferRequestForm;
use api\modules\shop\forms\OneSidedAcceptForm;
use api\modules\shop\forms\OneSidedRejecttForm;
use api\modules\shop\forms\OrderRequestForm;
use api\modules\shop\resources\OrderRequestResource;
use api\modules\shop\resources\OrderResource;
use common\behaviors\RoleAccessBehavior;
use common\enums\PkcsEnum;
use Yii;

/**
 * Default controller for the `shop` module
 */
class OrderRequestController extends ApiController
{
    public function behaviors()
    {
        $parent = parent::behaviors();
        $parent['accessByRole'] = [
            'class' => RoleAccessBehavior::class,
            'rules' => [
                'index' => ['user'],
                'create' => ['user'],
                'no-offer-request' => ['user'],
                'no-offer-accept' => ['user'],
                'no-offer-reject' => ['user'],
                'customer-accept' => ['user'],
                'customer-reject' => ['user'],
                'one-sided-accept' => ['user'],
                'one-sided-reject' => ['user'],
            ],
        ];
        return $parent;
    }

    public function actionIndex()
    {
        return $this->sendResponse(
            new OrderRequestListFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionCreate()
    {
        return $this->sendResponse(
            new OrderRequestForm(new OrderRequestResource()),
            Yii::$app->request->bodyParams
        );
    }
    /**
     * buyurtmachi skidka so'rovi
    */
    public function actionNoOfferRequest()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new NoOfferRequestForm(),
            $decodedPkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_NO_OFFER_REQUEST
        );
    }
    /**
     *   buyurtmachini skidka so'rgan so'rovini yetkazib beruvci tasdiqlashi
     */
    public function actionNoOfferAccept(): array
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new NoOfferAcceptForm(),
            $decodedPkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_NO_OFFER_ACCEPT
        );
    }
    /**
     *   yetkazib beruvci qaytarashi
     */
    public function actionNoOfferReject(): array
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new NoOfferRejectForm(),
            $decodedPkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_NO_OFFER_REJECT
        );
    }
    /**
     *   buyurtmaci   bowlangich narxni tanlashi
     */
    public function actionCustomerAccept($id)
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new CustomerAcceptForm(),
            $decodedPkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_CUSTOMER_ACCEPT
        );
    }
    /**
     *   buyurtmaci zakazni atmen qilishi
     */
    public function actionCustomerReject($id)
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new CustomerRejectForm(),
            $decodedPkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_CUSTOMER_REJECT
        );
    }
    public function actionOneSidedAccept()
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new OneSidedAcceptForm(),
            $decodedPkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_ONE_SIDED_ACCEPT
        );
    }
    public function actionOneSidedReject($id)
    {
        $body = Yii::$app->request->bodyParams;
        $decodedPkcs = $this->verifyPkcs7($body);

        return $this->sendResponsePost(
            new OneSidedRejecttForm(),
            $decodedPkcs,
            $body['pkcs7'],
            PkcsEnum::PKCS7_TYPE_SHOP_ONE_SIDED_REJECT
        );
    }
    private function findOne($id)
    {
        $model = OrderRequestResource::findOne($id);

        return $model;
    }

}
