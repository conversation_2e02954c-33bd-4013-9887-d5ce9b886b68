<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\client\resources\TenderRequestResource;
use api\modules\client\resources\TenderRequirementsAnswerResource;
use api\modules\tender\resources\TenderQualificationSelectionResource;
use api\modules\tender\resources\TenderRequestRatingResource;
use api\modules\tender\resources\TenderRequirementsResource;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;
use yii\web\ForbiddenHttpException;

class TenderForSecretaryQualificationRateForm extends BaseRequest
{

    public TenderResource $model;

    public $tender_request_id;
    public $tender_qualification_selection_id;
    public $state;

    public function __construct(TenderResource $model, $params = [])
    {
        $this->model = $model;
        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [['tender_request_id', 'tender_qualification_selection_id', 'state'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['tender_request_id', 'tender_qualification_selection_id'], 'integer'],
            ['state', 'checkState'],
            [['tender_request_id'], 'exist', 'skipOnError' => true, 'targetClass' => TenderRequestResource::class, 'targetAttribute' => ['tender_request_id' => 'id']],
            [['tender_qualification_selection_id'], 'exist', 'skipOnError' => true, 'targetClass' => TenderQualificationSelectionResource::class, 'targetAttribute' => ['tender_qualification_selection_id' => 'id']],
        ];

    }

    public function checkState()
    {
        if ($this->state == TenderEnum::QUALIFIER_STATE_NO || $this->state == TenderEnum::QUALIFIER_STATE_YES) {
            return true;
        } else {
            $this->addError("vote", t("State qiymatlari, Muvofiq = 30, Muvofiq emas = 20"));
            return false;
        }
    }


    public function getResult()
    {
        if ($this->model->state != TenderEnum::STATE_READY_TO_RATING) {
            $this->addError("state", t("Baholash bosqichida emas"));
            return false;
        }

        $commissionId = \Yii::$app->user->identity->commissionMemberId;
        $tenderCom = $this->model->getCommissionMember($commissionId);
        if ($tenderCom->role != TenderEnum::ROLE_SECRETARY) {
            $this->addError("grades", t("Sekretar roli uchun ruxsat mavjud"));
            return false;
        }

        $transaction = \Yii::$app->db->beginTransaction();

        /**
         * @var $tQualification TenderQualificationSelectionResource
         */
        $tQualification = TenderQualificationSelectionResource::find()
            ->notDeleted()
            ->andWhere([
                'id' => $this->tender_qualification_selection_id,
                'tender_id' => $this->model->id,
                'tender_request_id' => $this->tender_request_id
            ])
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->one();
        if (!$tQualification) {
            $this->addError("tender_qualification_selection_id", t("Malaka tanlov topilmadi"));
            return false;
        }
        $tRequest = TenderRequestResource::find()->notDeleted()
            ->andWhere(['id' => $tQualification->tender_request_id, 'tender_id' => $this->model->id])
            ->andWhere(['status' => TenderEnum::STATUS_ACTIVE])
            ->one();
        if (!$tRequest) {
            $this->addError("state", t("Taklif topilmadi"));
            return false;
        }

        $tQualification->state = $this->state;
        if (!$tQualification->save()) {
            $transaction->rollBack();
            $this->addErrors($tQualification->errors);
            return false;
        }

        $transaction->commit();
        return true;
    }
}