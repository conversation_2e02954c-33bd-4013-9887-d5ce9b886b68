<?php

namespace app\modules\tender\resources;

use api\modules\tender\resources\TenderRequestValuesResource;
use common\models\TenderRequestValues;

class TenderRequestValuesForSecretaryResource extends TenderRequestValuesResource
{
    public function fields()
    {
        $parent = parent::fields();
        unset($parent['price']);
        $child = [
            'price' => function (TenderRequestValues $model) {
                $request = $model->tenderRequest;
                if ($request) {
                    if ($request->preference_local_producer == 1 && $request->is_preference_local_producer == 1) {
                        return $request->price * env('TENDER_DIFFERENCIAL_PERCENT', 0.85) / 100; // convert to tiyin from sum
                    }
                }
                return $model->price / 100; // convert to tiyin from sum
            }
        ];
        return array_merge($parent, $child);
    }
}