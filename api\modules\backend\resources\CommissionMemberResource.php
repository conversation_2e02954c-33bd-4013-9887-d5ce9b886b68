<?php

namespace api\modules\backend\resources;

use common\models\CommissionMember;
use common\models\query\NotDeletedFromCompanyQuery;

class CommissionMemberResource extends CommissionMember {

    public function fields (){
        return ['id', 'fullname', 'position'];
    }

    public function extraFields() {
        return [
            'commissionGroupMembers'
        ];
    }

    public function getCommissionGroupMembers (){
        return $this->hasMany(CommissionGroupMemberResource::class, ['commission_member_id' => 'id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}