<?php

namespace api\modules\tender\resources;

use common\models\CommissionGroupMember;
use common\models\query\NotDeletedFromCompanyQuery;

class CommissionGroupMemberResource extends CommissionGroupMember
{

    public function fields()
    {
        return [
            'id',
            'role',
            'commissionGroup'
        ];
    }
    public function extraFields()
    {
        return ['commissionMember'];
    }

    public function getCommissionGroup()
    {
        return $this->hasOne(CommissionGroupResource::class, ['id' => 'commission_group_id']);
    }

    public function getCommissionMember()
    {
        return $this->hasOne(CommissionMemberResource::class, ['id' => 'commission_member_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}