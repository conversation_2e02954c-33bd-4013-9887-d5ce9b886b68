<?php

namespace api\modules\auction\resources;

use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\CompanyResource;
use api\modules\common\resources\FileResource;
use api\modules\common\resources\RegionResource;
use api\modules\common\resources\CompanyShortResource;
use common\models\auction\Auction;
use common\models\auction\AuctionHistory;
use common\models\auction\AuctionOffer;

class AuctionMyLotsResource extends Auction
{
  public function fields()
  {
    return [
      'id',
      'lot',
      'status',
      'total_sum' => function($model){
        return $model->total_sum / 100;
      },
      'auction_end',
      'requestDate'
    ];
  }

  public function extraFields()
  {
    return [
      'classifierCategory',
      'companyShort'
    ];
  }

    public function getCompanyShort()
    {
        return $this->hasOne(CompanyShortResource::class, ['id' => 'company_id']);
    }

  public function getRequestDate(){
      $model = AuctionOffer::find()->where(['auction_id' => $this->id, 'company_id' => \Yii::$app->user->identity->company_id])->orderBy(['created_at' => SORT_DESC])->one();
      return $model->created_at;
  }

    public function getClassifierCategory()
    {
        return $this->hasOne(ClassifierCategoryResource::class, ['id' => 'classifier_category_id']);
    }


}
