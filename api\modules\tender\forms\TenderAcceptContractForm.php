<?php


namespace api\modules\tender\forms;



use api\components\BaseRequest;
use api\modules\common\resources\VirtualTransactionResource;
use api\modules\tender\resources\ContractResource;
use common\enums\CompanyTransactionEnum;
use common\enums\ContractEnum;
use common\enums\OperationTypeEnum;
use common\enums\TenderEnum;
use common\models\CompanyTransaction;
use common\models\TenderRequest;
use common\models\VirtualTransaction;
use yii\base\Exception;

class TenderAcceptContractForm extends BaseRequest
{
    public ?ContractResource $model;
    public $vote;
    public $id;


    public function rules()
    {
        return [
            [['vote','id'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['vote','id'], 'integer'],
            ['vote', 'checkVote'],
        ];
    }


    public function checkVote()
    {
        if ($this->vote == TenderEnum::VOTE_YES || $this->vote == TenderEnum::VOTE_NO) {
            return true;
        } else {
            $this->addError("vote", t("Ovoz berish qiymatlari 1 yoki 0 bo'lishi kerak"));
            return false;
        }
    }

    /**
     * @throws \Throwable
     * @throws \yii\db\Exception
     */
    public function getResult()
    {
        /**
         * @var $win TenderRequest
         */

        $producer_id = \Yii::$app->user->identity->company_id;
        $this->model = ContractResource::find()->where(['id' => $this->id,'producer_id' => $producer_id])->one();
        if (!$this->model) {
            $this->addError('id', t("Shartnoma topilmadi."));

            return false;
        }
        $transaction = \Yii::$app->db->beginTransaction();
        if ($this->vote == TenderEnum::VOTE_YES) {
            $this->model->status = ContractEnum::STATUS_ACCEPT;
            if (!$this->model->save()) {
                $this->addErrors($this->model->errors);
                $transaction->rollBack();

                return false;
            }

            $this->model->tender->state = TenderEnum::STATE_ACCEPT_CONTRACT;
            if (!$this->model->tender->save()) {
                $this->addErrors($this->model->tender->errors);
                $transaction->rollBack();

                return false;
            }

            // tender shartida shartnoma tuzilganidan kn deyilgan bo'lsa, zalogni qaytarish kerak

            if ($this->model->tender->unblocking_type == TenderEnum::AFTER_END_CONTRACT) {
//                $company_transaction_zalog = CompanyTransaction::find()->where([
//                    'company_id' => $this->model->producer_id,
//                    'tender_id' => $this->model->tender_id,
//                    'type' => CompanyTransactionEnum::TYPE_ZALOG,
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'reverted_id' => null
//                ])->one();
//
//                $revert = new CompanyTransaction([
//                    'company_id' => $company_transaction_zalog->company_id,
//                    'amount' => $company_transaction_zalog->amount,
//                    'contract_id' => $this->model->id,
//                    'tender_id' => $company_transaction_zalog->tender_id,
//                    'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
//                    'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                    'description' => \Yii::t("main", "Garov qaytarildi"),
//                    'transaction_date' => date("Y-m-d H:i:s"),
//                ]);
//
//                if ($revert->save()) {
//                    $company_transaction_zalog->reverted_id = $revert->id;
//                    $company_transaction_zalog->save(false);
//                } else {
//                    $this->addErrors($revert->errors);
//                    $transaction->rollBack();
//
//                    return false;
//                }

                try {
                    $company_transaction_zalog = VirtualTransactionResource::find()->where([
                        'tender_id' => $this->model->id,
                        "operation_type" => OperationTypeEnum::BLOCK_TRANSACTION_DEPOSIT_AGAIN,
                        "parent_id" => null,
                        "credit_company_id" => $producer_id,
                    ])->andWhere(['>','credit',0])->one();
                    if (!$company_transaction_zalog) {
                        throw new Exception("Winner deposit transaction not found");
                    }
                    /** @var VirtualTransactionResource $company_transaction_zalog */
                    $_revertID = VirtualTransaction::saveTransaction(
                        $company_transaction_zalog->creditCompany,
                        $company_transaction_zalog->debitCompany,
                        $company_transaction_zalog->creditAccount->prefix_account,
                        $company_transaction_zalog->debitAccount->prefix_account,
                        $company_transaction_zalog->credit,
                        \Yii::t("main", "zalog qaytarildi, tenderda shartnomadan keyinn zalog qaytarish belgilangan ekan"),
                        OperationTypeEnum::PRODUCT_NAME_TENDER,
                        $this->model->tender_id,
                        $this->model->id,
                        OperationTypeEnum::CANCEL_BLOCK_TRANSACTION_DEPOSIT,
                    );
                    $company_transaction_zalog->parent_id = $_revertID;
                    if (!$company_transaction_zalog->save()){
                        $transaction->rollBack();
                        $this->addErrors($company_transaction_zalog->errors);
                        return false;
                    }
                } catch (Exception $ex) {
                    $transaction->rollBack();
                    $this->addError("error", $ex->getMessage());
                    return false;
                }
            }
        } else {

            $this->model->tender->state = TenderEnum::STATE_REJECT_WINNER;
            if (!$this->model->tender->save()) {
                $this->addErrors($this->model->tender->errors);
                $transaction->rollBack();

                return false;
            }

            $this->model->status = ContractEnum::STATUS_CANCELLED;
            if (!$this->model->save()) {
                $this->addErrors($this->model->errors);
                $transaction->rollBack();

                return false;
            }

            // customerga zalog o'tkazish kerak.
//            $date = date("Y-m-d H:i:s");
//
//            $company_transaction_zalog = CompanyTransaction::find()->where([
//                'company_id' => $this->model->producer_id,
//                'tender_id' => $this->model->tender_id,
//                'type' => CompanyTransactionEnum::TYPE_ZALOG,
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                'reverted_id' => null
//            ])->one();
//
//            $revert = new CompanyTransaction([
//                'company_id' => $company_transaction_zalog->company_id,
//                'amount' => $company_transaction_zalog->amount,
//                'contract_id' => $this->model->id,
//                'tender_id' => $company_transaction_zalog->tender_id,
//                'type' => CompanyTransactionEnum::TYPE_REVERT_ZALOG,
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                'description' => \Yii::t("main", "Garov qaytarildi"),
//                'transaction_date' => $date,
//            ]);
//
//            if ($revert->save()) {
//                $company_transaction_zalog->reverted_id = $revert->id;
//                $company_transaction_zalog->save(false);
//            } else {
//                $this->addErrors($revert->errors);
//                $transaction->rollBack();
//
//                return false;
//            }
//
//
//            $company_transaction_penalty_out = new CompanyTransaction([
//                'company_id' => $this->model->producer_id,
//                'contract_id' => $this->model->id,
//                'tender_id' => $this->model->tender_id,
//                'amount' => $company_transaction_zalog->amount,
//                'type' => CompanyTransactionEnum::TYPE_PENALTY_OUT,
//                'description' => \Yii::t("main", "Shartnoma qabul qilmagani uchun garovni buyurtmachiga o'tkazildi"),
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                'transaction_date' => $date,
//            ]);
////
//            if (!$company_transaction_penalty_out->save()) {
//                $this->addErrors($company_transaction_penalty_out->errors);
//                $transaction->rollBack();
//
//                return false;
//            }
//
//            $company_transaction_penalty_in = new CompanyTransaction([
//                'company_id' => $this->model->customer_id,
//                'contract_id' => $this->model->id,
//                'tender_id' => $this->model->tender_id,
//                'amount' => $company_transaction_zalog->amount,
//                'type' => CompanyTransactionEnum::TYPE_PENALTY_IN,
//                'description' => \Yii::t("main", "Shartnoma qabul qilmagani uchun garov buyurtmachiga qabul qilindi"),
//                'status' => CompanyTransactionEnum::STATUS_SUCCESS,
//                'transaction_date' => $date,
//            ]);
////
//            if (!$company_transaction_penalty_in->save()) {
//                $this->addErrors($company_transaction_penalty_in->errors);
//                $transaction->rollBack();
//
//                return false;
//            }

            try {
                $company_transaction_zalog = VirtualTransactionResource::find()->where([
                    'tender_id' => $this->model->id,
                    "operation_type" => OperationTypeEnum::BLOCK_TRANSACTION_DEPOSIT_AGAIN,
                    "parent_id" => null,
                    "credit_company_id" => $producer_id,
                ])->andWhere(['>','credit',0])->one();
                if (!$company_transaction_zalog) {
                    throw new Exception("Winner deposit transaction not found");
                }
                /** @var VirtualTransactionResource $company_transaction_zalog */
                $_revertID = VirtualTransaction::saveTransaction(
                    $this->model->producer,
                    $this->model->customer,
                    OperationTypeEnum::P_K_30301,
                    $this->model->customer->isBudget() ? OperationTypeEnum::P_B_31103 : OperationTypeEnum::P_K_30101,
                    $company_transaction_zalog->credit,
                    "Bitimni yopish uchun Sotuvchi tomonidan depozit miqdorini jarima sifatida toʻlandi",
                    OperationTypeEnum::PRODUCT_NAME_TENDER,
                    $this->model->tender_id,
                    $this->model->id,
                    OperationTypeEnum::PAY_DEPOSIT_AS_FINE_SELLER,
                );
                $company_transaction_zalog->parent_id = $_revertID;
                if (!$company_transaction_zalog->save()){
                    $transaction->rollBack();
                    $this->addErrors($company_transaction_zalog->errors);
                    return false;
                }
            } catch (Exception $ex) {
                $transaction->rollBack();
                $this->addError("error", $ex->getMessage());
                return false;
            }
        }


        $commissions = VirtualTransactionResource::find()->where([
            'tender_id' => $this->model->tender_id,
            'contract_id' => $this->model->id,
            "parent_id" => null,
            'operation_type' => OperationTypeEnum::HOLD_TRANSACTION_COMMISSION,
        ])->andWhere(['>','credit',0])->andWhere(["in","credit_company_id",[$this->model->producer,$this->model->customer]])->all();
        try {
            foreach ($commissions as $commission) {
                /** @var VirtualTransactionResource  $commission */
                $_company = $commission->creditCompany;
                $revertID = VirtualTransaction::saveTransaction(
                    $commission->creditCompany,
                    _company(),
                    $_company->isBudget() ? OperationTypeEnum::P_B_31501 : OperationTypeEnum::P_K_30501,
                    $_company->isBudget() ? OperationTypeEnum::A_50113 : OperationTypeEnum::A_50111,
                    $commission->credit,
                    "Bitim bo’yicha vositachilik yigʻimini toʻlandi.",
                    OperationTypeEnum::PRODUCT_NAME_TENDER,
                    $this->model->tender_id,
                    $this->model->id,
                    OperationTypeEnum::PAY_TRANSACTION_COMMISSION,
                );
                $commission->parent_id = $revertID;
                if (!$commission->save()){
                    $transaction->rollBack();
                    $this->addErrors($commission->errors);
                    return false;
                }
            }
        } catch (Exception $ex) {
            $transaction->rollBack();
            $this->addError("error", $ex->getMessage());
            return false;
        }

        $transaction->commit();
        return $this->model->id;

    }

}