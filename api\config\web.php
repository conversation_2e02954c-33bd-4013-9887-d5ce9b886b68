<?php

use common\components\log\ErrorHandler;

require_once dirname(__DIR__) . '/helpers/function.php';
$config = [
    'homeUrl' => Yii::getAlias('@apiUrl'),
    'controllerNamespace' => 'api\controllers',
    'defaultRoute' => 'site/index',
    'bootstrap' => ['maintenance'],
    'modules' => [
        'auth' => \api\modules\auth\Module::class,
        'shop' => \api\modules\shop\Module::class,
        'tender' => \api\modules\tender\Module::class,
        'auction' => \api\modules\auction\Module::class,
        'backend' => \api\modules\backend\Module::class,
        'common' => \api\modules\common\Module::class,
        'client' => \api\modules\client\Module::class,
    ],
    'timeZone' => 'Asia/Tashkent',
    'components' => [
        'errorHandler' => [
            'class' => ErrorHandler::class,
            'errorAction' => 'site/error'
        ],
        'maintenance' => [
            'class' => common\components\maintenance\Maintenance::class,
            'enabled' => function ($app) {
                if (env('APP_MAINTENANCE') === '1') {
                    return true;
                }
                return $app->keyStorage->get('frontend.maintenance') === 'enabled';
            }
        ],
        'request' => [
            'enableCookieValidation' => false,
            'parsers' => [
                'application/json' => 'yii\web\JsonParser'
            ],
        ],
        'user' => [
            'class' => yii\web\User::class,
            'identityClass' => common\models\User::class,
            'loginUrl' => ['/user/sign-in/login'],
            'enableAutoLogin' => true,
            'as afterLogin' => common\behaviors\LoginTimestampBehavior::class
        ],
        'response' => [
            'on beforeSend' => function ($event) {
                $headers = $event->sender->headers;
                $headers->set('X-Frame-Options', 'DENY');
            },
        ],
    ]
];

return $config;
