<?php
require_once dirname(__DIR__).'/../api/helpers/function.php';
$config = [
//    'homeUrl' => Yii::$app->homeUrl,
    'controllerNamespace' => 'backend\controllers',
    'defaultRoute' => 'site/index',
    'timeZone' => 'Asia/Tashkent',
    'components' => [
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],
        'response' => [
            'on beforeSend' => function ($event) {
                $headers = $event->sender->headers;
                $headers->set('X-Frame-Options', 'DENY');
            },
        ],
        'request' => [
            'cookieValidationKey' => env('BACKEND_COOKIE_VALIDATION_KEY'),
            'baseUrl' => env('BACKEND_BASE_URL'),
        ],
        'view' => [
            'theme' => [
                'pathMap' => [
                    '@app/views' => '@app/views/lte'
                ],
            ],
        ],
        'user' => [
            'class' => yii\web\User::class,
            'identityClass' => common\models\User::class,
            'loginUrl' => ['sign-in/login'],
//            'enableAutoLogin' => true,
            'as afterLogin' => common\behaviors\LoginTimestampBehavior::class,
        ],
    ],
    'modules' => [
        'file' => [
            'class' => backend\modules\file\Module::class,
        ],
        'system' => [
            'class' => backend\modules\system\Module::class,
        ],
        'translation' => [
            'class' => backend\modules\translation\Module::class,
        ],
        'rbac' => [
            'class' => backend\modules\rbac\Module::class,
            'defaultRoute' => 'rbac-auth-item/index',
        ],
        'admin' => [
            'class' => 'backend\modules\admin\Module',
        ],
    ],
//    'as globalAccess' => [
//        'class' => common\behaviors\GlobalAccessBehavior::class,
//        'rules' => [
//            [
//                'controllers' => ['sign-in'],
//                'allow' => true,
//                'roles' => ['?'],
//                'actions' => ['login'],
//            ],
//            [
//                'controllers' => ['sign-in'],
//                'allow' => true,
//                'roles' => ['@'],
//                'actions' => ['logout'],
//            ],
//            [
//                'controllers' => ['site'],
//                'allow' => true,
//                'roles' => ['?', '@'],
//                'actions' => ['error'],
//            ],
////            [
////                'controllers' => ['debug/default'],
////                'allow' => true,
////                'roles' => ['?'],
////            ],
////            [
////                'controllers' => ['user'],
////                'allow' => true,
////                'roles' => ['administrator'],
////            ],
////            [
////                'controllers' => ['user'],
////                'allow' => false,
////            ],
////            [
////                'allow' => true,
////                'roles' => ['moderator', 'administrator'],
////            ],
//        ],
//    ],
];

if (YII_ENV_DEV) {
    $config['modules']['gii'] = [
        'class' => yii\gii\Module::class,
        'generators' => [
            'crud' => [
                'class' => yii\gii\generators\crud\Generator::class,
                'templates' => [
                    'yii2-starter-kit' => Yii::getAlias('@backend/views/_gii/templates'),
                ],
                'template' => 'yii2-starter-kit',
                'messageCategory' => 'backend',
            ],
        ],
    ];
}

return $config;
