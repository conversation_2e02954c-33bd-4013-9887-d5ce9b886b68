<?php


namespace api\modules\tender\resources;


use common\enums\TenderEnum;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderRequestRatingCommission;
use yii\web\NotFoundHttpException;

class TenderRequestRatingCommissionResource extends TenderRequestRatingCommission
{

    public function fields()
    {
        return [
            'id',
            'vote',
            'description',
            'status',
            'tender_commission_member_id',
            'commission_member_id',
            'tender_id',
            'created_at',
            'role' => function ($model) {
                return $this->tenderCommissionMember ? $this->tenderCommissionMember->role : '';
            }
        ];
    }

    public function extraFields()
    {
        return [
            'commissionMember',
            'tender',
            'tenderCommissionMember',
        ];
    }

    /**
     * Gets query for [[CommissionMember]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCommissionMember()
    {
        return $this->hasOne(CommissionMemberShortResource::class, ['id' => 'commission_member_id']);
    }

    /**
     * Gets query for [[Tender]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTender()
    {
        return $this->hasOne(TenderResource::class, ['id' => 'tender_id']);
    }

    /**
     * Gets query for [[TenderCommissionMember]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTenderCommissionMember()
    {
        return $this->hasOne(TenderCommissionMemberResource::class, ['id' => 'tender_commission_member_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}