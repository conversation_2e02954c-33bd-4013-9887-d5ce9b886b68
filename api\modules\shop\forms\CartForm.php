<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\shop\resources\CartResource;
use common\models\shop\Product;

class CartForm extends BaseRequest
{

    public CartResource $model;

    public $product_id;
    public $quantity;

    public $regions = [];

    public function __construct(CartResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules()
    {
        return [
            [ ['product_id', 'quantity'
            ], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['product_id'], 'exist', 'skipOnError' => true, 'targetClass' => Product::class, 'targetAttribute' => ['product_id' => 'id']],

        ];
    }


    public function getResult()
    {
        $transaction = \Yii::$app->db->beginTransaction();

        $companyId = \Yii::$app->user->identity->company_id;

        //TODO check balance
        $product = Product::findOne($this->product_id);


//        $zalog = $product->max_order*$product->unit_price*env('SHOP_ZALOG_PERSENT', 0.003);
//        $commission = $product->max_order*$product->unit_price*env('COMMISSION_PERCENT', 0.015);
//        $companyBalance =  CompanyBalance::find()->andWhere(['company_id'=>$companyId])->one();
//
//        if ($companyBalance && ($companyBalance->available < ($zalog+$commission))) {
//            $this->addError("days", t("Balansda yetarli mablag' mavjud emas"));
//            return false;
//        }

        if ($this->quantity > $product->quantity) {
            $this->quantity = $product->quantity;
        }

        if ($this->quantity < $product->min_order) {
            $this->quantity = $product->min_order;
        }

        $this->model->user_id = \Yii::$app->user->id;
        $this->model->company_id = $companyId;
        $this->model->price = $product->unit_price * $this->quantity;
        $att = $this->attributes;
        $this->model->setAttributes($att,false);

        if($this->model->attributes && $this->model->validate() && $this->model->save()){

            $transaction->commit();
            return true;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;
    }
}