<?php

namespace api\modules\auction\forms;

use api\components\BaseRequest;
use api\modules\auction\resources\AuctionDetailResource;
use common\enums\AuctionEnum;
use common\models\auction\AuctionHistory;
use yii\base\Exception;

class AuctionDeleteForm extends BaseRequest
{
    public AuctionDetailResource $model;

    public function __construct(AuctionDetailResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function getResult()
    {
        if (!in_array($this->model->status, [AuctionEnum::STATUS_MODERATING])) {
            throw new Exception(t('Auksion moderatsiya xolatidan chiqib ketgan.'));
        }
        $this->model->deleted_at = date('Y-m-d H:i:s');
        $this->model->status = AuctionEnum::STATUS_DELETED;

        $transaction = \Yii::$app->db->beginTransaction();
        if (!$this->model->save()) {
            $this->addErrors($this->model->getErrors());
            return false;
        }

        $history = new AuctionHistory();
        $history->auction_id = $this->model->id;
        $history->user_id = \Yii::$app->user->id;
        $history->status = $this->model->status;
        $history->comment = t("Bekor qilindi.");
        $history->created_at = date("Y-m-d H:i:s");
        if (!$history->save()) {
            $this->addErrors($history->errors);
            $transaction->rollBack();
            return false;
        }

        $transaction->commit();

        return true;
    }
}
