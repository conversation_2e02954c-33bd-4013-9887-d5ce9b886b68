<?php


namespace backend\modules\admin\controllers;


use api\components\ApiController;
use backend\modules\admin\filters\BankAccountFilter;
use backend\modules\admin\forms\SetBalanceForm;
use Yii;

class BankAccountController extends ApiController
{
    public function actionIndex()
    {
        return $this->sendResponse(
            new BankAccountFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionSetBalance()
    {
        return $this->sendResponse(
            new SetBalanceForm(),
            Yii::$app->request->bodyParams,
        );
    }
}