<?php

use yii\db\Migration;

/**
 * Handles the creation of table `{{%tender_requirements}}`.
 */
class m231112_140225_create_tender_requirements_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->createTable('{{%tender_requirements}}', [
            'id' => $this->primaryKey(),
            'tender_id' => $this->integer(),
            'title' => $this->string(255),
            'description' => $this->string(255),
            'type' => $this->string(), // ENUM data type: TEXT, NUMBER, BINARY
            'obligation' => $this->integer(2), //majburiyligi, 2-majburiy, 1-majburiy emas
            'evaluation_method' => $this->string(), //baholash metodi: system, expert
            'file_uploading_necessary' => $this->integer(2),//1-fayk yuklash kerak, 0-kerak emas
            'file_is_required' => $this->integer(2),//1-majburiy, 0-majburiy emas
            'file_name' => $this->string(255),
            'max_ball' => $this->integer(),
            'min_ball' => $this->integer(),
            'comparative_weight' => $this->integer(),//Solishtirma og’irlik
            'status' => $this->integer(),
            'value' => $this->string(255), //type=NUMBER bo'lsa
            'value_binary' => $this->integer(), //type=BINARY bo'lsa
            'value_condition' => $this->integer(), //type=NUMBER bo'lsa radio buttonlar, teng = 1, katta emas = 2, kichik emas = 3, katta tomon yaxshiroq =4 , kichik tomon yaxshiroq =5
            'value_from' => $this->integer(), //type=NUMBER bo'lsa
            'value_to' => $this->integer(), //type=NUMBER bo'lsa
            'unit' => $this->integer(), //type=NUMBER bo'lsa
            'created_at' => $this->dateTime(),
            'updated_at' => $this->dateTime(),
            'deleted_at' => $this->dateTime(),
            'created_by' => $this->integer(),
            'updated_by' => $this->integer(),
        ]);

        $this->addCommentOnColumn('tender_requirements', 'comparative_weight', 'Solishtirma og’irlik');
        $this->addCommentOnColumn('tender_requirements', 'value_condition', 'radio buttonlar, teng = 1, katta emas = 2, kichik emas = 3, katta tomon yaxshiroq =4 , kichik tomon yaxshiroq =5');
        $this->addCommentOnColumn('tender_requirements', 'value', "radio buttonga(1,2,3) bog'liq qiymat, sonli yoki type=binary bo'lsa: xa/yoq =1, rozi/rozi =2 emas");
        $this->addCommentOnColumn('tender_requirements', 'value_from', "radio buttonga bog'liq qiymat, sonli (4,5)");
        $this->addCommentOnColumn('tender_requirements', 'value_to', "radio buttonga bog'liq qiymat, sonli (4,5)");
        $this->addCommentOnColumn('tender_requirements', 'file_uploading_necessary', "true-fayk yuklash kerak, false-kerak emas, checkbox");
        $this->addCommentOnColumn('tender_requirements', 'file_is_required', "1-majburiy, 0-majburiy emas, radiobutton");

        $this->addForeignKey('FK_tender_requirements_tender', 'tender_requirements', 'tender_id', 'tender', 'id', 'CASCADE', 'CASCADE');

    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropTable('{{%tender_requirements}}');
    }
}
