<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\common\resources\FileResource;
use api\modules\tender\resources\TenderResource;
use common\enums\ContractEnum;
use common\enums\OperationTypeEnum;
use common\enums\TenderEnum;
use common\models\Contract;
use common\models\TenderRequest;
use common\models\VirtualTransaction;
use yii\httpclient\Exception;
use yii\web\NotFoundHttpException;

class TenderSendContractForm extends BaseRequest
{
    public TenderResource $model;
    public $file_id;
    public $id;
    public $winner;

    /**
     * @throws NotFoundHttpException
     */
    public function __construct($winner, $params = [])
    {
        $this->winner = $winner;
        parent::__construct($params);
    }

    public function rules()
    {
        return  [
            [['file_id','id'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['file_id'], 'integer'],
            [['file_id'], 'exist', 'skipOnError' => true, 'targetClass' => FileResource::class, 'targetAttribute' => ['file_id' => 'id']],
        ];
    }


    /**
     * @throws \yii\db\Exception
     * @throws Exception
     * @throws \Throwable
     */
    public function getResult()
    {
        /**
         * @var $win TenderRequest
         */

        $this->model = TenderResource::_findOne($this->id, $this->winner == 1 ? TenderEnum::STATE_MADE_DISCUSSION_PROTOCOL : TenderEnum::STATE_REJECT_WINNER);

        $transaction = \Yii::$app->db->beginTransaction();

        if (!in_array($this->model->state, [TenderEnum::STATE_MADE_DISCUSSION_PROTOCOL, TenderEnum::STATE_REJECT_WINNER])) {
            throw new Exception(t("Shartnoma yuborish xolatida emas"));
        }
        $customer_id = \Yii::$app->user->identity->company_id;
        $customer = \Yii::$app->user->identity->company;

        if ($this->winner == 1) {
            $win = $this->model->tenderRequestWinner;
            if (!$win) {
                throw new Exception(t("G'olib topilmadi"));
            }

            $this->model->state = TenderEnum::STATE_SEND_CONTRACT;
        } else {

            $win = $this->model->tenderRequestSecondWinner;
            if (!$win) {
                $this->addError('file_id', t("Zaxira g'olib topilmadi"));

                return false;
            }

            $this->model->state = TenderEnum::STATE_SEND_CONTRACT_SECOND_WINNER;
        }
        $producer_id = $win->company_id;
        $producer = $win->company;


        if (!$this->model->save()) {
            $this->addErrors($this->model->errors);
            $transaction->rollBack();
            return false;
        }

        $contract = new Contract([
            'customer_id' => $customer_id,
            'producer_id' => $producer_id,
            'tender_id' => $this->model->id,
            'file_id' => $this->file_id,
            'price' => $win->price + $win->price_qqs,
            'customer_signed' => 1,
            'producer_signed' => 1,
            'status' => ContractEnum::STATUS_NEW
        ]);

        if (!$contract->save()) {
            $this->addErrors($contract->errors);
            $transaction->rollBack();
            return false;
        }

        $__commission = ($win->price + $win->price_qqs) * env("COMMISSION_PERCENT",0.0015);
        $_commission = min($__commission, env('SHOP_CUSTOMER_MAX_COMMISSION_SUM', 1000000));
        $_deposit = ($win->price + $win->price_qqs) * env("SHOP_ZALOG_PERSENT",0.03);
        if (!hasMoney($producer, $_deposit + $_commission)) {
            throw new Exception(t("Yetkazib beruvchida mablag' yetarli emas"));
        }
        if (!hasMoney($customer, $_commission)) {
            throw new Exception(t("Buyurtmachida mablag' yetarli emas"));
        }
        try {
            // Producer va Customer dan commission block qilish
            VirtualTransaction::saveTransaction(
                $customer,
                $customer,
                $customer->isBudget() ? OperationTypeEnum::P_B_31101 : OperationTypeEnum::P_K_30101,
                $customer->isBudget() ? OperationTypeEnum::P_B_31501 : OperationTypeEnum::P_K_30501,
                $_commission,
                "Bitim bo’yicha vositachilik yigʻimini ushlab qolindi.",
                OperationTypeEnum::PRODUCT_NAME_TENDER,
                $this->model->id,
                $contract->id,
                OperationTypeEnum::HOLD_TRANSACTION_COMMISSION,
            );
            VirtualTransaction::saveTransaction(
                $producer,
                $producer,
                $producer->isBudget() ? OperationTypeEnum::P_B_31101 : OperationTypeEnum::P_K_30101,
                $producer->isBudget() ? OperationTypeEnum::P_B_31501 : OperationTypeEnum::P_K_30501,
                $_commission,
                "Bitim bo’yicha vositachilik yigʻimini ushlab qolindi.",
                OperationTypeEnum::PRODUCT_NAME_TENDER,
                $this->model->id,
                $contract->id,
                OperationTypeEnum::HOLD_TRANSACTION_COMMISSION,
            );

            // Producer deposit block qilish
            if (!$producer->isBudget())
            {
                VirtualTransaction::saveTransaction(
                    $producer,
                    $producer,
                    OperationTypeEnum::P_K_30101,
                    OperationTypeEnum::P_K_30301,
                    $_deposit,
                    "Bitim bo’yicha vositachilik yigʻimini ushlab qolindi.",
                    OperationTypeEnum::PRODUCT_NAME_TENDER,
                    $this->model->id,
                    $contract->id,
                    OperationTypeEnum::BLOCK_TRANSACTION_DEPOSIT_AGAIN,
                );
            }
        } catch (Exception $exception) {
            $transaction->rollBack();
            $this->addError('error',$exception->getMessage());
            return false;
        }

        $transaction->commit();
        return $this->model->id;

    }

}