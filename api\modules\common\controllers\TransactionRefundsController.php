<?php


namespace api\modules\common\controllers;


use api\components\ApiController;
use api\modules\common\filters\TransactionRefundsFilter;
use api\modules\common\forms\CreateRefundsForm;
use api\modules\common\resources\TransactionRefundsResource;
use Yii;

class TransactionRefundsController extends ApiController
{
    public function actionIndex()
    {
        return $this->sendResponse(
            new TransactionRefundsFilter(),
            Yii::$app->request->queryParams
        );
    }
    public function actionCreate()
    {
        return $this->sendResponse(
            new CreateRefundsForm(new TransactionRefundsResource),
            Yii::$app->request->bodyParams
        );
    }

}
