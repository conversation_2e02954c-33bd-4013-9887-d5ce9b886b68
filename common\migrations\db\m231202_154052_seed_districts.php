<?php

use common\models\Region;
use PhpOffice\PhpSpreadsheet\IOFactory;
use yii\db\Migration;

/**
 * Class m231202_154052_seed_districts
 */
class m231202_154052_seed_districts extends Migration
{
    /**
     * {@inheritdoc}
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
  public function safeUp()
  {
    $file = Yii::getAlias('@base/uploads/') . 'districts.xlsx';
    $inputFileType = IOFactory::identify($file);
    $objReader = IOFactory::createReader($inputFileType);
    $objPHPExcel = $objReader->load($file);

    $transaction = Yii::$app->db->beginTransaction();
    try {
      $sheet = $objPHPExcel->getSheet(0);
      $highestRow = $sheet->getHighestRow();
      $highestColumn = $sheet->getHighestColumn();

      //$row is start 2 because first row assigned for heading.         
      for ($row = 2; $row <= $highestRow; $row++) {
        $rowData = $sheet->rangeToArray('A' . $row . ':' . $highestColumn . $row, NULL, TRUE, FALSE);

        $emptyRow = true;

        foreach ($rowData as $k => $v) {
          if ($v) {
            $emptyRow = false;
          }
        }

        if ($emptyRow) {
          break; //this can be changed to continute to allow blank row in the excelsheet, otherwise loop will be terminated if blank row is found.
        }

        //save to database table.
        $this->insert('region', [
          'parent_id' => $rowData[0][0],
          'title_uz' => $rowData[0][1],
          'title_uzk' => $rowData[0][1],
          'title_ru' => $rowData[0][2],
          'title_en' => $rowData[0][1],
          'type' => Region::TYPE_DISTRICT,
        ]);
      }

      $transaction->commit();
    } catch (Throwable $th) {
      die(var_dump($th->getMessage()));
      $transaction->rollBack();
    }
  }

  /**
   * {@inheritdoc}
   */
  public function safeDown()
  {
    echo "m231202_154052_seed_districts cannot be reverted.\n";

    return false;
  }

  /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m231202_154052_seed_districts cannot be reverted.\n";

        return false;
    }
    */
}
