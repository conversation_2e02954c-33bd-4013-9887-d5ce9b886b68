<?php


namespace api\modules\shop\forms;


use api\components\BaseRequest;
use api\modules\common\resources\ClassifierCategoryResource;
use api\modules\common\resources\ClassifierResource;
use api\modules\shop\resources\CartResource;
use api\modules\shop\resources\ProductResource;
use common\enums\ProductEnum;
use common\models\CompanyBalance;
use common\models\shop\ProductFile;
use common\models\shop\ProductRegion;
use common\models\TenderModeratorLog;
use Yii;
use function foo\func;

class CartDeleteForm extends BaseRequest
{

    public CartResource $model;

    public function __construct(CartResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }


    public function getResult()
    {
        $this->model->delete();
        return true;
    }
}