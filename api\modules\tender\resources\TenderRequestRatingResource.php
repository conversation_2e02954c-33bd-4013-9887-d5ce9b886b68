<?php


namespace api\modules\tender\resources;


use api\modules\client\resources\TenderRequestResource;
use api\modules\client\resources\TenderRequirementsAnswerResource;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderRequestRating;

class TenderRequestRatingResource extends TenderRequestRating
{

    public function fields()
    {
        return [
            'id',
            'tender_id',
            'tender_request_id',
            'tender_requirement_answer_id',
            'ball',
            'status',
            'description',
        ];
    }

    public function extraFields()
    {
        return [
            'tenderRequest',
            'tenderRequirementAnswer',
            'tender'
        ];
    }

    /**
     * Gets query for [[TenderRequest]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTenderRequest()
    {
        return $this->hasOne(TenderRequestResource::class, ['id' => 'tender_request_id']);
    }

    public function getTender()
    {
        return $this->hasOne(TenderResource::class, ['id' => 'tender_id']);
    }

    /**
     * Gets query for [[TenderRequirementAnswer]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getTenderRequirementAnswer()
    {
        return $this->hasOne(TenderRequirementsAnswerResource::class, ['id' => 'tender_requirement_answer_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }

}