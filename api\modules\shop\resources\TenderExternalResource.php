<?php

namespace app\modules\shop\resources;

use api\modules\common\resources\ClassifierResource;
use common\models\Tender;
use yii\helpers\ArrayHelper;

class TenderExternalResource extends Tender
{
    public function fields()
    {
        return [
            'id',
            'lot',
            'classifiers',
        ];
    }

    public function getClassifiers()
    {
        $auctionClassifiers = $this->tenderClassifiers;
        $classfiers = ClassifierResource::find()->where(['id' => ArrayHelper::getColumn($auctionClassifiers, 'classifier_id')])->all();
        return $classfiers;
    }
}
