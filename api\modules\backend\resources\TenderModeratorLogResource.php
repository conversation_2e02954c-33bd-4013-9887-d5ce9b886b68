<?php


namespace api\modules\backend\resources;


use common\enums\TenderEnum;
use common\models\TenderModeratorLog;

class TenderModeratorLogResource extends TenderModeratorLog
{
    public function fields()
    {
        return [
            'created_at', 'description'
        ];
    }

    public static function create(TenderResource $tender, TenderEnum $state, $description){

        $log = new self();
        $log->tender_id = $tender->id;
        $log->state = $state;
        $log->description = $description;
        $log->moderator_pinfl = \Yii::$app->user->identity->username;

    }

}