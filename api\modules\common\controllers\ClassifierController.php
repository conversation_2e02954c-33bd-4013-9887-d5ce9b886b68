<?php


namespace api\modules\common\controllers;


use api\components\ApiController;
use api\modules\common\filters\ClassifierFilter;
use api\modules\common\filters\ClassifierListFilter;
use api\modules\common\filters\ClassifierPropertiesFilter;
use api\modules\common\filters\ClassifierWithPlanScheduleFilter;
use common\models\Classifier;
use Yii;
use yii\web\NotFoundHttpException;

class ClassifierController extends ApiController
{
    public function actionIndex($title = null, $categoryId = null, $type = null)
    {
        return $this->sendResponse(
            new ClassifierFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionListByPlanScheduleAndCategory()
    {
        return $this->sendResponse(
            new ClassifierWithPlanScheduleFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionClassifierList($title = null, $categoryId = null, $type = null)
    {
        return $this->sendResponse(
            new ClassifierListFilter(),
            Yii::$app->request->queryParams
        );
    }

    public function actionProperties()
    {
        return $this->sendResponse(
            new ClassifierPropertiesFilter(),
            Yii::$app->request->queryParams
        );
    }
}
