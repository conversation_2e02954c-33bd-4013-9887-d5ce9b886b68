<?php


namespace api\modules\tender\forms;


use api\components\BaseRequest;
use api\modules\tender\resources\CommissionGroupResource;
use common\enums\StatusEnum;

class CommissionGroupUpdateForm extends  BaseRequest
{
    public $title;

    public CommissionGroupResource $model;

    public function __construct(CommissionGroupResource $model, $params = [])
    {
        $this->model = $model;

        parent::__construct($params);
    }

    public function rules (){
        return [
            ['title', 'required', 'message' => t('{attribute} yuborish majburiy')],
            ['title', 'safe']
        ];
    }

    public function getResult()
    {
        $this->model->title = $this->title;
        return $this->model->save();
    }
}