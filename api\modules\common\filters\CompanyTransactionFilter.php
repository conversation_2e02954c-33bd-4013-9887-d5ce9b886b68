<?php

namespace api\modules\common\filters;

use api\components\BaseRequest;
use api\modules\common\resources\VirtualTransactionResource;
use common\enums\CompanyTransactionEnum;
use DateTime;
use yii\db\ActiveQuery;

class CompanyTransactionFilter extends BaseRequest
{
    public ?string $search = null;
    public ?string $created_date = null;
    public ?int $procedure_type = null;

    public function rules(): array
    {
        return [
            [['search', 'created_date','procedure_type'], 'safe'],
            ['created_date', 'date', 'format' => 'php:d/m/Y'],
            ['procedure_type', 'in', 'range' => array_keys(CompanyTransactionEnum::PROCEDURE_LIST)],
        ];
    }

    /**
     * @throws \Exception
     */
    public function getResult(): array
    {
        return paginate($this->_query());
    }

    /**
     * @throws \Exception
     */
    public function _query(): ActiveQuery
    {
        $user = \Yii::$app->user->identity;
        if (!$user || !($companyID = $user->company_id))
        {
            throw new \Exception("Unauthorized");
        }
        $query = VirtualTransactionResource::find()
                    ->alias('transaction')
                    ->leftJoin(['credit_company' => 'company'], 'credit_company.id=transaction.credit_company_id')
                    ->leftJoin(['debit_company'  => 'company'], 'debit_company.id=transaction.debit_company_id')
                    ->andWhere(['not',['transaction.contract_id' => null]])
                    ->andWhere([
                        'or',
                        ['transaction.debit_company_id' => $companyID],
                        ['transaction.credit_company_id' => $companyID],
                    ]);
        if ($this->created_date) {
            $date = DateTime::createFromFormat('d/m/Y', $this->created_date)->format('Y-m-d');
            $query->andWhere(['BETWEEN', 'transaction.created_at', $date . ' 00:00:00' , $date . ' 23:59:59']);
        }
        if ($this->search)
        {
            $query->andFilterWhere([
                'OR',
                ['transaction.contract_id' => $this->search],
                ['transaction.description' => $this->search],
                ['like', 'credit_company.title' , $this->search],
            ]);
        }
        if ($this->procedure_type) {
            $productName = CompanyTransactionEnum::PROCEDURE_LIST[$this->procedure_type];
            $query->andWhere(['NOT', ['IS', "transaction.{$productName}", null]]);
        }
        return $query;
    }
}