<?php


namespace api\modules\tender\forms;


use api\components\BaseModel;
use api\components\BaseRequest;
use api\modules\common\resources\FileResource;
use api\modules\tender\resources\TenderClassifierResource;
use api\modules\tender\resources\TenderCommissionMemberResource;
use api\modules\tender\resources\TenderCommissionVoteResource;
use api\modules\tender\resources\TenderRequestRatingCommissionResource;
use api\modules\tender\resources\TenderRequirementsResource;
use api\modules\tender\resources\TenderResource;
use common\enums\TenderEnum;
use yii\base\Exception;
use yii\web\NotFoundHttpException;

class TenderDeleteForm extends BaseModel
{

    public ?TenderResource $model;
    public $deleted_file_id;
    public $deleted_reason;
    public $pkcs7;
    public $id;


    public function rules()
    {
        $parent =parent::rules();
        $child =  [
            [['id','deleted_file_id', 'deleted_reason'], 'required', 'message' => t('{attribute} yuborish majburiy')],
            [['deleted_file_id','id'], 'integer'],
            [['deleted_reason'], 'string'],
            [['deleted_file_id'], 'integer'],

            [['deleted_file_id'], 'exist', 'skipOnError' => true, 'targetClass' => FileResource::class, 'targetAttribute' => ['deleted_file_id' => 'id']],
        ];
        return  array_merge($parent,$child);
    }


    public function getResult()
    {
        $this->model = TenderResource::find()->notDeletedAndFromCompany()->andWhere(['id' => $this->id])->one();
        if (!$this->model) throw new NotFoundHttpException("Tender is not found");

        if ($this->model->status == TenderEnum::STATUS_DELETED) {
            throw new Exception(t("Tender avval o'chirilgan"));
            //return false;
        }
        if (!in_array($this->model->state, [TenderEnum::STATE_NEW, TenderEnum::STATE_REJECT_MODERATOR])) {
            throw new Exception(t("Tenderni yangi yoki moderator rad qilgan xolatda o'chirish mumkin"));
            //return false;
        }

        $tenderId = $this->model->id;

        $transaction = \Yii::$app->db->beginTransaction();

        $this->model->state = TenderEnum::STATE_CANCELLED;
        $this->model->status = TenderEnum::STATUS_DELETED;
        $this->model->deleted_file_id = $this->deleted_file_id;
        $this->model->deleted_reason = $this->deleted_reason;
        $this->model->deleted_at = date("Y-m-d H:i:s");
        $this->model->updated_by = \Yii::$app->user->id;

        #delete_custom
        #all_tender
        if ($this->model->save()) {
//                TenderCommissionMemberResource::updateAll(['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s")],['tender_id' => $tenderId]);
//                TenderClassifierResource::updateAll(['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s")],['tender_id' => $tenderId]);
//                TenderRequirementsResource::updateAll(['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s")],['tender_id' => $tenderId]);
            TenderCommissionVoteResource::updateAll(['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s")], ['tender_id' => $tenderId]);
            TenderRequestRatingCommissionResource::updateAll(['status' => TenderEnum::STATUS_DELETED, 'deleted_at' => date("Y-m-d H:i:s")], ['tender_id' => $tenderId]);
            $transaction->commit();
            return $this->model->id;
        }

        $transaction->rollBack();
        $this->addErrors($this->model->errors);
        return false;

    }
}
