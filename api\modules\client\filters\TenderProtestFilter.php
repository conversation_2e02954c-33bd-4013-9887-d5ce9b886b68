<?php


namespace api\modules\client\filters;


use api\components\BaseRequest;
use api\modules\client\resources\TenderDiscussionResource;
use api\modules\client\resources\TenderResource;

class TenderProtestFilter extends BaseRequest
{

    public TenderResource $tender;

    public function __construct(TenderResource $model, $params = [])
    {
        $this->tender = $model;

        parent::__construct($params);
    }

    public function getResult()
    {
        return TenderDiscussionResource::find()->where(['tender_id' => $this->tender->id, 'company_id' => \Yii::$app->user->identity->company_id])->one();

    }
}