<?php


namespace api\modules\common\filters;


use api\components\BaseRequest;
use api\modules\common\resources\CompanyBankAccountBudgetResource;
use api\modules\common\resources\CompanyBankAccountResource;
use common\models\CompanyBankAccount;

class CompanyBankAccountBudgetFilter extends BaseRequest
{

    public function getResult()
    {

        $user = \Yii::$app->user->identity;
        if (!$user->isBudget) {
            $this->addError("error", t("Ruxsat mavjud emas"));
            return false;
        }
//        return $companyId;

        $company_balance = CompanyBankAccountBudgetResource::find()->notDeleted()
            ->andWhere(['company_id' => $user->company_id, 'organ' => $user->organ, 'type' => CompanyBankAccount::TYPE_BUDGET]);
        return paginate($company_balance);
    }
}