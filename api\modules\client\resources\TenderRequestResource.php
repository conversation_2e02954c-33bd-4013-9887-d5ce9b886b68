<?php


namespace api\modules\client\resources;


use api\modules\common\resources\FileResource;
use api\modules\tender\resources\CompanyResource;
use common\models\query\NotDeletedFromCompanyQuery;
use common\models\TenderRequest;

class TenderRequestResource extends TenderRequest
{

    public function fields(){
        return [
            'id',
            'lot' => function($model){
                return $model->tender ? $model->tender->lot : $model->tender_id;
            },
            'tender_id',
            'price' => function($model){
                return $model->price / 100;
            },
            'price_qqs' => function($model){
                return $model->price_qqs / 100;
            },
            'status',
            'description',
            'preference_local_producer',
            'preference_local_producer_file_id' => function($model){
                return $model->file;
            },
            'tenderRequestValues'
        ];
    }
    public function extraFields(){
        return [
            'tenderRequestValues'
        ];
    }


    public function getTenderRequestValues()
    {
        return TenderRequestValuesResource::find()->notDeleted()->andWhere(['tender_request_id' => $this->id])->all();
//        return $this->hasMany(TenderRequestValuesResource::class, ['tender_request_id' => 'id']);
    }

    public function getQualificationSelection()
    {
        return $this->hasMany(TenderQualificationSelectionResource::class, ['tender_request_id' => 'id']);
    }

    public function getFile()
    {
        return $this->hasOne(FileResource::class, ['id' => 'preference_local_producer_file_id']);
    }

    public function getCompany()
    {
        return $this->hasOne(CompanyResource::class, ['id' => 'company_id']);
    }

    /**
     * return TenderResource
     * */
    public function getTender()
    {
        return $this->hasOne(TenderResource::class, ['id' => 'tender_id']);
    }

    public static function find()
    {
        return new NotDeletedFromCompanyQuery(get_called_class());
    }
}