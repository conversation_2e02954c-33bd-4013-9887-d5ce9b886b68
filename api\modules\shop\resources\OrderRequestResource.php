<?php

namespace api\modules\shop\resources;


use common\enums\ShopEnum;
use common\models\shop\OrderRequest;
use yii\helpers\ArrayHelper;

class OrderRequestResource extends OrderRequest
{
    public function fields()
    {
        return [
            'order_id',
            'company_id',
            'is_winner',
            'created_at',
            'price' => function($model){
                return $model->price / 100;
            },
            'type',
            'status',
            'id',
        ];
    }

    public function extraFields()
    {
        return [
            'order',
            'company',
            'statusName',
        ];
    }
    public function getStatusName()
    {
        return  ArrayHelper::getValue(
            [
                ShopEnum::ORDER_REQUEST_STATUS_ACTIVE=>\Yii::t('app','Buyurtmachi taklif berdi'),
                ShopEnum::ORDER_REQUEST_STATUS_DONE =>\Yii::t('app','Yetkazib beruvchi tasdiqladi'),
                ShopEnum::ORDER_REQUEST_STATUS_CANCEL=>\Yii::t('app','Yetka<PERSON>b beruvchi rad etdi'),
            ],
            $this->status);

    }
}
