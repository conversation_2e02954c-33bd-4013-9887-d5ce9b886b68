<?php

use common\enums\CurrencyEnum;
use yii\db\Migration;

class m250716_105948_add_new_col_to_company_bank_account_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $this->addColumn('company_bank_account', 'currency_code', $this->string(5)->after('account')->defaultValue(CurrencyEnum::CURRENCY_SUM));
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        $this->dropColumn('company_bank_account','currency_code');
    }
}
