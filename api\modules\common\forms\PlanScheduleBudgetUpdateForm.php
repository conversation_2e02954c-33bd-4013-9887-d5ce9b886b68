<?php

namespace api\modules\common\forms;

use api\components\BaseRequest;
use api\modules\common\resources\PlanScheduleClassifierBudgetCreateResource;
use api\modules\common\resources\PlanScheduleClassifierCreateResource;
use api\modules\common\resources\PlanScheduleClassifierResource;
use api\modules\common\resources\PlanSchedulePropertiesResource;
use api\modules\common\resources\PlanScheduleResource;
use common\enums\StatusEnum;
use common\enums\TenderEnum;
use common\models\Classifier;
use common\models\CompanyBankAccount;
use Yii;

class PlanScheduleBudgetUpdateForm extends BaseRequest
{

    public $planScheduleClassifierId;
    public $year;
    public $quarter;
    public $title;

    public $month;
    public $description;
    public $count; //TOVARAMOUNT
    public $price;
    public $expense; // xarajat moddasi
    public $accountId; // company_bank_account_id -> kls
    public $unitId; // Ўлчов бирлиги
    public $srok; //yetkazib berish muddati
    public $address; //yetkazib berish manzili
    public $classifierId; //tovar, tovarname
    public array $conditions = []; // dinamichniy maxsulotga qrab chiqadigani


    public PlanScheduleResource $model;

    public function __construct(PlanScheduleResource $model, $params = [])
    {
        $this->model = $model;
        parent::__construct($params);
    }

    /**
     * {@inheritdoc}
     * #return, year qiymatini aniqlashtirish kerak
     */
    public function rules()
    {
        return [
            [['planScheduleClassifierId', 'conditions', 'price', 'title', 'quarter', 'year', 'month', 'description', 'count', 'expense', 'accountId', 'srok', 'address', 'classifierId', 'unitId'], 'required', 'message' => t('{attribute} yuborish kerak')],
            [['count', 'month', 'srok', 'classifierId', 'expense', 'accountId', 'classifierId', 'unitId'], 'integer'],
            [['title', 'description'], 'string', 'max' => 255],
            ['quarter', 'integer', 'min' => 1, 'max' => 4],
            ['conditions', 'checkCondition'],
            ['year', 'integer', 'min' => date("Y"), 'max' => date("Y", strtotime("+1 year"))],
        ];
    }

    public function checkCondition()
    {
        if (!isset($this->conditions) || !is_array($this->conditions)) {
            $this->addError("conditions", "condition to'g'ri formatda yuborilmagan");
            return false;
        }
        foreach ($this->conditions as $condition) {
            if (
                !isset($condition['prop_numb']) ||
                !isset($condition['prop_name']) ||
                !isset($condition['val_numb']) ||
                !isset($condition['val_name'])
            ) {
                $this->addError("conditions", "condition to'g'ri formatda yuborilmagan");
                return false;
            }
        }
        return true;
    }

    public function attributeLabels()
    {
        return [
            'year' => Yii::t('main', 'Yil'),
            'quarter' => Yii::t('main', 'Chorak'),
            'title' => Yii::t('main', 'Reja-jadval nomi'),
            'classifiers' => Yii::t('main', 'Mahsulot'),
        ];
    }


    public function getResult()
    {
        $user = Yii::$app->user->identity;
        if (!$user->isBudget) {
            $this->addError("error", t("Ruxsat mavjud emas"));
            return false;
        }

        //$model validation to updating
        $transaction = Yii::$app->db->beginTransaction();

        $model = $this->model;
        $model->title = $this->title;
        $model->year = $this->year;
        $model->quarter = $this->quarter;

        if (!$model->save()) {
            $this->addErrors($model->errors);
            $transaction->rollBack();
            return false;
        }

        $product = PlanScheduleClassifierResource::find()->notDeleted()
            ->andWhere(['plan_schedule_id' => $model->id, 'id' => $this->planScheduleClassifierId])->one();
        if (!$product) {
            $this->addError("classifiers", $model->id);
            $transaction->rollBack();
            return false;
        }

        $classfier = Classifier::findOne($this->classifierId);
        if (!$classfier) {
            $this->addError("classifiers", t("Bu mahsulot topilmadi"));
            $transaction->rollBack();
            return false;
        }

        $account = CompanyBankAccount::findOne(['id' => $this->accountId, 'company_id' => $user->company_id, 'organ' => $user->organ]);
        if (!$account) {
            $this->addError("account", t("Xisob raqam topilmadi"));
            $transaction->rollBack();
            return false;
        }

        $product->classifier_id = $this->classifierId;
        $product->description = $this->description;
        $product->year = $model->year;
        $product->month = $this->month;
        $product->count = $this->count;
        $product->count_live = $this->count;
        $product->unit_id = $this->unitId;
        $product->kls = $account->account;
        $product->tovar = $classfier->code;
        $product->tovarname = $classfier->title_uzk;
        $product->expense = $this->expense;
        $product->tovarprice = $this->price * 100;
        $product->summa = $this->price * 100;
        $product->srok = $this->srok;
        $product->address = $this->address;
        $product->company_bank_account_id = $account->id;

        $model->total_product_count = $product->count;

        if (!$product->save()) {
            $this->addError('classifiers', $product->errors);
            $transaction->rollBack();
            return false;
        }

        foreach ($this->conditions as $condition) {
            $item = new PlanSchedulePropertiesResource([
                'plan_schedule_id' => $model->id,
                'classifier_id' => $this->classifierId,
                'plan_schedule_classifier_id' => $product->id,
                'prop_numb' => $condition['prop_numb'],
                'prop_name' => $condition['prop_name'],
                'val_numb' => $condition['val_numb'],
                'val_name' => $condition['val_name'],
            ]);
            if (!$item->save()) {
                $this->addError('conditions', $item->errors);
                $transaction->rollBack();
                return false;
            }
        }


        if (!$model->save()) {
            $this->addError('classifiers', $model->errors);
            $transaction->rollBack();
            return false;
        }

        $transaction->commit();
        return true;
    }
}
