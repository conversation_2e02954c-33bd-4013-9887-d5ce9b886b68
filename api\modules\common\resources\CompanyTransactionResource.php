<?php


namespace api\modules\common\resources;


use api\modules\auction\resources\AuctionResource;
use api\modules\shop\resources\OrderResource;
use api\modules\tender\resources\TenderResource;
use common\models\CompanyTransaction;

class CompanyTransactionResource extends CompanyTransaction
{
    public function fields()
    {
        return [
            'id', 'created_at' => function ($model) {
                return $model->transaction_date;
            },
            'amount' => function($model){
                return $model->amount / 100;
            },
            'type', 
            'transaction_date', 
            'description', 'typeNamePlusMinus', 'contract_id',
            'order_id' => function ($model) {
                return $model->order ? $model->order->lot_number : null;
            },
            'auction_id' => function ($model) {
                return $model->auction ? $model->auction->lot : null;
            },
            'tender_id' => function ($model) {
                return $model->tender ? $model->tender->lot : null;
            }
        ];
    }

    public function getOrder()
    {
        return $this->hasOne(OrderResource::class, ['id' => 'order_id']);
    }

    public function getAuction()
    {
        return $this->hasOne(AuctionResource::class, ['id' => 'auction_id']);
    }

    public function getTender()
    {
        return $this->hasOne(TenderResource::class, ['id' => 'tender_id']);
    }

//    public function fields()
//    {
//        return ['id', 'created_at', 'amount', 'type', 'transaction_date', 'description', 'typeNamePlusMinus'];
//    }

}