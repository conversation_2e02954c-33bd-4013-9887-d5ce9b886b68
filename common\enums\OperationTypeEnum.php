<?php

namespace common\enums;

interface OperationTypeEnum
{
    const ORGANIZATION_TYPE_CORPORATIVE = 0;
    const ORGANIZATION_TYPE_BUDGET = 1;
    const ORGANIZATION_TYPE_FOREIGN = 2;
    const VIRTUAL_ACCOUNT_CURRENCY_SUM = "000";
    const VIRTUAL_ACCOUNT_CURRENCY_USD = "840";
    const VIRTUAL_ACCOUNT_CURRENCY_EURO = "978";
    const VIRTUAL_ACCOUNT_CURRENCY_RUB = "643";
    const PRODUCT_NAME_TENDER = "tender_id";
    const PRODUCT_NAME_AUCTION = "auction_id";
    const PRODUCT_NAME_ORDER = "order_id";

    //aktiv schetlar
    const A_50111 = 50111;//Bankdagi vakillik hisob raqami
    const A_50113 = 50113;//G'aznada vositachilik yi'gimi uchun hisobraqam
    const A_50114 = 50114;//G'aznada shartnomalar to'lovi bo'yicha hisobraqam

    public const ACTIVE_ACCOUNTS = [
        self::A_50111,
        self::A_50113,
        self::A_50114,
    ];
    //aktiv schetlar end

    //passiv schetlar
    const P_B_31101 = 31101;//Byudjet tashkilotlarining erkin mablag'lari
    const P_B_31103 = 31103;//Byudjet tashkilotlariga kelib tushgan jarimalar
    const P_B_31901 = 31901;//Byudjet tashkilotlarining aniqlash jarayonidagi tranzaksiyalari
    const P_B_31202 = 31202;//Byudjet tashkilotlar blakirovka qilgan vositachilik yig'imi
    const P_B_31301 = 31301;//Byudjet tashkilotlarining shartnomalar bo'yicha blokirovka qilingan mablag'lari
    const P_B_31501 = 31501;//Byudjet tashkilotlarining vositachilik daromadlari
    const P_K_30201 = 30201;//Korporativ mijozlarning blokirovka qilingan zaklat mablag'lari
    const P_K_30202 = 30202;//Korporativ mijozlar blokirovka qilingan vositachilik yig'imi
    const P_K_30301 = 30301;//Byudjet tashkilotlarining shartnomalar bo'yicha blakirovka qilingan mablag'lari
    const P_K_30501 = 30501;//Korporativ mijozlarning vositachilik daromadlari
    const P_K_30901 = 30901;//Korporativ mijozlarning aniqlash jarayonidagi tranzaksiyalari
    const P_K_30101 = 30101;//Korporativ mijozlarning harajatlari


    public const PASSIVE_ACCOUNTS = [ // Passiv schetlar
        self::P_K_30101,
        self::P_B_31101,
        self::P_B_31103,
        self::P_K_30201,
        self::P_K_30202,
        self::P_B_31202,
        self::P_K_30301,
        self::P_B_31301,
        self::P_K_30501,
        self::P_B_31501,
        self::P_K_30901,
        self::P_B_31901,
    ];

    public const P_K_ACCOUNTS = [ // Karparativ shetlar
        self::P_K_30101,
        self::P_K_30201,
        self::P_K_30202,
        self::P_K_30301,
        self::P_K_30501,
        self::P_K_30901,
    ];

    public const P_B_ACCOUNTS = [ //Byudjet schetlar
        self::P_B_31101,
        self::P_B_31103,
        self::P_B_31202,
        self::P_B_31301,
        self::P_B_31501,
        self::P_B_31901,
    ];

    //passiv schetlar end

    const FILL_ACCOUNT = "FILL_ACCOUNT";
    const TEST_FILL_ACCOUNT = "TEST_FILL_ACCOUNT";
    const WITHDRAW_FUNDS = "WITHDRAW_FUNDS";
    const BLOCK_SALE_DEPOSIT = "BLOCK_SALE_DEPOSIT";
    const BLOCK_SALE_COMMISSION = "BLOCK_SALE_COMMISSION";
    const BLOCK_BROKER_FEE = "BLOCK_BROKER_FEE";
    const UNBLOCK_SALE_DEPOSIT = "UNBLOCK_SALE_DEPOSIT";
    const UNBLOCK_SALE_COMMISSION = "UNBLOCK_SALE_COMMISSION";
    const UNBLOCK_BROKER_FEE = "UNBLOCK_BROKER_FEE";
    const BLOCK_TRANSACTION_DEPOSIT = "BLOCK_TRANSACTION_DEPOSIT";
    const CANCEL_BLOCK_TRANSACTION_DEPOSIT = "CANCEL_BLOCK_TRANSACTION_DEPOSIT";
    const HOLD_TRANSACTION_COMMISSION = "HOLD_TRANSACTION_COMMISSION";
    const HOLD_TRANSACTION_BROKER_FEE = "HOLD_TRANSACTION_BROKER_FEE";
    const BLOCK_TRANSACTION_FULL_PAYMENT = "BLOCK_TRANSACTION_FULL_PAYMENT";
    const CANCEL_BLOCK_TRANSACTION_FULL_PAYMENT = "CANCEL_BLOCK_TRANSACTION_FULL_PAYMENT";
    const PAY_TRANSACTION_COMMISSION = "PAY_TRANSACTION_COMMISSION";
    const PAY_TRANSACTION_BROKER_FEE = "PAY_TRANSACTION_BROKER_FEE";
    const PAY_FOR_DELIVERED_GOODS = "PAY_FOR_DELIVERED_GOODS";
    const FINE_FROM_BROKER = "FINE_FROM_BROKER";
    const FINE_AS_BIRJA_REVENUE = "FINE_AS_BIRJA_REVENUE";
    const PAY_TRANSACTION_FINE = "PAY_TRANSACTION_FINE";
    const PAY_DEPOSIT_AS_FINE = "PAY_DEPOSIT_AS_FINE";
    const PAY_DEPOSIT_AS_FINE_SELLER = "PAY_DEPOSIT_AS_FINE_SELLER";
    const SELLER_OBLIGATION_TO_CREATE_DEPOSIT = "SELLER_OBLIGATION_TO_CREATE_DEPOSIT";
    const SELLER_OBLIGATION_TO_PAY_TRANSACTION_BROKER_FEE = "SELLER_OBLIGATION_TO_PAY_TRANSACTION_BROKER_FEE";
    const CANCEL_SELLER_OBLIGATION_TO_PAY_TRANSACTION_BROKER_FEE = "CANCEL_SELLER_OBLIGATION_TO_PAY_TRANSACTION_BROKER_FEE";
    const CANCEL_SELLER_OBLIGATION_TO_CREATE_DEPOSIT = "CANCEL_SELLER_OBLIGATION_TO_CREATE_DEPOSIT";
    const SELLER_TRANSACTION_FINES_ACCOUNTED = "SELLER_TRANSACTION_FINES_ACCOUNTED";
    const BLOCK_TRANSACTION_DEPOSIT_AGAIN = "BLOCK_TRANSACTION_DEPOSIT_AGAIN";
    const CANCEL_SELLER_TRANSACTION_FINES_ACCOUNTED = "CANCEL_SELLER_TRANSACTION_FINES_ACCOUNTED";
    const CLARIFY_TRANSACTION = "CLARIFY_TRANSACTION";
    const DEPOSIT_TO_PERSONAL_ACCOUNT = "DEPOSIT_TO_PERSONAL_ACCOUNT";
    const REFUND_INCORRECTLY_RECEIVED_FUNDS = "REFUND_INCORRECTLY_RECEIVED_FUNDS";
    const TRANSFER_MONEY_TO_FILIAL = "TRANSFER_MONEY_TO_FILIAL";




    const TYPES = [
        'FILL_ACCOUNT' => [
            'uz' => 'Shaxsiy hisobvaraqni toʻldirish',
            'uzk' => 'Шаҳсий ҳисобварақни тўлдириш',
            'ru' => 'Пополнение личного счета',
            'en' => 'Filling a personal account',
        ],
        'WITHDRAW_FUNDS' => [
            'uz' => 'Boʻsh pul mablagʻlarini chiqarish',
            'uzk' => 'Бўш пул маблағларини чиқариш',
            'ru' => 'Вывод свободных денежных средств',
            'en' => 'Withdrawing free funds',
        ],
        'BLOCK_SALE_DEPOSIT' => [
            'uz' => 'Savdo uchun zakalat pulini blokirovkalash',
            'uzk' => 'Савдо учун закалат пулини блокировкалаш',
            'ru' => 'Блокировка залоговых средств для торговли',
            'en' => 'Blocking deposit funds for trading',
        ],
        self::BLOCK_SALE_COMMISSION => [
            'uz' => 'Savdo uchun vositachilik yigʻimini blokirovkalash',
            'uzk' => 'Савдо учун воситачилик йиғимини блокировкалаш',
            'ru' => 'Блокировка комиссии за посредничество для торговли',
            'en' => 'Blocking commission fee for trading',
        ],
        'BLOCK_BROKER_FEE' => [
            'uz' => 'Brokerlik xizmat haqini blokirovkalash',
            'uzk' => 'Брокерлик хизмат ҳақини блокировкалаш',
            'ru' => 'Блокировка платы за брокерские услуги',
            'en' => 'Blocking broker service fee',
        ],
        'UNBLOCK_SALE_DEPOSIT' => [
            'uz' => 'Savdo uchun zakalat pulini blokirovkadan chiqarish',
            'uzk' => 'Савдо учун закалат пулини блокировкадан чиқариш',
            'ru' => 'Разблокировка залоговых средств для торговли',
            'en' => 'Unblocking deposit funds for trading',
        ],
        'UNBLOCK_SALE_COMMISSION' => [
            'uz' => 'Savdo uchun vositachilik yigʻimini blokirovkadan chiqarish',
            'uzk' => 'Савдо учун воситачилик йиғимини блокировкадан чиқариш',
            'ru' => 'Разблокировка комиссии за посредничество для торговли',
            'en' => 'Unblocking commission fee for trading',
        ],
        'UNBLOCK_BROKER_FEE' => [
            'uz' => 'Brokerlik xizmat haqini blokirovkadan chiqarish',
            'uzk' => 'Брокерлик хизмат ҳақини блокировкадан чиқариш',
            'ru' => 'Разблокировка платы за брокерские услуги',
            'en' => 'Unblocking broker service fee',
        ],
        'BLOCK_TRANSACTION_DEPOSIT' => [
            'uz' => 'Bitim bo’yicha zakalat pulini blokirovkalash',
            'uzk' => 'Битим бўйича закалат пулини блокировкалаш',
            'ru' => 'Блокировка залоговых средств по сделке',
            'en' => 'Blocking deposit funds for a transaction',
        ],
        'CANCEL_BLOCK_TRANSACTION_DEPOSIT' => [
            'uz' => 'Bitim bo’yicha zakalat pulini blokirovkadan chiqarish',
            'uzk' => 'Битим бўйича закалат пулни блокировкадан чиқариш',
            'ru' => 'Разблокировка залоговых средств по сделке',
            'en' => 'Unblocking deposit funds for a transaction',
        ],
        'HOLD_TRANSACTION_COMMISSION' => [
            'uz' => 'Bitim bo’yicha vositachilik yigʻimini ushlab qolish',
            'uzk' => 'Битим бўйича воситачилик йиғимини ушлаб қолиш',
            'ru' => 'Удержание комиссии за посредничество по сделке',
            'en' => 'Holding commission fee for a transaction',
        ],
        'HOLD_TRANSACTION_BROKER_FEE' => [
            'uz' => 'Bitim bo’yicha brokerlik xizmat haqini ushlab qolish',
            'uzk' => 'Битим бўйича брокерлик хизмат ҳақини ушлаб қолиш',
            'ru' => 'Удержание платы за брокерские услуги по сделке',
            'en' => 'Holding broker service fee for a transaction',
        ],
        'BLOCK_TRANSACTION_FULL_PAYMENT' => [
            'uz' => 'Bitim toʻliq toʻlovini blokirovkalash',
            'uzk' => 'Битим тўловини тўлиқ блокировкалаш',
            'ru' => 'Блокировка полной оплаты по сделке',
            'en' => 'Blocking full payment for a transaction',
        ],
        'CANCEL_BLOCK_TRANSACTION_FULL_PAYMENT' => [
            'uz' => 'Bitim toʻliq toʻlovini blokirovkadan chiqarish',
            'uzk' => 'Битим тўлиқ тўловини блокировкадан чиқариш',
            'ru' => 'Разблокировка полной оплаты по сделке',
            'en' => 'Unblocking full payment for a transaction',
        ],
        'PAY_TRANSACTION_COMMISSION' => [
            'uz' => 'Bitim bo’yicha vositachilik yigʻimini toʻlash',
            'uzk' => 'Битим бўйича воситачилик йиғимини тўлаш',
            'ru' => 'Оплата комиссии за посредничество по сделке',
            'en' => 'Paying commission fee for a transaction',
        ],
        'PAY_TRANSACTION_BROKER_FEE' => [
            'uz' => 'Bitim bo’yicha brokerlik xizmat haqini toʻlash',
            'uzk' => 'Битим бўйича брокерлик хизмат ҳақини тўлаш',
            'ru' => 'Оплата платы за брокерские услуги по сделке',
            'en' => 'Paying broker service fee for a transaction',
        ],
        'PAY_FOR_DELIVERED_GOODS' => [
            'uz' => 'Bitim boʻyicha haqiqatda yetkazilgan tovar uchun to’lov',
            'uzk' => 'Битим бўйича ҳақиқатда етказилган товар учун тўлов',
            'ru' => 'Оплата за фактически поставленный товар по сделке',
            'en' => 'Payment for actually delivered goods in a transaction',
        ],
        'FINE_FROM_BROKER' => [
            'uz' => 'Brokerdan jarimani undirish',
            'uzk' => 'Брокердан жаримани ундириш',
            'ru' => 'Взыскание штрафа с брокера',
            'en' => 'Collecting a fine from the broker',
        ],
        'FINE_AS_BIRJA_REVENUE' => [
            'uz' => 'Jarimalarni birja daromadiga olish',
            'uzk' => 'Жарималарни биржа даромадига олиш',
            'ru' => 'Получение штрафов в доход биржи',
            'en' => 'Collecting fines as exchange revenue',
        ],
        'PAY_TRANSACTION_FINE' => [
            'uz' => 'Bitim boʻyicha jarimani undirish',
            'uzk' => 'Битим бўйича жаримани ундириш',
            'ru' => 'Взыскание штрафа по сделке',
            'en' => 'Collecting a fine for a transaction',
        ],
        'PAY_DEPOSIT_AS_FINE' => [
            'uz' => 'Bitimni yopish uchun Xaridor tomonidan depozit miqdorini jarima sifatida toʻlash',
            'uzk' => 'Битимни ёпиш учун закалат суммаси Харидорнинг жарима сифатида тўлаш',
            'ru' => 'Оплата залоговой суммы покупателем в качестве штрафа для закрытия сделки',
            'en' => 'Paying the deposit amount as a fine by the buyer to close the transaction',
        ],
        'PAY_DEPOSIT_AS_FINE_SELLER' => [
            'uz' => 'Bitimni yopish uchun Sotuvchi tomonidan depozit miqdorini jarima sifatida toʻlash',
            'uzk' => 'Битимни ёпиш учун закалат суммаси Сотувчининг жарима сифатида тўлаш',
            'ru' => 'Оплата залоговой суммы продавцом в качестве штрафа для закрытия сделки',
            'en' => 'Paying the deposit amount as a fine by the seller to close the transaction',
        ],
        'SELLER_OBLIGATION_TO_CREATE_DEPOSIT' => [
            'uz' => 'Zakalat pulini shakllantirish bo’yicha Sotuvchining majburiyati',
            'uzk' => 'Закалат пулини шакллантириш бўйича Сотувчининг мажбурияти',
            'ru' => 'Обязательство продавца по формированию залоговых средств',
            'en' => 'Seller’s obligation to create deposit funds',
        ],
        'SELLER_OBLIGATION_TO_PAY_TRANSACTION_BROKER_FEE' => [
            'uz' => 'Bitim bo\'yicha brokerlik xizmat pulini shakllantirish bo’yicha Sotuvchining majburiyati',
            'uzk' => 'Битим бўйича брокерлик хизмат пулини шакллантириш бўйича Сотувчининг мажбурияти',
            'ru' => 'Обязательство продавца по формированию платы за брокерские услуги по сделке',
            'en' => 'Seller’s obligation to pay broker service fee for a transaction',
        ],
        'CANCEL_SELLER_OBLIGATION_TO_PAY_TRANSACTION_BROKER_FEE' => [
            'uz' => 'Bitim bo\'yicha brokerlik xizmat pulini shakllantirish bo’yicha Sotuvchining majburiyati bekor qilindi',
            'uzk' => 'Битим бўйича брокерлик хизмат пулини шакллантириш бўйича Сотувчининг мажбурияти бекор қилинди',
            'ru' => 'Отмена обязательства продавца по формированию платы за брокерские услуги по сделке',
            'en' => 'Cancellation of seller’s obligation to pay broker service fee for a transaction',
        ],
        'CANCEL_SELLER_OBLIGATION_TO_CREATE_DEPOSIT' => [
            'uz' => 'Zakalat pulini shakllantirish bo’yicha Sotuvchining majburiyati bekor qilindi',
            'uzk' => 'Закалат пулини шакллантириш бўйича Сотувчининг мажбурияти бекор қилинди',
            'ru' => 'Отмена обязательства продавца по формированию залоговых средств',
            'en' => 'Cancellation of seller’s obligation to create deposit funds',
        ],
        'SELLER_TRANSACTION_FINES_ACCOUNTED' => [
            'uz' => 'Sotuvchining bitim bo’yicha jarimalar hisobga olindi',
            'uzk' => 'Сотувчининг битим бўйича жарима ҳисобга олинди',
            'ru' => 'Учет штрафов продавца по сделке',
            'en' => 'Accounting for seller’s fines in a transaction',
        ],
        'BLOCK_TRANSACTION_DEPOSIT_AGAIN' => [
            'uz' => 'Bitim bo’yicha zakalat pulini blokirovkalash',
            'uzk' => 'Битим бўйича закалат пулини блокировкалаш',
            'ru' => 'Блокировка залоговых средств по сделке',
            'en' => 'Blocking deposit funds for a transaction',
        ],
        'CANCEL_SELLER_TRANSACTION_FINES_ACCOUNTED' => [
            'uz' => 'Bitim bo’yicha Sotuvchining jarimalari hisobi bekor qilindi',
            'uzk' => 'Битим бўйича Сотувчининг жарима ҳисоби бекор қилинди',
            'ru' => 'Отмена учета штрафов продавца по сделке',
            'en' => 'Cancellation of accounting for seller’s fines in a transaction',
        ],
        'CLARIFY_TRANSACTION' => [
            'uz' => 'Tranzaksiyani aniqlashtirish uchun kirim qilish',
            'uzk' => 'Транзакцияни аниқлаштириш учун кирим қилиш',
            'ru' => 'Внесение для уточнения транзакции',
            'en' => 'Deposit to clarify a transaction',
        ],
        'DEPOSIT_TO_PERSONAL_ACCOUNT' => [
            'uz' => 'Mablag’larni tegishli shaxsiy hisobvaraqqa kirim qilish',
            'uzk' => 'Маблағларни тегишли шаҳсий ҳисобвараққа кирим қилиш',
            'ru' => 'Внесение средств на соответствующий личный счет',
            'en' => 'Deposit to the respective personal account',
        ],
        'REFUND_INCORRECTLY_RECEIVED_FUNDS' => [
            'uz' => 'Noto’g’ri tushgan mablag’ orqaga qaytarildi',
            'uzk' => 'Нотўғри тушган маблағ орқага қайтарилди',
            'ru' => 'Возврат ошибочно полученных средств',
            'en' => 'Refund of incorrectly received funds',
        ],
        'TRANSFER_MONEY_TO_FILIAL' => [
            'uz' => 'Filialga pul o\'tkazmasi',
            'uzk' => 'Filialga pul o\'tkazmasi',
            'ru' => 'Перевод денег в филиал',
            'en' => 'Transfer of money to a branch',
        ],
    ];
}

