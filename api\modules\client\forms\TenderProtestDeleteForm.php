<?php


namespace api\modules\client\forms;


use api\components\BaseRequest;
use api\modules\client\resources\TenderDiscussionResource;
use api\modules\client\resources\TenderResource;
use common\enums\TenderEnum;

class TenderProtestDeleteForm extends BaseRequest
{
    public TenderResource $tender;

    public function __construct(TenderResource $model, $params = [])
    {
        $this->tender = $model;
        parent::__construct($params);
    }


    public function getResult()
    {
        date_default_timezone_set("Asia/Tashkent");

        try {
            $model = TenderDiscussionResource::find()->where(['tender_id' => $this->tender->id, 'status' => TenderEnum::STATUS_ACTIVE, 'company_id' => \Yii::$app->user->identity->company_id])->one();
            if(!$model){
                $this->addError("id", t("Siz avval etiroz yubormagansiz"));
                return false;
            }
            $model->status = TenderEnum::STATUS_DELETED;
            $model->updated_at =  \Yii::$app->user->id;
            $model->deletet_at = date("Y-m-d H:i:s");
            if(!$model->save()){
                $this->addErrors($model->errors);
                return false;
            }
//            return $model->delete();

            return true;

        } catch (\Exception $e){
            $this->addErrors($e->getMessage());
            return false;
        }


    }
}