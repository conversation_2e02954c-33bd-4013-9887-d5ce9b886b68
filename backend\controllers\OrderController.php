<?php

namespace backend\controllers;

use common\enums\ShopEnum;
use Yii;
use common\models\shop\Order;
use common\models\shop\OrderSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * OrderController implements the CRUD actions for Order model.
 */
class OrderController extends BackendController
{
    /**
     * Lists all Order models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new OrderSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    public function actionDmbat()
    {
        $searchModel = new OrderSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, ShopEnum::ORDER_STATUS_WAITING);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }
    public function actionAcceptDmbat()
    {
        $searchModel = new OrderSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, ShopEnum::ORDER_STATUS_ACTIVE);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }
    public function actionRejectDmbat()
    {
        $searchModel = new OrderSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams, ShopEnum::ORDER_STATUS_REJECT_DMBAT);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }
    /**
     * Lists all Product models.
     * @return mixed
     */
    public function actionModeratorAccept()
    {
        $model = $this->findModel(Yii::$app->request->post()['id']);
        $model->updateAttributes([
            'request_end' => date("Y-m-d H:i:s", strtotime("+30 minutes", time())),
            'status' => ShopEnum::ORDER_STATUS_ACTIVE,
        ]);
        \Yii::$app->session->setFlash("success", "Успешно moderating");
        return $this->render('view', [
            'model' => $model,
        ]);
    }
    public function actionModeratorReject()
    {
        $model = $this->findModel(Yii::$app->request->post()['id']);
        $model->updateAttributes([
            'request_end' => null,
            'status' => ShopEnum::ORDER_STATUS_REJECT_DMBAT,
            'cancel_reason' => Yii::$app->request->post()['description'],
            'cancel_date' => date("Y-m-d H:i:s"),
        ]);
        \Yii::$app->session->setFlash("success", "Успешно no moderating");
        return $this->render('view', [
            'model' => $model,
        ]);
    }
    /**
     * Displays a single Order model.
     * @param int $id ID
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }
    /**
     * Finds the Order model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id ID
     * @return Order the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Order::findOne($id)) !== null) {
            return $model;
        }
        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
