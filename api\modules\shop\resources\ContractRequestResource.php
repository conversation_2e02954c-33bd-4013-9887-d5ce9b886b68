<?php

namespace api\modules\shop\resources;

use api\modules\common\resources\CompanyShortResource;
use common\enums\ContractEnum;
use common\models\ContractRequest;
use common\traits\FindOrFail;

class ContractRequestResource extends ContractRequest
{
    use FindOrFail;
    public function fields()
    {
        return [
            'id', 'status', 'created_at', 'updated_at', 'stateName'
        ];
    }

    public function extraFields()
    {
        return [
            'contract',
            'order',
            'company',
        ];
    }

    public function getStateName(){
        $arr = [
            ContractEnum::STATUS_REQUEST_NEW => t("Yangi"),
            ContractEnum::STATUS_REQUEST_DONE => t("Tasdiqlangan"),
            ContractEnum::STATUS_REQUEST_CANCELED => t("Bekor qilingan"),
        ];
        return $arr[$this->status];
    }

    public function getCompany()
    {
        return $this->hasOne(CompanyShortResource::class, ['id' => 'company_id']);
    }

    /**
     * Gets query for [[Contract]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getContract()
    {
        return $this->hasOne(ContractResource::class, ['id' => 'contract_id']);
    }

    /**
     * Gets query for [[Order]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getOrder()
    {
        return $this->hasOne(OrderResource::class, ['id' => 'order_id']);
    }
}
