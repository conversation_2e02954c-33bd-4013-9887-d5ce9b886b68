<?php

namespace api\modules\common\filters;

use api\components\BaseRequest;
use api\modules\common\resources\VirtualTransactionResource;
use common\widgets\ExcelExportWidget;
use PhpOffice\PhpSpreadsheet\Writer\Exception;
use yii\data\ActiveDataProvider;

class TransactionImportFilter extends BaseRequest
{
    public $tin;
    public $from_date;
    public $end_date;
    public $order_id;
    public $auction_id;
    public $tender_id;
    public $contract_id;
    public $limit;

    public function rules(): array
    {
        return [
            [
                ['tin', 'from_date', 'end_date', 'order_id', 'auction_id','tender_id','contract_id','limit'], 'safe'
            ],
            [['from_date','end_date'], 'date', 'format' => 'php:Y-m-d'],
            ['limit', 'integer', 'min' => 1, 'max' => 500],
        ];
    }

    /**
     * @throws Exception
     */
    public function getResult(): string
    {
        $query = VirtualTransactionResource::find()
            ->andFilterWhere(['or', ['debit_company_tin' => $this->tin], ['credit_company_tin' => $this->tin]])
            ->andFilterWhere(['<=', 'created_at', $this->from_date])
            ->andFilterWhere(['>=', 'created_at', $this->end_date])
            ->andFilterWhere(['order_id' => $this->order_id])
            ->andFilterWhere(['auction_id' => $this->auction_id])
            ->andFilterWhere(['tender_id' => $this->tender_id])
            ->andFilterWhere(['contract_id' => $this->contract_id]);

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'pageSize' => $this->getLimit(),
            ],
            'sort' => [
                'defaultOrder' => [
                    'created_at' => SORT_DESC,
                ]
            ],
        ]);

        return ExcelExportWidget::widget([
            'dataProvider' => $dataProvider,
            'fields' => [
                'id',
                'credit_company_id',
                'debit_company_id',
                'debit',
                'credit',
                'debit_company_tin',
                'credit_company_tin',
                'debit_account',
                'credit_account',
                'operation_type',
                'description',
                'debit_id',
                'parent_id',
                'created_at',
                'updated_at',
            ]
        ]);
    }

    private function getLimit(): int
    {
        return $this->limit ?: 100;
    }
}